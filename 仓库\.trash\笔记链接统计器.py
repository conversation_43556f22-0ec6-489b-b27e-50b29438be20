#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
笔记链接统计器
统计当前笔记系统中的链接分布情况
"""

import os
import re
from pathlib import Path
from datetime import datetime

def extract_links(content):
    """提取现有链接"""
    return re.findall(r'\[\[([^\]]+)\]\]', content)

def analyze_all_notes(base_dir):
    """分析所有笔记的链接情况"""
    notes_stats = []
    exclude_dirs = ['默写本', '.obsidian', '.trash', '原子笔记', '知识卡片', 'template']
    
    for file_path in base_dir.rglob("*.md"):
        if any(ex in str(file_path).lower() for ex in exclude_dirs):
            continue
            
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
        except:
            continue
        
        title = file_path.stem
        links = extract_links(content)
        
        notes_stats.append({
            'title': title,
            'path': str(file_path),
            'link_count': len(links),
            'links': links,
            'word_count': len(content)
        })
    
    return notes_stats

def create_detailed_report(notes_stats, base_dir):
    """创建详细的链接分析报告"""
    report_path = base_dir / "详细链接分析报告.md"
    
    # 按链接数分类
    link_distribution = {
        '0个链接': [],
        '1个链接': [],
        '2个链接': [],
        '3-5个链接': [],
        '6-10个链接': [],
        '10+个链接': []
    }
    
    for note in notes_stats:
        count = note['link_count']
        if count == 0:
            link_distribution['0个链接'].append(note)
        elif count == 1:
            link_distribution['1个链接'].append(note)
        elif count == 2:
            link_distribution['2个链接'].append(note)
        elif count <= 5:
            link_distribution['3-5个链接'].append(note)
        elif count <= 10:
            link_distribution['6-10个链接'].append(note)
        else:
            link_distribution['10+个链接'].append(note)
    
    # 生成报告
    report_content = f"""# 详细链接分析报告

生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
总笔记数: {len(notes_stats)}

## 链接分布统计

"""
    
    total_isolated = 0
    for category, notes in link_distribution.items():
        count = len(notes)
        percentage = count / len(notes_stats) * 100
        report_content += f"- **{category}**: {count}个 ({percentage:.1f}%)\n"
        
        # 统计孤立笔记（0-2个链接）
        if category in ['0个链接', '1个链接', '2个链接']:
            total_isolated += count
    
    isolated_percentage = total_isolated / len(notes_stats) * 100
    report_content += f"\n**孤立笔记总计（0-2个链接）**: {total_isolated}个 ({isolated_percentage:.1f}%)\n"
    
    # 详细列出孤立笔记
    report_content += f"\n## 孤立笔记详情\n\n"
    
    for category in ['0个链接', '1个链接', '2个链接']:
        notes = link_distribution[category]
        if notes:
            report_content += f"### {category} ({len(notes)}个)\n\n"
            for note in notes[:50]:  # 只显示前50个
                report_content += f"- {note['title']}\n"
            if len(notes) > 50:
                report_content += f"- ... 还有 {len(notes) - 50} 个笔记\n"
            report_content += "\n"
    
    # 最受欢迎的笔记（被链接最多）
    all_links = []
    for note in notes_stats:
        all_links.extend(note['links'])
    
    link_count = {}
    for link in all_links:
        link_count[link] = link_count.get(link, 0) + 1
    
    popular_notes = sorted(link_count.items(), key=lambda x: x[1], reverse=True)[:20]
    
    report_content += f"## 最受欢迎的笔记（被链接次数）\n\n"
    for note_title, count in popular_notes:
        report_content += f"- **{note_title}**: 被链接 {count} 次\n"
    
    with open(report_path, 'w', encoding='utf-8') as f:
        f.write(report_content)
    
    return report_path, link_distribution

def main():
    """主函数"""
    base_dir = Path(r"c:\Users\<USER>\OneDrive\obsidian笔记系统\战略与战术")
    
    print("笔记链接统计器")
    print("=" * 30)
    
    # 分析所有笔记
    print("分析笔记链接情况...")
    notes_stats = analyze_all_notes(base_dir)
    print(f"分析了 {len(notes_stats)} 个笔记")
    
    # 创建详细报告
    print("生成详细报告...")
    report_path, link_distribution = create_detailed_report(notes_stats, base_dir)
    
    # 显示统计结果
    print(f"\n📊 链接分布统计:")
    total_isolated = 0
    for category, notes in link_distribution.items():
        count = len(notes)
        percentage = count / len(notes_stats) * 100
        print(f"  {category}: {count}个 ({percentage:.1f}%)")
        
        if category in ['0个链接', '1个链接', '2个链接']:
            total_isolated += count
    
    isolated_percentage = total_isolated / len(notes_stats) * 100
    print(f"\n🔍 孤立笔记总计（0-2个链接）: {total_isolated}个 ({isolated_percentage:.1f}%)")
    
    print(f"\n📄 详细报告已保存: {report_path}")
    
    # 建议
    if total_isolated > len(notes_stats) * 0.3:  # 超过30%
        print(f"\n💡 建议:")
        print(f"  - 孤立笔记比例较高，建议继续运行链接增强脚本")
        print(f"  - 可以考虑手动为重要笔记添加链接")
        print(f"  - 检查笔记内容是否需要更多关键词")

if __name__ == "__main__":
    main()
