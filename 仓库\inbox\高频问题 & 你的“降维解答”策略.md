# 高频问题 & 你的“降维解答”策略
#### **2. 高频问题 & 你的“降维解答”策略**

|**提问者**|**典型问题**|**技术难点**|**你的“翻译”回答**（附技巧）|
|---|---|---|---|
|**法务**|“用户隐私协议怎么写才合规？”|法律条款如何匹配实际业务场景|**“盯死3点：**  <br>1. 明确告知数据用途（如‘用手机号发货’而非模糊的‘提供服务’）；  <br>2. 给用户拒绝权（如‘不同意收集地理位置时，仍能使用核心功能’）；  <br>3. 留证据（如记录用户勾选‘同意’的时间戳）。”|
|**产品经理**|“我们要做用户画像，怎么避免违规？”|匿名化 vs. 去标识化的技术差异|**“分两步走：**  <br>1. 先用‘去标识化’（如替换手机号为ID），确保数据可逆；  <br>2. 若需长期分析，升级为‘匿名化’（如聚合数据，无法关联到个人）。  <br>**注意**：匿名化数据才算真正豁免隐私条款！”|
|**IT 部门**|“数据库怎么存用户信息才安全？”|加密、访问控制的技术实现细节|**“优先做3件事：**  <br>1. 敏感数据（如身份证号）加密存储（用AES-256）；  <br>2. 设最小权限（如客服只能看订单，不能看身份证）；  <br>3. 日志留痕（谁在什么时候查了数据）。”|# 未命名
