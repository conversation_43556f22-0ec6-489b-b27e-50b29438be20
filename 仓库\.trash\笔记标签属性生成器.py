#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
笔记标签属性生成器
自动为笔记生成标签和YAML前置属性
"""

import os
import re
from pathlib import Path
from datetime import datetime
import hashlib

class NoteTagPropertyGenerator:
    def __init__(self, base_dir):
        self.base_dir = Path(base_dir)
        self.processed_notes = []
        
    def get_keyword_categories(self):
        """关键词分类库"""
        return {
            '股权融资': {
                'keywords': ['股权', '股份', '股东', '控制权', '投票权', '分红权', '优先股', '普通股', 'AB股', 
                           '期权池', '融资', '投资', '估值', '对赌协议', '清算优先权', '领售权', '反稀释', '一票否决权'],
                'tags': ['股权', '融资', '投资']
            },
            '法律合规': {
                'keywords': ['合同', '协议', '法律风险', '合规', '知识产权', '竞业禁止', '保密协议', '劳动合同',
                           '法律', '律师', '诉讼', '仲裁', '违约', '责任', '义务', '权利'],
                'tags': ['法律', '合规', '风险']
            },
            '财务管理': {
                'keywords': ['财务报表', '现金流', '利润', '成本', '税务', '审计', '会计', '预算', '资产负债',
                           '收入', '支出', '财务', '资金', '资本', '负债', '资产'],
                'tags': ['财务', '会计', '资金']
            },
            '战略管理': {
                'keywords': ['战略', '策略', '商业模式', '竞争', '市场', '客户', '产品', '运营', '管理',
                           '规划', '目标', '愿景', '使命', '价值观', '文化'],
                'tags': ['战略', '管理', '商业']
            },
            '组织人力': {
                'keywords': ['领导力', '团队', '人才', '招聘', '培训', '绩效', '激励', '文化', '组织',
                           '员工', '人力资源', '管理', '沟通', '协作', '领导'],
                'tags': ['团队', '人力', '领导']
            },
            '风险控制': {
                'keywords': ['风险', '危机', '应急', '预警', '防范', '控制', '监控', '评估', '识别',
                           '安全', '保险', '担保', '抵押', '质押'],
                'tags': ['风险', '安全', '控制']
            },
            '市场营销': {
                'keywords': ['营销', '推广', '品牌', '广告', '客户', '用户', '市场', '销售', '渠道',
                           '定位', '传播', '公关', '媒体', '社交'],
                'tags': ['营销', '品牌', '市场']
            },
            '技术创新': {
                'keywords': ['技术', '创新', '研发', '产品', '开发', '设计', '专利', '知识产权',
                           '数字化', '智能', '自动化', '科技', 'AI', '互联网'],
                'tags': ['技术', '创新', '产品']
            }
        }
    
    def analyze_content(self, content, title):
        """分析内容，提取关键信息"""
        full_text = f"{title} {content}"
        
        # 提取关键词和分类
        categories = self.get_keyword_categories()
        found_categories = []
        found_keywords = []
        
        for category, data in categories.items():
            category_score = 0
            category_keywords = []
            
            for keyword in data['keywords']:
                if keyword in full_text:
                    category_score += 1
                    category_keywords.append(keyword)
                    if keyword not in found_keywords:
                        found_keywords.append(keyword)
            
            if category_score >= 2:  # 至少匹配2个关键词才算该分类
                found_categories.append({
                    'category': category,
                    'score': category_score,
                    'keywords': category_keywords,
                    'tags': data['tags']
                })
        
        # 按分数排序
        found_categories.sort(key=lambda x: x['score'], reverse=True)
        
        return found_categories, found_keywords
    
    def determine_note_type(self, content, title):
        """判断笔记类型"""
        if any(word in content for word in ['案例', '实例', '例子', '故事', '经历']):
            return '案例分析'
        elif any(word in content for word in ['方法', '技巧', '策略', '操作', '步骤']):
            return '方法论'
        elif any(word in content for word in ['思考', '反思', '总结', '心得', '感悟']):
            return '思考总结'
        elif any(word in content for word in ['法则', '原则', '规律', '定律', '理论']):
            return '理论法则'
        elif any(word in content for word in ['风险', '陷阱', '坑', '警告', '注意']):
            return '风险警示'
        elif any(word in content for word in ['模板', '范本', '格式', '清单', '表格']):
            return '工具模板'
        else:
            return '知识笔记'
    
    def calculate_priority(self, content, categories):
        """计算笔记优先级"""
        # 基于内容长度和关键词密度
        content_length = len(content)
        keyword_count = sum(cat['score'] for cat in categories)
        
        if content_length > 2000 and keyword_count > 10:
            return '高'
        elif content_length > 1000 and keyword_count > 5:
            return '中'
        else:
            return '低'
    
    def estimate_reading_time(self, content):
        """估算阅读时间（分钟）"""
        # 按中文阅读速度 300字/分钟计算
        char_count = len(content)
        minutes = max(1, round(char_count / 300))
        return minutes
    
    def generate_properties(self, file_path, content):
        """生成笔记属性"""
        title = file_path.stem
        
        # 分析内容
        categories, keywords = self.analyze_content(content, title)
        
        # 生成标签
        tags = []
        for cat in categories[:3]:  # 最多取前3个分类
            tags.extend(cat['tags'])
        tags = list(set(tags))  # 去重
        
        # 确定笔记类型
        note_type = self.determine_note_type(content, title)
        
        # 计算优先级
        priority = self.calculate_priority(content, categories)
        
        # 估算阅读时间
        reading_time = self.estimate_reading_time(content)
        
        # 生成属性
        properties = {
            'title': title,
            'created': datetime.now().strftime('%Y-%m-%d'),
            'modified': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
            'type': note_type,
            'tags': tags,
            'keywords': keywords[:10],  # 最多10个关键词
            'categories': [cat['category'] for cat in categories[:3]],
            'priority': priority,
            'reading_time': f"{reading_time}分钟",
            'word_count': len(content),
            'status': '已完成'
        }
        
        return properties
    
    def has_yaml_frontmatter(self, content):
        """检查是否已有YAML前置属性"""
        return content.strip().startswith('---')
    
    def extract_existing_yaml(self, content):
        """提取现有的YAML属性"""
        if not self.has_yaml_frontmatter(content):
            return {}, content
            
        lines = content.split('\n')
        yaml_lines = []
        content_lines = []
        in_yaml = False
        yaml_end_found = False
        
        for i, line in enumerate(lines):
            if i == 0 and line.strip() == '---':
                in_yaml = True
                continue
            elif in_yaml and line.strip() == '---':
                yaml_end_found = True
                in_yaml = False
                content_lines = lines[i+1:]
                break
            elif in_yaml:
                yaml_lines.append(line)
            else:
                content_lines.append(line)
        
        # 简单解析YAML（仅支持基本格式）
        existing_props = {}
        for line in yaml_lines:
            if ':' in line:
                key, value = line.split(':', 1)
                existing_props[key.strip()] = value.strip()
        
        return existing_props, '\n'.join(content_lines)
    
    def create_yaml_frontmatter(self, properties):
        """创建YAML前置属性"""
        yaml_content = "---\n"
        
        # 基本信息
        yaml_content += f"title: {properties['title']}\n"
        yaml_content += f"created: {properties['created']}\n"
        yaml_content += f"modified: {properties['modified']}\n"
        yaml_content += f"type: {properties['type']}\n"
        yaml_content += f"status: {properties['status']}\n"
        yaml_content += f"priority: {properties['priority']}\n"
        
        # 标签
        if properties['tags']:
            yaml_content += f"tags: [{', '.join(f'#{tag}' for tag in properties['tags'])}]\n"
        
        # 分类
        if properties['categories']:
            yaml_content += f"categories: [{', '.join(properties['categories'])}]\n"
        
        # 关键词
        if properties['keywords']:
            yaml_content += f"keywords: [{', '.join(properties['keywords'][:8])}]\n"
        
        # 统计信息
        yaml_content += f"reading_time: {properties['reading_time']}\n"
        yaml_content += f"word_count: {properties['word_count']}\n"
        
        yaml_content += "---\n\n"
        
        return yaml_content
    
    def process_note(self, file_path, update_existing=False):
        """处理单个笔记"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
        except Exception as e:
            print(f"  读取失败: {e}")
            return False
        
        # 检查是否已有属性
        existing_props, main_content = self.extract_existing_yaml(content)
        
        if existing_props and not update_existing:
            print(f"  跳过（已有属性）")
            return False
        
        # 生成新属性
        properties = self.generate_properties(file_path, main_content)
        
        # 合并现有属性（如果需要保留）
        if existing_props and update_existing:
            # 保留一些手动设置的属性
            for key in ['status', 'priority']:
                if key in existing_props:
                    properties[key] = existing_props[key]
        
        # 创建新内容
        yaml_frontmatter = self.create_yaml_frontmatter(properties)
        new_content = yaml_frontmatter + main_content
        
        # 保存文件
        try:
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(new_content)
            
            self.processed_notes.append({
                'file': file_path.name,
                'tags': properties['tags'],
                'type': properties['type'],
                'priority': properties['priority']
            })
            
            return True
        except Exception as e:
            print(f"  保存失败: {e}")
            return False
    
    def create_summary_report(self):
        """创建处理总结报告"""
        report_path = self.base_dir / "笔记属性生成报告.md"
        
        report_content = f"""# 笔记属性生成报告

生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
处理笔记数量: {len(self.processed_notes)}

## 统计信息

### 按类型分布
"""
        
        # 统计类型分布
        type_stats = {}
        for note in self.processed_notes:
            note_type = note['type']
            type_stats[note_type] = type_stats.get(note_type, 0) + 1
        
        for note_type, count in sorted(type_stats.items(), key=lambda x: x[1], reverse=True):
            report_content += f"- **{note_type}**: {count}个\n"
        
        report_content += "\n### 按优先级分布\n"
        
        # 统计优先级分布
        priority_stats = {}
        for note in self.processed_notes:
            priority = note['priority']
            priority_stats[priority] = priority_stats.get(priority, 0) + 1
        
        for priority in ['高', '中', '低']:
            count = priority_stats.get(priority, 0)
            report_content += f"- **{priority}优先级**: {count}个\n"
        
        report_content += "\n## 处理详情\n\n"
        
        for note in self.processed_notes:
            tags_str = ', '.join(f'#{tag}' for tag in note['tags'])
            report_content += f"- **{note['file']}** - {note['type']} ({note['priority']}优先级) - {tags_str}\n"
        
        with open(report_path, 'w', encoding='utf-8') as f:
            f.write(report_content)
        
        return report_path
    
    def run(self, update_existing=False, exclude_dirs=None):
        """运行属性生成器"""
        if exclude_dirs is None:
            exclude_dirs = ['默写本', '.obsidian', '.trash', '原子笔记', '知识卡片']
        
        print(f"笔记标签属性生成器")
        print(f"处理目录: {self.base_dir}")
        print(f"更新现有属性: {'是' if update_existing else '否'}")
        print("=" * 50)
        
        # 收集所有笔记文件
        md_files = []
        for file_path in self.base_dir.rglob("*.md"):
            # 排除指定目录
            if any(exclude_dir in str(file_path) for exclude_dir in exclude_dirs):
                continue
            md_files.append(file_path)
        
        print(f"找到 {len(md_files)} 个笔记文件\n")
        
        # 处理每个文件
        success_count = 0
        for file_path in md_files:
            print(f"处理: {file_path.name}")
            if self.process_note(file_path, update_existing):
                success_count += 1
                print(f"  ✓ 已添加属性")
            else:
                print(f"  - 跳过")
        
        # 创建报告
        if self.processed_notes:
            report_path = self.create_summary_report()
            print(f"\n📊 处理报告: {report_path}")
        
        print(f"\n✅ 完成！")
        print(f"📝 成功处理: {success_count} 个笔记")
        print(f"📋 生成属性: {len(self.processed_notes)} 个笔记")

if __name__ == "__main__":
    # 配置基础目录
    base_directory = Path(r"c:\Users\<USER>\OneDrive\obsidian笔记系统\战略与战术")
    
    print("笔记标签属性生成器")
    print("=" * 50)
    print("选择处理模式:")
    print("1. 仅为没有属性的笔记添加属性（推荐）")
    print("2. 更新所有笔记的属性（包括已有属性的）")
    
    choice = input("请选择 (1/2): ").strip()
    update_existing = (choice == "2")
    
    if update_existing:
        confirm = input("确认要更新所有笔记的属性吗？(y/N): ").strip().lower()
        update_existing = (confirm == 'y')
    
    # 运行生成器
    generator = NoteTagPropertyGenerator(base_directory)
    generator.run(update_existing=update_existing)
