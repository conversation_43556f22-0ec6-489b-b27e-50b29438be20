---
title: 脱敏Markdown生成
tags: [数据合规, 脱敏处理, Markdown, 数据保护]
属性:
  工作流顺序: 3
  交付物: 
    - 脱敏后的Markdown文件
    - 脱敏处理报告
    - 敏感信息映射表（加密存储）
  合规要点: 
    - 个人信息自动识别与替换
    - 敏感数据分类处理
    - 脱敏质量验证
    - 可逆性控制管理
---

# 脱敏Markdown生成

## 工作流描述
对MinerU转换后的Markdown文件进行敏感信息脱敏处理，确保输出文件符合数据保护要求。

## 脱敏策略
### 个人信息处理
- **姓名**: 替换为 `[姓名_001]`, `[姓名_002]`
- **身份证号**: 替换为 `[身份证_***]`
- **电话号码**: 替换为 `[电话_***]`
- **地址**: 替换为 `[地址_***]`
- **邮箱**: 替换为 `[邮箱_***]`

### 敏感业务信息
- **金额**: 保留范围，具体数值脱敏
- **日期**: 保留年月，日期模糊化
- **合同编号**: 替换为通用标识符
- **公司名称**: 根据敏感级别决定处理方式

## 脱敏处理脚本
```python
import re
import hashlib

class DataDesensitizer:
    def __init__(self):
        self.mapping_table = {}
    
    def desensitize_name(self, text):
        # 姓名脱敏正则
        name_pattern = r'[\u4e00-\u9fa5]{2,4}'
        return re.sub(name_pattern, self._replace_name, text)
    
    def desensitize_id_card(self, text):
        # 身份证脱敏
        id_pattern = r'\d{17}[\dXx]'
        return re.sub(id_pattern, '[身份证_***]', text)
    
    def desensitize_phone(self, text):
        # 电话脱敏
        phone_pattern = r'1[3-9]\d{9}'
        return re.sub(phone_pattern, '[电话_***]', text)
    
    def _replace_name(self, match):
        name = match.group()
        if name not in self.mapping_table:
            self.mapping_table[name] = f"[姓名_{len(self.mapping_table)+1:03d}]"
        return self.mapping_table[name]
```

## 质量验证
### 脱敏完整性检查
- [ ] 个人信息识别准确率 > 95%
- [ ] 误脱敏率 < 1%
- [ ] 文档结构完整性保持
- [ ] 表格格式正确性

### 合规性验证
- [ ] GDPR第4条个人数据定义符合性
- [ ] 数据最小化原则执行
- [ ] 脱敏可逆性控制
- [ ] 处理记录完整性

## 输出管理
- **脱敏文件**: 存储在安全目录
- **映射表**: 加密存储，严格访问控制
- **处理日志**: 记录所有脱敏操作
- **质量报告**: 脱敏效果评估结果