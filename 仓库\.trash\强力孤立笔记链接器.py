#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
强力孤立笔记链接器
更积极地为孤立笔记添加链接，降低匹配阈值，增加链接数量
"""

import os
import re
from pathlib import Path
from datetime import datetime

class PowerfulNoteLinkGenerator:
    def __init__(self, base_dir):
        self.base_dir = Path(base_dir)
        self.all_notes = {}
        self.processed_count = 0
        
    def get_extended_keywords(self):
        """扩展的关键词库"""
        return {
            '股权融资': ['股权', '股份', '股东', '控制权', '投票权', '分红权', '优先股', '普通股', 'AB股', 
                      '期权', '融资', '投资', '估值', '对赌', '清算', '领售权', '反稀释', '一票否决'],
            '法律风险': ['法律', '合规', '风险', '合同', '协议', '知识产权', '竞业禁止', '保密', 
                      '劳动合同', '律师', '诉讼', '仲裁', '违约', '责任', '义务', '权利'],
            '财务管理': ['财务', '现金流', '利润', '成本', '税务', '审计', '会计', '预算', '资产',
                      '负债', '收入', '支出', '资金', '资本', '投入', '回报'],
            '战略管理': ['战略', '策略', '商业模式', '竞争', '市场', '客户', '产品', '运营', '管理',
                      '规划', '目标', '愿景', '使命', '价值观', '文化', '发展'],
            '团队组织': ['团队', '人才', '招聘', '培训', '绩效', '激励', '组织', '员工', '人力',
                      '管理', '沟通', '协作', '领导', '领导力', '文化'],
            '市场营销': ['营销', '推广', '品牌', '广告', '客户', '用户', '市场', '销售', '渠道',
                      '定位', '传播', '公关', '媒体', '社交', '宣传'],
            '技术创新': ['技术', '创新', '研发', '产品', '开发', '设计', '专利', '数字化',
                      '智能', '自动化', '科技', 'AI', '互联网', '平台']
        }
    
    def extract_links(self, content):
        """提取现有链接"""
        return re.findall(r'\[\[([^\]]+)\]\]', content)
    
    def extract_keywords(self, text):
        """提取关键词"""
        found_keywords = []
        keyword_categories = self.get_extended_keywords()
        
        for category, keywords in keyword_categories.items():
            for keyword in keywords:
                if keyword in text:
                    found_keywords.append((keyword, category))
        
        return found_keywords
    
    def should_exclude(self, file_path):
        """判断是否排除文件"""
        path_str = str(file_path).lower()
        excludes = ['默写本', '.obsidian', '.trash', '原子笔记', '知识卡片', 'template', '模板']
        return any(ex in path_str for ex in excludes)
    
    def analyze_note(self, file_path):
        """分析笔记"""
        if self.should_exclude(file_path):
            return None
            
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
        except:
            return None
        
        title = file_path.stem
        links = self.extract_links(content)
        keywords = self.extract_keywords(f"{title} {content}")
        
        return {
            'path': file_path,
            'title': title,
            'content': content,
            'links': links,
            'link_count': len(links),
            'keywords': keywords,
            'word_count': len(content)
        }
    
    def calculate_similarity(self, note1, note2):
        """计算两个笔记的相似度"""
        score = 0
        
        # 关键词匹配
        keywords1 = set([kw[0] for kw in note1['keywords']])
        keywords2 = set([kw[0] for kw in note2['keywords']])
        keyword_overlap = len(keywords1 & keywords2)
        score += keyword_overlap * 8
        
        # 标题词匹配
        title_words1 = set([w for w in note1['title'].split() if len(w) > 2])
        title_words2 = set([w for w in note2['title'].split() if len(w) > 2])
        title_overlap = len(title_words1 & title_words2)
        score += title_overlap * 12
        
        # 标题包含关系
        for word in title_words1:
            if word in note2['title']:
                score += 6
        for word in title_words2:
            if word in note1['title']:
                score += 6
        
        # 分类匹配
        categories1 = set([kw[1] for kw in note1['keywords']])
        categories2 = set([kw[1] for kw in note2['keywords']])
        category_overlap = len(categories1 & categories2)
        score += category_overlap * 5
        
        # 内容长度相似性加分
        if abs(note1['word_count'] - note2['word_count']) < 500:
            score += 2
        
        return score
    
    def find_related_notes(self, target_note, max_results=5):
        """找到相关笔记（降低阈值）"""
        related = []
        
        for other_title, other_note in self.all_notes.items():
            if other_title == target_note['title']:
                continue
                
            # 如果已经有链接，跳过
            if other_title in target_note['links']:
                continue
            
            # 计算相似度
            score = self.calculate_similarity(target_note, other_note)
            
            # 降低阈值，更容易匹配
            if score >= 5:  # 从8降低到5
                related.append((other_note, score))
        
        # 如果还是找不到，进一步降低阈值
        if len(related) < 2:
            for other_title, other_note in self.all_notes.items():
                if other_title == target_note['title']:
                    continue
                if other_title in target_note['links']:
                    continue
                
                score = self.calculate_similarity(target_note, other_note)
                if score >= 2:  # 进一步降低到2
                    related.append((other_note, score))
        
        # 排序并返回
        related.sort(key=lambda x: x[1], reverse=True)
        return related[:max_results]
    
    def add_links_to_note(self, note, related_notes):
        """为笔记添加链接"""
        if not related_notes:
            return False
            
        content = note['content']
        
        # 检查是否已有相关笔记部分
        if '## 相关笔记' in content:
            # 更新现有部分
            lines = content.split('\n')
            new_lines = []
            in_related_section = False
            
            for line in lines:
                if line.startswith('## 相关笔记'):
                    in_related_section = True
                    new_lines.append(line)
                    new_lines.append(f"*更新于 {datetime.now().strftime('%Y-%m-%d')}*\n")
                    
                    # 添加新的相关笔记
                    for related_note, score in related_notes:
                        new_lines.append(f"- [[{related_note['title']}]]")
                    
                elif in_related_section and line.startswith('##'):
                    in_related_section = False
                    new_lines.append(line)
                elif not in_related_section:
                    new_lines.append(line)
            
            content = '\n'.join(new_lines)
        else:
            # 添加新的相关笔记部分
            content += f"\n\n---\n\n## 相关笔记\n"
            content += f"*自动生成于 {datetime.now().strftime('%Y-%m-%d')}*\n\n"
            
            for related_note, score in related_notes:
                content += f"- [[{related_note['title']}]]\n"
        
        # 保存文件
        try:
            with open(note['path'], 'w', encoding='utf-8') as f:
                f.write(content)
            return True
        except:
            return False
    
    def run(self, max_links_threshold=2):
        """运行强力链接器"""
        print("强力孤立笔记链接器")
        print("=" * 30)
        
        # 收集笔记
        print("收集笔记...")
        md_files = list(self.base_dir.rglob("*.md"))
        
        for file_path in md_files:
            note = self.analyze_note(file_path)
            if note:
                self.all_notes[note['title']] = note
        
        print(f"找到 {len(self.all_notes)} 个笔记")
        
        # 找到孤立笔记
        isolated_notes = []
        for note in self.all_notes.values():
            if note['link_count'] <= max_links_threshold:
                isolated_notes.append(note)
        
        print(f"发现 {len(isolated_notes)} 个孤立笔记（链接数≤{max_links_threshold}）")
        
        # 为孤立笔记添加链接
        print("\n开始添加链接...")
        success_count = 0
        
        for i, note in enumerate(isolated_notes):
            print(f"处理 {i+1}/{len(isolated_notes)}: {note['title'][:50]}...")
            
            related_notes = self.find_related_notes(note)
            
            if related_notes:
                if self.add_links_to_note(note, related_notes):
                    success_count += 1
                    print(f"  ✓ 添加了 {len(related_notes)} 个链接")
                else:
                    print(f"  ✗ 添加失败")
            else:
                print(f"  - 未找到相关笔记")
        
        print(f"\n✅ 完成！")
        print(f"📊 处理了 {len(isolated_notes)} 个孤立笔记")
        print(f"🔗 成功为 {success_count} 个笔记添加了链接")
        print(f"📈 成功率: {success_count/len(isolated_notes)*100:.1f}%")

if __name__ == "__main__":
    base_dir = Path(r"c:\Users\<USER>\OneDrive\obsidian笔记系统\战略与战术")
    
    print("选择处理模式:")
    print("1. 处理链接数≤1的笔记")
    print("2. 处理链接数≤2的笔记（推荐）")
    print("3. 处理链接数≤3的笔记")
    
    choice = input("请选择 (1/2/3): ").strip()
    threshold_map = {"1": 1, "2": 2, "3": 3}
    threshold = threshold_map.get(choice, 2)
    
    generator = PowerfulNoteLinkGenerator(base_dir)
    generator.run(max_links_threshold=threshold)
