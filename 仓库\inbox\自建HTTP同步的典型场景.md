---
title: "自建HTTP同步的典型场景"
source: "[[官方同步 vs 自建同步：为什么更安全？]]"
tags: ["自建HTTP", "同步场景", "安全方案"]
keywords: ["自建HTTP", "同步场景", "安全方案", "HTTPS证书"]
created: 2025-08-02
type: 原子笔记
---

# 自建HTTP同步的典型场景

- **（1）用CouchDB（LiveSync插件）**
  - **怎么传数据**：
    text
    你的手机 → HTTP → 你的CouchDB服务器 → HTTP → 你的电脑
  - **风险**：如果中间有人“偷听”，能看到你的笔记内容。
- **（2）解决方案**：
  - **方法1（推荐）**：  给你的服务器配 HTTPS证书（像给快递站装保险柜）。
    - 工具：`Caddy`或`Nginx`（一行命令自动搞定）。
  - **方法2（临时）**：  如果懒得弄HTTPS，**不要同步敏感笔记**（比如密码、日记）。

---

## 元信息
- **来源笔记**: [[官方同步 vs 自建同步：为什么更安全？]]
- **创建时间**: 2025/8/2 20:22:10
- **标签**: #自建HTTP #同步场景 #安全方案
- **关键词**: 自建HTTP, 同步场景, 安全方案, HTTPS证书

## 相关链接
- 返回原笔记: [[官方同步 vs 自建同步：为什么更安全？]]
