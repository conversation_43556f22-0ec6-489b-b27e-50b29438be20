#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
少于2链接笔记处理器
专门处理链接数少于2个的孤立笔记
"""

import os
import re
from pathlib import Path
from datetime import datetime

def extract_links(content):
    """提取现有链接"""
    return re.findall(r'\[\[([^\]]+)\]\]', content)

def find_isolated_notes(base_dir):
    """找到链接少于2个的笔记"""
    isolated_notes = []
    exclude_dirs = ['默写本', '.obsidian', '.trash', '原子笔记', '知识卡片']
    
    for file_path in base_dir.rglob("*.md"):
        if any(ex in str(file_path).lower() for ex in exclude_dirs):
            continue
            
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
        except:
            continue
        
        title = file_path.stem
        links = extract_links(content)
        
        # 只处理链接数少于2个的笔记
        if len(links) < 2:
            isolated_notes.append({
                'path': file_path,
                'title': title,
                'content': content,
                'links': links,
                'link_count': len(links)
            })
    
    return isolated_notes

def get_popular_links():
    """获取热门链接列表"""
    return [
        "✅3个通用生存法则-从王石案例",
        "创业者必知的「吸血条款」清单",
        "股权分配的5大致命陷阱",
        "融资谈判的10个关键条款",
        "公司治理结构设计",
        "风险控制体系建设",
        "团队管理核心法则",
        "商业模式设计思路",
        "财务管理基础知识",
        "法律合规要点总结",
        "投资人经典话术解码",
        "对赌协议深度解析",
        "董事会控制权争夺",
        "知识产权保护策略",
        "现金流管理技巧"
    ]

def select_relevant_links(note, popular_links):
    """为笔记选择相关链接"""
    title = note['title'].lower()
    content = note['content'].lower()
    full_text = f"{title} {content}"
    
    selected = []
    
    # 基于关键词匹配选择相关链接
    keyword_mapping = {
        '股权': ["✅3个通用生存法则-从王石案例", "股权分配的5大致命陷阱", "董事会控制权争夺"],
        '融资': ["创业者必知的「吸血条款」清单", "融资谈判的10个关键条款", "对赌协议深度解析"],
        '投资': ["投资人经典话术解码", "融资谈判的10个关键条款", "对赌协议深度解析"],
        '法律': ["法律合规要点总结", "知识产权保护策略", "创业者必知的「吸血条款」清单"],
        '风险': ["风险控制体系建设", "法律合规要点总结", "✅3个通用生存法则-从王石案例"],
        '管理': ["团队管理核心法则", "公司治理结构设计", "商业模式设计思路"],
        '团队': ["团队管理核心法则", "公司治理结构设计", "商业模式设计思路"],
        '财务': ["财务管理基础知识", "现金流管理技巧", "商业模式设计思路"],
        '现金流': ["现金流管理技巧", "财务管理基础知识", "商业模式设计思路"]
    }
    
    # 根据关键词匹配
    for keyword, related_links in keyword_mapping.items():
        if keyword in full_text:
            for link in related_links:
                if link not in note['links'] and link not in selected:
                    selected.append(link)
                    if len(selected) >= 4:  # 最多选择4个
                        break
            if len(selected) >= 4:
                break
    
    # 如果没有匹配到足够的链接，随机添加一些热门链接
    if len(selected) < 3:
        for link in popular_links:
            if link not in note['links'] and link not in selected:
                selected.append(link)
                if len(selected) >= 3:
                    break
    
    return selected

def add_links_to_note(note, new_links):
    """为笔记添加链接"""
    if not new_links:
        return False
    
    content = note['content']
    
    # 添加相关笔记部分
    if '## 相关笔记' not in content:
        content += f"\n\n---\n\n## 相关笔记\n"
        content += f"*自动添加于 {datetime.now().strftime('%Y-%m-%d')}*\n\n"
        
        for link in new_links:
            content += f"- [[{link}]]\n"
        
        try:
            with open(note['path'], 'w', encoding='utf-8') as f:
                f.write(content)
            return True
        except:
            return False
    
    return False

def main():
    """主函数"""
    base_dir = Path(r"c:\Users\<USER>\OneDrive\obsidian笔记系统\战略与战术")
    
    print("少于2链接笔记处理器")
    print("=" * 30)
    
    # 找到孤立笔记
    print("查找链接少于2个的笔记...")
    isolated_notes = find_isolated_notes(base_dir)
    
    # 按链接数分组显示
    zero_links = [n for n in isolated_notes if n['link_count'] == 0]
    one_link = [n for n in isolated_notes if n['link_count'] == 1]
    
    print(f"找到孤立笔记:")
    print(f"  0个链接: {len(zero_links)}个")
    print(f"  1个链接: {len(one_link)}个")
    print(f"  总计: {len(isolated_notes)}个")
    
    if len(isolated_notes) == 0:
        print("🎉 没有找到孤立笔记！")
        return
    
    # 获取热门链接
    popular_links = get_popular_links()
    
    # 处理每个孤立笔记
    print(f"\n开始处理孤立笔记...")
    success_count = 0
    
    for i, note in enumerate(isolated_notes):
        print(f"处理 {i+1}/{len(isolated_notes)}: {note['title'][:50]}...")
        print(f"  当前链接数: {note['link_count']}")
        
        # 选择相关链接
        new_links = select_relevant_links(note, popular_links)
        
        if new_links:
            if add_links_to_note(note, new_links):
                success_count += 1
                print(f"  ✓ 添加了 {len(new_links)} 个链接")
            else:
                print(f"  ✗ 添加失败")
        else:
            print(f"  - 未找到合适链接")
    
    print(f"\n✅ 处理完成！")
    print(f"📊 处理了 {len(isolated_notes)} 个孤立笔记")
    print(f"🔗 成功为 {success_count} 个笔记添加了链接")
    print(f"📈 成功率: {success_count/len(isolated_notes)*100:.1f}%")
    
    # 显示处理结果
    remaining_isolated = len(isolated_notes) - success_count
    if remaining_isolated == 0:
        print(f"🏆 完美！所有孤立笔记都已处理！")
    else:
        print(f"⚠️  还有 {remaining_isolated} 个笔记可能需要手动处理")

if __name__ == "__main__":
    main()
