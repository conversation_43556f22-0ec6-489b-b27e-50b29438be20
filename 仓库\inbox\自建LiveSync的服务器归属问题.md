---
title: "自建LiveSync的服务器归属问题"
source: "[[官方同步 vs 自建同步：为什么更安全？]]"
tags: ["自建LiveSync", "服务器归属", "Fly.io", "Obsidian官方"]
keywords: ["自建LiveSync", "服务器归属", "Fly.io", "Obsidian官方", "租房子", "住酒店"]
created: 2025-08-02
type: 原子笔记
---

# 自建LiveSync的服务器归属问题

- **“自建” ≠ “自己买硬件”**
  - 你租用云服务器（如Fly.io/AWS/阿里云）就相当于 **“租房子”**：
    - 房子产权是房东的（服务器硬件归平台）
    - 但租房期间 **完全由你控制**（数据、软件、权限全归你）
  - **“用别人的服务器”**（如iCloud/Obsidian官方）相当于 **“住酒店”**：
    - 酒店有权随时开门查房（平台可访问你的数据）

---

## 元信息
- **来源笔记**: [[官方同步 vs 自建同步：为什么更安全？]]
- **创建时间**: 2025/8/2 20:22:24
- **标签**: #自建LiveSync #服务器归属 #Fly.io #Obsidian官方
- **关键词**: 自建LiveSync, 服务器归属, Fly.io, Obsidian官方, 租房子, 住酒店

## 相关链接
- 返回原笔记: [[官方同步 vs 自建同步：为什么更安全？]]
