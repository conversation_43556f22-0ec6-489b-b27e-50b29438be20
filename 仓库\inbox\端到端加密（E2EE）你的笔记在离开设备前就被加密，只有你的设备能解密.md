---
已学: 
🍎重要: 
📒学习笔记: true
---
# 端到端加密（E2EE）你的笔记在离开设备前就被加密，只有你的设备能解密
#### **1. 官方同步为什么安全？**

**✅ 核心保障：**

- **端到端加密（E2EE）**：
    
    - 你的笔记在离开设备前就被加密，只有你的设备能解密。
        
    - 即使Obsidian公司被黑客攻破，他们也看不到你的内容。
        
- **物理隔离**：
    
    - 官方服务器专为Obsidian设计，防火墙+入侵检测全开。
        
- **法律约束**：
    
    - 受GDPR等隐私法保护，官方不敢乱动用户数据。
        

**🔒 举个栗子**：

> 你寄快递，官方同步给你的数据上了**密码箱**（钥匙只有你有），快递员（服务器）只能运箱子，但打不开。

---

#### **2. 自建方案（如LiveSync）为什么安全？**

**✅ 核心保障：**

- **数据完全私有**：
    
    - 你的笔记只存在你自己的服务器（如家里的NAS/租的云主机），**没有第三方经手**。
        
- **加密自主权**：
    
    - 你可以用更狠的加密工具（如`Veracrypt`全盘加密），连服务器管理员都看不懂。
        
- **网络隔离**：
    
    - 自建服务器可以关闭所有外网端口，只允许你的设备连接。
        

**🔒 举个栗子**：

> 你把笔记锁在自家保险箱（自建服务器），钥匙你拿着，连造保险箱的厂商都进不去。

---

#### **3. 为什么免费方案（如iCloud/Remotely Save）容易泄露？**

**❌ 风险点：**

- **网盘默认不加密**：
    
    - 你的笔记在微软/苹果服务器上是“裸奔”的，员工或有权限的人可能看到。
        
- **中间人攻击**：
    
    - 如果同步时用HTTP（非HTTPS），黑客在咖啡厅WiFi就能截获数据。
        
- **平台后门**：
    
    - 某些网盘会扫描用户文件（如版权审查），可能误删你的笔记。
        

**💣 真实案例**：  
2021年，某知名网盘因漏洞导致用户照片被陌生人浏览，而Obsidian官方同步至今零泄露事件。

---

#### **4. 安全等级对比**

|方案|谁能看到你的笔记？|加密强度|抗审查性|
|---|---|---|---|
|**官方同步**|只有你|军用级|高|
|**自建LiveSync**|只有你|可自定义|极高|
|**iCloud/OneDrive**|你+平台员工|部分加密|低|# 250802
