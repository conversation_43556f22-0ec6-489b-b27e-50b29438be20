---
title: "风险提醒"
source: "[[官方同步 vs 自建同步：为什么更安全？]]"
tags: ["风险提醒", "Fly.io", "数据安全", "自建服务器"]
keywords: ["风险提醒", "Fly.io", "数据安全", "自建服务器", "阿里云", "旧电脑", "NAS"]
created: 2025-08-02
type: 原子笔记
---

# 风险提醒

- **如果Fly.io倒闭/封你账号**：
  - 你的数据不会丢（平时定期用`couchdb-dump`备份即可）
  - 换个平台（如阿里云）重新部署，10分钟恢复
- **如果完全不想依赖任何平台**：
  - 用家里旧电脑/NAS 24小时开机自建服务器（教程可另给）

---

## 元信息
- **来源笔记**: [[官方同步 vs 自建同步：为什么更安全？]]
- **创建时间**: 2025/8/2 20:22:27
- **标签**: #风险提醒 #Fly.io #数据安全 #自建服务器
- **关键词**: 风险提醒, Fly.io, 数据安全, 自建服务器, 阿里云, 旧电脑, NAS

## 相关链接
- 返回原笔记: [[官方同步 vs 自建同步：为什么更安全？]]
