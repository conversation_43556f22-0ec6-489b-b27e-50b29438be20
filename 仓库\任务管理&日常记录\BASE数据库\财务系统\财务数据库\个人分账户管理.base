filters:
  and:
    - file.inFolder("财务管理系统/个人财务数据")
    - file.ext == "md"
formulas:
  blue_bag_total: if(living_expense && emergency_fund && utilities, living_expense + emergency_fund + utilities, 0)
  green_bag_total: if(travel_fund && social_fund && insurance, travel_fund + social_fund + insurance, 0)
  yellow_bag_total: if(fixed_deposit && investment_fund, fixed_deposit + investment_fund, 0)
  rent_months: if(rent_reserve && monthly_rent, (rent_reserve / monthly_rent).toFixed(1) + "个月", "未知")
  salary_months: if(salary_reserve && monthly_salary, (salary_reserve / monthly_salary).toFixed(1) + "个月", "未知")
  marketing_months: if(marketing_reserve && monthly_marketing, (marketing_reserve / monthly_marketing).toFixed(1) + "个月", "未知")
  blue_bag_status: if(formula.blue_bag_total >= target_blue, "✅ 充足", if(formula.blue_bag_total >= target_blue * 0.7, "⚠️ 注意", "❗ 不足"))
  green_bag_status: if(formula.green_bag_total >= target_green, "✅ 充足", if(formula.green_bag_total >= target_green * 0.7, "⚠️ 注意", "❗ 不足"))
  yellow_bag_status: if(formula.yellow_bag_total >= target_yellow, "✅ 充足", if(formula.yellow_bag_total >= target_yellow * 0.7, "⚠️ 注意", "❗ 不足"))
  rent_status: if(formula.rent_months >= 3, "✅ 安全", if(formula.rent_months >= 2, "⚠️ 注意", "❗ 危险"))
  salary_status: if(formula.salary_months >= 3, "✅ 安全", if(formula.salary_months >= 2, "⚠️ 注意", "❗ 危险"))
  total_savings: if(formula.blue_bag_total && formula.green_bag_total && formula.yellow_bag_total, formula.blue_bag_total + formula.green_bag_total + formula.yellow_bag_total, 0)
  savings_rate: if(monthly_income && formula.total_savings, (formula.total_savings / monthly_income * 100).toFixed(1) + "%", "0%")
  monthly_surplus: if(monthly_income && monthly_expense, monthly_income - monthly_expense, 0)
properties:
  record_type:
    displayName: 记录类型
  date:
    displayName: 日期
  month:
    displayName: 月份
  monthly_income:
    displayName: 月收入(元)
  monthly_expense:
    displayName: 月支出(元)
  formula.monthly_surplus:
    displayName: 月度结余(元)
  bag_color:
    displayName: 资金袋颜色
  living_expense:
    displayName: 🔵生活费(元)
  emergency_fund:
    displayName: 🔵备用金(元)
  utilities:
    displayName: 🔵水电物业(元)
  travel_fund:
    displayName: 🟢旅游基金(元)
  social_fund:
    displayName: 🟢人情往来(元)
  insurance:
    displayName: 🟢保险费(元)
  fixed_deposit:
    displayName: 🟡定期存款(元)
  investment_fund:
    displayName: 🟡投资基金(元)
  formula.blue_bag_total:
    displayName: 🔵蓝色袋总额
  formula.green_bag_total:
    displayName: 🟢绿色袋总额
  formula.yellow_bag_total:
    displayName: 🟡黄色袋总额
  target_blue:
    displayName: 蓝色袋目标
  target_green:
    displayName: 绿色袋目标
  target_yellow:
    displayName: 黄色袋目标
  formula.blue_bag_status:
    displayName: 🔵蓝色袋状态
  formula.green_bag_status:
    displayName: 🟢绿色袋状态
  formula.yellow_bag_status:
    displayName: 🟡黄色袋状态
  medicine_type:
    displayName: 药箱格子
  rent_reserve:
    displayName: 💊房租储备(元)
  salary_reserve:
    displayName: 💉工资储备(元)
  marketing_reserve:
    displayName: 🧪推广储备(元)
  tax_reserve:
    displayName: 🔬税收储备(元)
  profit_reserve:
    displayName: 💰利润储备(元)
  monthly_rent:
    displayName: 月租金(元)
  monthly_salary:
    displayName: 月工资支出(元)
  monthly_marketing:
    displayName: 月推广支出(元)
  formula.rent_months:
    displayName: 房租可用月数
  formula.salary_months:
    displayName: 工资可用月数
  formula.marketing_months:
    displayName: 推广可用月数
  formula.rent_status:
    displayName: 💊房租状态
  formula.salary_status:
    displayName: 💉工资状态
  formula.total_savings:
    displayName: 总储蓄(元)
  formula.savings_rate:
    displayName: 储蓄率
  notes:
    displayName: 备注
  adjustment_reason:
    displayName: 调整原因
views:
  - type: table
    name: 三色资金袋总览
    filters:
      and:
        - record_type == "三色资金袋"
    order:
      - month
      - formula.blue_bag_total
      - formula.green_bag_total
      - formula.yellow_bag_total
      - formula.blue_bag_status
      - formula.green_bag_status
      - formula.yellow_bag_status
      - formula.total_savings
      - formula.savings_rate
    sort:
      - property: date
        direction: DESC
    limit: 12
  - type: table
    name: 五格药箱监控
    filters:
      and:
        - record_type == "五格药箱"
    order:
      - rent_reserve
      - salary_reserve
      - marketing_reserve
      - month
      - tax_reserve
      - profit_reserve
      - formula.rent_months
      - formula.salary_months
      - formula.rent_status
      - formula.salary_status
    sort:
      - property: month
        direction: ASC
      - property: rent_reserve
        direction: ASC
      - property: date
        direction: DESC
    limit: 12
  - type: table
    name: 月度财务分析
    filters:
      and:
        - record_type == "月度分析"
    order:
      - month
      - monthly_income
      - monthly_expense
      - formula.monthly_surplus
      - formula.total_savings
      - formula.savings_rate
      - adjustment_reason
    sort:
      - property: month
        direction: DESC
    limit: 12
  - type: cards
    name: 资金袋预警
    filters:
      or:
        - formula.blue_bag_status == "❗ 不足"
        - formula.green_bag_status == "❗ 不足"
        - formula.yellow_bag_status == "❗ 不足"
    order:
      - month
      - formula.blue_bag_status
      - formula.green_bag_status
      - formula.yellow_bag_status
      - notes
    sort:
      - property: date
        direction: DESC
    limit: 10
  - type: cards
    name: 药箱预警
    filters:
      or:
        - formula.rent_status == "❗ 危险"
        - formula.salary_status == "❗ 危险"
    order:
      - month
      - formula.rent_status
      - formula.salary_status
      - formula.rent_months
      - formula.salary_months
      - notes
    sort:
      - property: date
        direction: DESC
    limit: 8
  - type: table
    name: 储蓄目标追踪
    filters:
      and:
        - record_type == "月度分析"
        - formula.savings_rate >= "15%"
    order:
      - month
      - formula.savings_rate
      - formula.total_savings
      - formula.monthly_surplus
      - notes
    sort:
      - property: formula.savings_rate
        direction: DESC
    limit: 12
  - type: table
    name: 分账户执行情况
    filters:
      or:
        - record_type == "三色资金袋"
        - record_type == "五格药箱"
    order:
      - record_type
      - month
      - formula.blue_bag_status
      - formula.green_bag_status
      - formula.yellow_bag_status
      - formula.rent_status
      - formula.salary_status
    sort:
      - property: record_type
        direction: DESC
      - property: date
        direction: DESC
    limit: 20
