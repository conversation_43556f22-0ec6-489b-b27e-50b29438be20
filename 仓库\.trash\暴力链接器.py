#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
暴力链接器
简单粗暴地为笔记添加链接，不做复杂分析
"""

import os
import re
from pathlib import Path

def extract_links(content):
    """提取现有链接"""
    return re.findall(r'\[\[([^\]]+)\]\]', content)

def add_basic_links(file_path):
    """为笔记添加基础链接"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
    except:
        return False
    
    # 检查现有链接数
    existing_links = extract_links(content)
    if len(existing_links) >= 4:  # 如果已经有4个或更多链接，跳过
        return False
    
    # 如果已经有相关笔记部分，跳过
    if '## 相关笔记' in content:
        return False
    
    title = file_path.stem.lower()
    
    # 预定义的常用链接
    common_links = [
        "✅3个通用生存法则-从王石案例",
        "创业者必知的「吸血条款」清单",
        "股权分配的5大致命陷阱",
        "融资谈判的10个关键条款",
        "公司治理结构设计",
        "风险控制体系建设",
        "团队管理核心法则",
        "商业模式设计思路",
        "财务管理基础知识",
        "法律合规要点总结"
    ]
    
    # 根据标题关键词选择相关链接
    selected_links = []
    
    # 关键词匹配
    if any(word in title for word in ['股权', '股份', '股东']):
        selected_links.extend(["✅3个通用生存法则-从王石案例", "股权分配的5大致命陷阱"])
    
    if any(word in title for word in ['融资', '投资', '对赌']):
        selected_links.extend(["创业者必知的「吸血条款」清单", "融资谈判的10个关键条款"])
    
    if any(word in title for word in ['管理', '团队', '领导']):
        selected_links.extend(["团队管理核心法则", "公司治理结构设计"])
    
    if any(word in title for word in ['风险', '法律', '合规']):
        selected_links.extend(["风险控制体系建设", "法律合规要点总结"])
    
    if any(word in title for word in ['财务', '成本', '利润']):
        selected_links.extend(["财务管理基础知识", "商业模式设计思路"])
    
    # 如果没有匹配的，随机选择一些
    if not selected_links:
        selected_links = common_links[:3]
    
    # 去重并限制数量
    selected_links = list(set(selected_links))[:3]
    
    # 排除已存在的链接
    selected_links = [link for link in selected_links if link not in existing_links]
    
    if selected_links:
        # 添加相关笔记部分
        content += f"\n\n---\n\n## 相关笔记\n\n"
        for link in selected_links:
            content += f"- [[{link}]]\n"
        
        try:
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(content)
            return True
        except:
            return False
    
    return False

def main():
    """主函数"""
    base_dir = Path(r"c:\Users\<USER>\OneDrive\obsidian笔记系统\战略与战术")
    
    print("暴力链接器")
    print("=" * 20)
    
    # 收集所有md文件
    md_files = []
    exclude_dirs = ['默写本', '.obsidian', '.trash', '原子笔记', '知识卡片']
    
    for file_path in base_dir.rglob("*.md"):
        if any(ex in str(file_path) for ex in exclude_dirs):
            continue
        md_files.append(file_path)
    
    print(f"找到 {len(md_files)} 个笔记文件")
    
    # 处理每个文件
    success_count = 0
    for i, file_path in enumerate(md_files):
        print(f"处理 {i+1}/{len(md_files)}: {file_path.name}")
        
        if add_basic_links(file_path):
            success_count += 1
            print(f"  ✓ 已添加链接")
        else:
            print(f"  - 跳过")
    
    print(f"\n完成！成功为 {success_count} 个笔记添加了链接")

if __name__ == "__main__":
    main()
