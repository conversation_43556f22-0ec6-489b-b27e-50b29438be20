#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
全系统笔记统计器
统计整个obsidian笔记系统中所有笔记的链接情况
"""

import os
import re
from pathlib import Path
from datetime import datetime

def extract_links(content):
    """提取现有链接"""
    return re.findall(r'\[\[([^\]]+)\]\]', content)

def analyze_all_notes_in_system(base_dir):
    """分析整个笔记系统中的所有笔记"""
    notes_stats = []
    exclude_dirs = ['.obsidian', '.trash']  # 只排除系统文件夹
    
    print(f"扫描目录: {base_dir}")
    
    for file_path in base_dir.rglob("*.md"):
        # 只排除系统文件夹
        if any(ex in str(file_path).lower() for ex in exclude_dirs):
            continue
            
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
        except:
            continue
        
        title = file_path.stem
        links = extract_links(content)
        relative_path = str(file_path.relative_to(base_dir))
        
        notes_stats.append({
            'title': title,
            'path': relative_path,
            'link_count': len(links),
            'links': links,
            'word_count': len(content)
        })
    
    return notes_stats

def create_comprehensive_report(notes_stats, base_dir):
    """创建全面的链接分析报告"""
    report_path = base_dir / "全系统链接分析报告.md"
    
    # 按链接数分类
    link_distribution = {
        '0个链接': [],
        '1个链接': [],
        '2个链接': [],
        '3-5个链接': [],
        '6-10个链接': [],
        '10+个链接': []
    }
    
    for note in notes_stats:
        count = note['link_count']
        if count == 0:
            link_distribution['0个链接'].append(note)
        elif count == 1:
            link_distribution['1个链接'].append(note)
        elif count == 2:
            link_distribution['2个链接'].append(note)
        elif count <= 5:
            link_distribution['3-5个链接'].append(note)
        elif count <= 10:
            link_distribution['6-10个链接'].append(note)
        else:
            link_distribution['10+个链接'].append(note)
    
    # 生成报告
    report_content = f"""# 全系统链接分析报告

生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
总笔记数: {len(notes_stats)}

## 链接分布统计

"""
    
    total_isolated = 0
    for category, notes in link_distribution.items():
        count = len(notes)
        percentage = count / len(notes_stats) * 100
        report_content += f"- **{category}**: {count}个 ({percentage:.1f}%)\n"
        
        # 统计孤立笔记（0-2个链接）
        if category in ['0个链接', '1个链接', '2个链接']:
            total_isolated += count
    
    isolated_percentage = total_isolated / len(notes_stats) * 100
    report_content += f"\n**孤立笔记总计（0-2个链接）**: {total_isolated}个 ({isolated_percentage:.1f}%)\n"
    
    # 按文件夹分组统计
    folder_stats = {}
    for note in notes_stats:
        folder = str(Path(note['path']).parent)
        if folder == '.':
            folder = '根目录'
        
        if folder not in folder_stats:
            folder_stats[folder] = {'total': 0, 'isolated': 0}
        
        folder_stats[folder]['total'] += 1
        if note['link_count'] <= 2:
            folder_stats[folder]['isolated'] += 1
    
    report_content += f"\n## 按文件夹分布\n\n"
    for folder, stats in sorted(folder_stats.items()):
        isolated_rate = stats['isolated'] / stats['total'] * 100
        report_content += f"- **{folder}**: {stats['total']}个笔记, {stats['isolated']}个孤立 ({isolated_rate:.1f}%)\n"
    
    # 详细列出孤立笔记
    report_content += f"\n## 孤立笔记详情\n\n"
    
    for category in ['0个链接', '1个链接', '2个链接']:
        notes = link_distribution[category]
        if notes:
            report_content += f"### {category} ({len(notes)}个)\n\n"
            for note in notes[:100]:  # 显示前100个
                report_content += f"- {note['title']} ({note['path']})\n"
            if len(notes) > 100:
                report_content += f"- ... 还有 {len(notes) - 100} 个笔记\n"
            report_content += "\n"
    
    with open(report_path, 'w', encoding='utf-8') as f:
        f.write(report_content)
    
    return report_path, link_distribution

def main():
    """主函数"""
    # 整个obsidian笔记系统的根目录
    base_dir = Path(r"c:\Users\<USER>\OneDrive\obsidian笔记系统")
    
    print("全系统笔记统计器")
    print("=" * 40)
    
    # 分析所有笔记
    print("分析整个笔记系统...")
    notes_stats = analyze_all_notes_in_system(base_dir)
    print(f"分析了 {len(notes_stats)} 个笔记")
    
    # 创建详细报告
    print("生成全面报告...")
    report_path, link_distribution = create_comprehensive_report(notes_stats, base_dir)
    
    # 显示统计结果
    print(f"\n📊 全系统链接分布统计:")
    total_isolated = 0
    for category, notes in link_distribution.items():
        count = len(notes)
        percentage = count / len(notes_stats) * 100
        print(f"  {category}: {count}个 ({percentage:.1f}%)")
        
        if category in ['0个链接', '1个链接', '2个链接']:
            total_isolated += count
    
    isolated_percentage = total_isolated / len(notes_stats) * 100
    print(f"\n🔍 孤立笔记总计（0-2个链接）: {total_isolated}个 ({isolated_percentage:.1f}%)")
    
    print(f"\n📄 详细报告已保存: {report_path}")
    
    # 建议
    if total_isolated > len(notes_stats) * 0.2:  # 超过20%
        print(f"\n💡 发现大量孤立笔记，建议:")
        print(f"  - 运行全系统链接增强脚本")
        print(f"  - 重点处理0个和1个链接的笔记")
        print(f"  - 考虑为重要笔记手动添加链接")

if __name__ == "__main__":
    main()
