#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
零链接笔记处理器
专门处理完全没有链接的笔记，为它们添加最基本的链接
"""

import os
import re
from pathlib import Path
from datetime import datetime

def extract_links(content):
    """提取现有链接"""
    return re.findall(r'\[\[([^\]]+)\]\]', content)

def has_keywords(text, keywords):
    """检查是否包含关键词"""
    for keyword in keywords:
        if keyword in text:
            return True
    return False

def find_zero_link_notes(base_dir):
    """找到没有任何链接的笔记"""
    zero_link_notes = []
    exclude_dirs = ['默写本', '.obsidian', '.trash', '原子笔记', '知识卡片', 'template']
    
    for file_path in base_dir.rglob("*.md"):
        # 排除特定目录
        if any(ex in str(file_path).lower() for ex in exclude_dirs):
            continue
            
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
        except:
            continue
        
        # 检查是否有链接
        links = extract_links(content)
        if len(links) == 0:
            zero_link_notes.append({
                'path': file_path,
                'title': file_path.stem,
                'content': content
            })
    
    return zero_link_notes

def find_popular_notes(base_dir):
    """找到被链接最多的笔记（热门笔记）"""
    all_links = []
    exclude_dirs = ['默写本', '.obsidian', '.trash', '原子笔记', '知识卡片', 'template']
    
    for file_path in base_dir.rglob("*.md"):
        if any(ex in str(file_path).lower() for ex in exclude_dirs):
            continue
            
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            links = extract_links(content)
            all_links.extend(links)
        except:
            continue
    
    # 统计链接频率
    link_count = {}
    for link in all_links:
        link_count[link] = link_count.get(link, 0) + 1
    
    # 返回前20个热门笔记
    popular = sorted(link_count.items(), key=lambda x: x[1], reverse=True)[:20]
    return [link[0] for link in popular]

def add_basic_links(note, popular_notes):
    """为笔记添加基本链接"""
    content = note['content']
    title = note['title']
    
    # 基于关键词匹配找到相关的热门笔记
    related_links = []
    
    # 关键词匹配
    keyword_groups = {
        '股权': ['股权', '股份', '股东', '控制权'],
        '融资': ['融资', '投资', '估值', '对赌'],
        '法律': ['法律', '合规', '风险', '合同'],
        '管理': ['管理', '团队', '领导', '运营'],
        '财务': ['财务', '现金流', '利润', '成本']
    }
    
    full_text = f"{title} {content}".lower()
    
    for popular_note in popular_notes:
        # 如果热门笔记的标题出现在当前笔记中
        if popular_note.lower() in full_text:
            related_links.append(popular_note)
            continue
        
        # 关键词匹配
        for group, keywords in keyword_groups.items():
            if has_keywords(full_text, keywords) and has_keywords(popular_note.lower(), keywords):
                related_links.append(popular_note)
                break
        
        if len(related_links) >= 3:  # 最多3个链接
            break
    
    # 如果没找到相关链接，随机添加一些热门笔记
    if len(related_links) == 0:
        related_links = popular_notes[:2]
    
    # 添加链接到笔记
    if related_links:
        content += f"\n\n---\n\n## 相关笔记\n"
        content += f"*自动添加于 {datetime.now().strftime('%Y-%m-%d')}*\n\n"
        
        for link in related_links[:3]:  # 最多3个
            content += f"- [[{link}]]\n"
        
        return content
    
    return None

def main():
    """主函数"""
    base_dir = Path(r"c:\Users\<USER>\OneDrive\obsidian笔记系统\战略与战术")
    
    print("零链接笔记处理器")
    print("=" * 30)
    
    # 找到零链接笔记
    print("查找零链接笔记...")
    zero_link_notes = find_zero_link_notes(base_dir)
    print(f"找到 {len(zero_link_notes)} 个零链接笔记")
    
    if len(zero_link_notes) == 0:
        print("没有找到零链接笔记！")
        return
    
    # 找到热门笔记
    print("分析热门笔记...")
    popular_notes = find_popular_notes(base_dir)
    print(f"找到 {len(popular_notes)} 个热门笔记")
    
    # 为零链接笔记添加链接
    print("\n开始添加链接...")
    success_count = 0
    
    for i, note in enumerate(zero_link_notes):
        print(f"处理 {i+1}/{len(zero_link_notes)}: {note['title'][:40]}...")
        
        new_content = add_basic_links(note, popular_notes)
        
        if new_content:
            try:
                with open(note['path'], 'w', encoding='utf-8') as f:
                    f.write(new_content)
                success_count += 1
                print(f"  ✓ 已添加链接")
            except:
                print(f"  ✗ 保存失败")
        else:
            print(f"  - 未找到合适链接")
    
    print(f"\n✅ 完成！")
    print(f"📊 处理了 {len(zero_link_notes)} 个零链接笔记")
    print(f"🔗 成功为 {success_count} 个笔记添加了链接")

if __name__ == "__main__":
    main()
