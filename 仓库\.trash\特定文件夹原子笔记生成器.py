#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
特定文件夹原子笔记生成器
为财务管理、法律、防骗局、股权财务文件夹生成原子笔记并建立链接
"""

import os
import re
from pathlib import Path
from datetime import datetime
import hashlib

class SpecificFolderAtomicGenerator:
    def __init__(self, base_dir):
        self.base_dir = Path(base_dir)
        self.target_folders = [
            "财务管理",
            "法律", 
            "防骗局",
            "股权财务"
        ]
        self.output_dir = self.base_dir / "特定文件夹原子笔记库"
        self.output_dir.mkdir(exist_ok=True)
        
        self.atomic_dir = self.output_dir / "原子笔记"
        self.atomic_dir.mkdir(exist_ok=True)
        
        self.all_atomic_notes = []
        self.existing_notes = {}
        
    def scan_existing_notes(self):
        """扫描已有的笔记文件"""
        print("扫描已有笔记...")
        
        # 扫描整个智库辅助手册
        for md_file in self.base_dir.rglob("*.md"):
            # 排除输出目录和脚本工具
            if (self.output_dir.name in str(md_file) or 
                '脚本工具' in str(md_file)):
                continue
                
            title = md_file.stem
            self.existing_notes[title] = md_file
        
        print(f"找到 {len(self.existing_notes)} 个已有笔记")
    
    def clean_title(self, title):
        """清理标题"""
        # 去掉序号和特殊符号
        title = re.sub(r'^[\d\.\s]*', '', title)
        title = re.sub(r'^[✅🏆📌⚡️💡🔍🌰❌📊🎯🚀💰⭐️\*\-\#\s]+', '', title)
        title = re.sub(r'[：:]+$', '', title)
        title = title.strip()
        
        # 去掉markdown格式
        title = re.sub(r'\*\*(.*?)\*\*', r'\1', title)
        title = re.sub(r'\*(.*?)\*', r'\1', title)
        
        return title
    
    def extract_atomic_sections(self, content, source_file, folder_name):
        """提取原子化章节"""
        sections = []
        lines = content.split('\n')
        current_section = None
        current_content = []
        
        for line in lines:
            # 检测标题行（各种格式）
            if (line.startswith('#') or 
                re.match(r'^\d+\.\s*\*\*.*\*\*', line) or  # 数字序号+粗体
                re.match(r'^\*\*\d+\.\s*.*\*\*', line) or  # 粗体数字序号
                re.match(r'^[✅🏆📌⚡️💡🔍🌰❌📊🎯🚀💰⭐️\d\.\s]*[\*\-]*\s*\*\*.*\*\*', line) or
                re.match(r'^[✅🏆📌⚡️💡🔍🌰❌📊🎯🚀💰⭐️\d\.\s]*.*[:：]$', line)):
                
                # 保存上一个章节
                if current_section and current_content:
                    content_text = '\n'.join(current_content).strip()
                    if len(content_text) > 80:  # 只保留有足够内容的章节
                        sections.append({
                            'title': current_section,
                            'content': content_text,
                            'source_file': source_file.name,
                            'source_path': source_file,
                            'folder': folder_name
                        })
                
                # 开始新章节
                raw_title = line.replace('#', '').strip()
                current_section = self.clean_title(raw_title)
                current_content = [line]
                
            else:
                if current_section:
                    current_content.append(line)
        
        # 保存最后一个章节
        if current_section and current_content:
            content_text = '\n'.join(current_content).strip()
            if len(content_text) > 80:
                sections.append({
                    'title': current_section,
                    'content': content_text,
                    'source_file': source_file.name,
                    'source_path': source_file,
                    'folder': folder_name
                })
        
        return sections
    
    def find_related_existing_notes(self, atomic_note):
        """找到与原子笔记相关的已有笔记"""
        related_notes = []
        atomic_content = f"{atomic_note['title']} {atomic_note['content']}"
        
        # 关键词匹配
        keywords = self.extract_keywords(atomic_content)
        
        for existing_title, existing_path in self.existing_notes.items():
            # 计算相关度
            score = 0
            
            # 标题相似度
            if any(word in existing_title for word in atomic_note['title'].split() if len(word) > 2):
                score += 10
            
            # 关键词匹配
            for keyword in keywords:
                if keyword in existing_title:
                    score += 5
            
            # 文件夹相关性
            folder_keywords = {
                '财务管理': ['财务', '管理', '成本', '利润', '现金流'],
                '法律': ['法律', '合规', '风险', '合同', '协议'],
                '防骗局': ['防骗', '风险', '陷阱', '警示', '识别'],
                '股权财务': ['股权', '股份', '融资', '投资', '估值']
            }
            
            if atomic_note['folder'] in folder_keywords:
                for keyword in folder_keywords[atomic_note['folder']]:
                    if keyword in existing_title.lower():
                        score += 8
            
            if score >= 5:  # 相关度阈值
                related_notes.append((existing_title, existing_path, score))
        
        # 按相关度排序，返回前3个
        related_notes.sort(key=lambda x: x[2], reverse=True)
        return related_notes[:3]
    
    def extract_keywords(self, content):
        """提取关键词"""
        keywords = []
        
        # 分类关键词
        keyword_groups = {
            '财务管理': ['财务', '管理', '成本', '利润', '现金流', '预算', '资金', '投资回报'],
            '法律': ['法律', '合规', '风险', '合同', '协议', '知识产权', '竞业禁止'],
            '防骗局': ['防骗', '风险', '陷阱', '警示', '识别', '欺诈', '诈骗'],
            '股权财务': ['股权', '股份', '融资', '投资', '估值', '对赌', '控制权']
        }
        
        for group, words in keyword_groups.items():
            for word in words:
                if word in content:
                    keywords.append(word)
        
        return keywords
    
    def generate_tags(self, content, folder_name):
        """生成标签"""
        tags = ['原子笔记', folder_name]
        
        keywords = self.extract_keywords(content)
        
        # 基于文件夹生成特定标签
        folder_tags = {
            '财务管理': ['财务', '管理', '成本控制'],
            '法律': ['法律', '合规', '风险防范'],
            '防骗局': ['防骗', '风险识别', '安全防护'],
            '股权财务': ['股权', '融资', '投资']
        }
        
        if folder_name in folder_tags:
            tags.extend(folder_tags[folder_name])
        
        return list(set(tags))
    
    def safe_filename(self, title):
        """生成安全文件名"""
        safe = re.sub(r'[<>:"/\\|?*\n\r\t]', '_', title)
        safe = re.sub(r'[_\s]+', '_', safe)
        safe = safe.strip('_')
        if len(safe) > 40:
            safe = safe[:40]
        return safe if safe else "原子笔记"
    
    def create_atomic_note(self, section, related_notes):
        """创建原子笔记"""
        title = section['title']
        content = section['content']
        source_file = section['source_file']
        folder_name = section['folder']
        
        tags = self.generate_tags(f"{title} {content}", folder_name)
        
        # 生成原子笔记内容
        note_content = f"""---
title: {title}
source: [[{source_file}]]
folder: {folder_name}
tags: {', '.join(f'#{tag}' for tag in tags)}
created: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
type: 原子笔记
---

# {title}

{content}

---

## 来源信息
- 原始笔记: [[{source_file}]]
- 所属分类: {folder_name}

## 相关笔记
"""
        
        # 添加相关已有笔记的链接
        if related_notes:
            for note_title, note_path, score in related_notes:
                note_content += f"- [[{note_title}]] (相关度: {score})\n"
        else:
            note_content += "- 暂无相关笔记\n"
        
        return note_content
    
    def process_folder(self, folder_name):
        """处理单个文件夹"""
        folder_path = self.base_dir / folder_name
        
        if not folder_path.exists():
            print(f"文件夹不存在: {folder_path}")
            return
        
        print(f"处理文件夹: {folder_name}")
        
        md_files = list(folder_path.rglob("*.md"))
        print(f"  找到 {len(md_files)} 个文件")
        
        for file_path in md_files:
            print(f"  处理: {file_path.name}")
            
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read()
            except Exception as e:
                print(f"    读取失败: {e}")
                continue
            
            # 提取原子章节
            sections = self.extract_atomic_sections(content, file_path, folder_name)
            
            if not sections:
                print(f"    未找到原子章节")
                continue
            
            print(f"    提取到 {len(sections)} 个原子笔记")
            
            for section in sections:
                # 找到相关的已有笔记
                related_notes = self.find_related_existing_notes(section)
                
                # 创建原子笔记
                atomic_content = self.create_atomic_note(section, related_notes)
                
                # 保存原子笔记
                safe_title = self.safe_filename(section['title'])
                atomic_filename = f"{folder_name}_{safe_title}.md"
                atomic_path = self.atomic_dir / atomic_filename
                
                # 避免重名
                counter = 1
                while atomic_path.exists():
                    atomic_filename = f"{folder_name}_{safe_title}_{counter}.md"
                    atomic_path = self.atomic_dir / atomic_filename
                    counter += 1
                
                with open(atomic_path, 'w', encoding='utf-8') as f:
                    f.write(atomic_content)
                
                self.all_atomic_notes.append({
                    'title': section['title'],
                    'filename': atomic_filename,
                    'source': section['source_file'],
                    'folder': folder_name,
                    'related_count': len(related_notes)
                })
                
                print(f"      ✓ {safe_title} (关联 {len(related_notes)} 个笔记)")
    
    def create_index(self):
        """创建索引"""
        index_content = f"""# 特定文件夹原子笔记索引

生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
原子笔记数量: {len(self.all_atomic_notes)}

## 统计信息
- 总原子笔记: {len(self.all_atomic_notes)} 个
- 有关联笔记: {len([n for n in self.all_atomic_notes if n['related_count'] > 0])} 个
- 无关联笔记: {len([n for n in self.all_atomic_notes if n['related_count'] == 0])} 个

## 按文件夹分组

"""
        
        # 按文件夹分组
        by_folder = {}
        for note in self.all_atomic_notes:
            folder = note['folder']
            if folder not in by_folder:
                by_folder[folder] = []
            by_folder[folder].append(note)
        
        for folder, notes in sorted(by_folder.items()):
            index_content += f"### {folder} ({len(notes)}个)\n"
            for note in notes:
                filename_without_ext = note['filename'][:-3]
                index_content += f"- [[{filename_without_ext}]] - {note['title']} (关联{note['related_count']}个)\n"
            index_content += "\n"
        
        with open(self.output_dir / "特定文件夹原子笔记索引.md", 'w', encoding='utf-8') as f:
            f.write(index_content)
    
    def run(self):
        """运行生成器"""
        print(f"特定文件夹原子笔记生成器")
        print(f"目标文件夹: {', '.join(self.target_folders)}")
        print(f"输出目录: {self.output_dir}")
        print("=" * 50)
        
        # 扫描已有笔记
        self.scan_existing_notes()
        
        # 处理每个目标文件夹
        for folder_name in self.target_folders:
            self.process_folder(folder_name)
        
        # 创建索引
        print(f"\n创建索引...")
        self.create_index()
        
        print(f"\n✅ 完成！")
        print(f"📝 生成了 {len(self.all_atomic_notes)} 个原子笔记")
        print(f"📁 保存在: {self.atomic_dir}")
        print(f"📊 索引文件: {self.output_dir / '特定文件夹原子笔记索引.md'}")

if __name__ == "__main__":
    # 配置路径
    base_directory = Path(r"c:\Users\<USER>\OneDrive\obsidian笔记系统\战略与战术\智库辅助手册")
    
    # 运行生成器
    generator = SpecificFolderAtomicGenerator(base_directory)
    generator.run()
