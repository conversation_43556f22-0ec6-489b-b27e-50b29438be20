#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
智库手册孤立笔记处理器
专门处理智库辅助手册文件夹中链接少于2个的孤立笔记
"""

import os
import re
from pathlib import Path
from datetime import datetime

def extract_links(content):
    """提取现有链接"""
    return re.findall(r'\[\[([^\]]+)\]\]', content)

def find_isolated_notes_in_handbook():
    """找到智库辅助手册中的孤立笔记"""
    handbook_dir = Path(r"c:\Users\<USER>\OneDrive\obsidian笔记系统\战略与战术\智库辅助手册")
    isolated_notes = []
    
    # 排除脚本工具文件夹
    exclude_dirs = ['脚本工具', '.obsidian', '.trash']
    
    print(f"扫描目录: {handbook_dir}")
    
    for file_path in handbook_dir.rglob("*.md"):
        # 排除脚本工具等目录
        if any(ex in str(file_path) for ex in exclude_dirs):
            continue
            
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
        except:
            continue
        
        title = file_path.stem
        links = extract_links(content)
        
        # 只处理链接数少于2个的笔记
        if len(links) < 2:
            isolated_notes.append({
                'path': file_path,
                'title': title,
                'content': content,
                'links': links,
                'link_count': len(links),
                'relative_path': str(file_path.relative_to(handbook_dir))
            })
    
    return isolated_notes

def get_handbook_related_links():
    """获取智库手册相关的链接"""
    return [
        "✅3个通用生存法则-从王石案例",
        "创业者必知的「吸血条款」清单",
        "股权分配的5大致命陷阱",
        "融资谈判的10个关键条款",
        "公司治理结构设计",
        "风险控制体系建设",
        "团队管理核心法则",
        "商业模式设计思路",
        "财务管理基础知识",
        "法律合规要点总结",
        "投资人经典话术解码",
        "对赌协议深度解析",
        "董事会控制权争夺",
        "知识产权保护策略",
        "现金流管理技巧",
        "市场营销核心策略",
        "人才招聘与培养",
        "企业文化建设指南"
    ]

def select_relevant_links_for_note(note):
    """为笔记选择相关链接"""
    title = note['title'].lower()
    content = note['content'].lower()
    full_text = f"{title} {content}"
    
    all_links = get_handbook_related_links()
    selected = []
    
    # 基于关键词匹配
    keyword_mapping = {
        '股权': ["✅3个通用生存法则-从王石案例", "股权分配的5大致命陷阱", "董事会控制权争夺"],
        '融资': ["创业者必知的「吸血条款」清单", "融资谈判的10个关键条款", "对赌协议深度解析"],
        '投资': ["投资人经典话术解码", "融资谈判的10个关键条款"],
        '法律': ["法律合规要点总结", "知识产权保护策略", "创业者必知的「吸血条款」清单"],
        '风险': ["风险控制体系建设", "法律合规要点总结"],
        '管理': ["团队管理核心法则", "公司治理结构设计", "企业文化建设指南"],
        '团队': ["团队管理核心法则", "人才招聘与培养", "企业文化建设指南"],
        '财务': ["财务管理基础知识", "现金流管理技巧", "商业模式设计思路"],
        '市场': ["市场营销核心策略", "商业模式设计思路"]
    }
    
    # 根据关键词匹配选择链接
    for keyword, related_links in keyword_mapping.items():
        if keyword in full_text:
            for link in related_links:
                if link not in note['links'] and link not in selected:
                    selected.append(link)
                    if len(selected) >= 3:
                        break
            if len(selected) >= 3:
                break
    
    # 如果没有匹配到足够的链接，添加通用链接
    if len(selected) < 3:
        default_links = [
            "✅3个通用生存法则-从王石案例",
            "创业者必知的「吸血条款」清单", 
            "团队管理核心法则"
        ]
        for link in default_links:
            if link not in note['links'] and link not in selected:
                selected.append(link)
                if len(selected) >= 3:
                    break
    
    return selected

def add_links_to_note(note, new_links):
    """为笔记添加链接"""
    if not new_links:
        return False
    
    content = note['content']
    
    # 检查是否已有相关笔记部分
    if '## 相关笔记' in content:
        return False  # 已有相关笔记部分，跳过
    
    # 添加相关笔记部分
    content += f"\n\n---\n\n## 相关笔记\n"
    content += f"*智库手册链接于 {datetime.now().strftime('%Y-%m-%d')}*\n\n"
    
    for link in new_links:
        content += f"- [[{link}]]\n"
    
    # 保存文件
    try:
        with open(note['path'], 'w', encoding='utf-8') as f:
            f.write(content)
        return True
    except:
        return False

def main():
    """主函数"""
    print("智库手册孤立笔记处理器")
    print("=" * 35)
    
    # 找到孤立笔记
    print("查找智库辅助手册中的孤立笔记...")
    isolated_notes = find_isolated_notes_in_handbook()
    
    # 按链接数分组显示
    zero_links = [n for n in isolated_notes if n['link_count'] == 0]
    one_link = [n for n in isolated_notes if n['link_count'] == 1]
    
    print(f"\n发现孤立笔记:")
    print(f"  0个链接: {len(zero_links)}个")
    print(f"  1个链接: {len(one_link)}个")
    print(f"  总计: {len(isolated_notes)}个")
    
    if len(isolated_notes) == 0:
        print("🎉 智库辅助手册中没有孤立笔记！")
        return
    
    # 显示孤立笔记列表
    print(f"\n孤立笔记列表:")
    for note in isolated_notes:
        print(f"  - {note['title']} ({note['link_count']}个链接) - {note['relative_path']}")
    
    # 处理每个孤立笔记
    print(f"\n开始处理孤立笔记...")
    success_count = 0
    
    for i, note in enumerate(isolated_notes):
        print(f"\n处理 {i+1}/{len(isolated_notes)}: {note['title']}")
        print(f"  当前链接数: {note['link_count']}")
        print(f"  文件路径: {note['relative_path']}")
        
        # 选择相关链接
        new_links = select_relevant_links_for_note(note)
        
        if new_links:
            if add_links_to_note(note, new_links):
                success_count += 1
                print(f"  ✓ 成功添加了 {len(new_links)} 个链接:")
                for link in new_links:
                    print(f"    - {link}")
            else:
                print(f"  ✗ 添加失败（可能已有相关笔记部分）")
        else:
            print(f"  - 未找到合适链接")
    
    print(f"\n✅ 智库手册处理完成！")
    print(f"📊 发现孤立笔记: {len(isolated_notes)}个")
    print(f"🔗 成功处理: {success_count}个")
    print(f"📈 成功率: {success_count/len(isolated_notes)*100:.1f}%")

if __name__ == "__main__":
    main()
