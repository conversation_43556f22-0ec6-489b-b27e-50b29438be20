#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
零链接笔记终结者
专门处理完全没有链接的笔记，确保每个笔记都有至少2-3个链接
"""

import os
import re
from pathlib import Path
from datetime import datetime

def extract_links(content):
    """提取现有链接"""
    return re.findall(r'\[\[([^\]]+)\]\]', content)

def extract_keywords(text):
    """提取关键词"""
    keywords = [
        '股权', '股份', '股东', '控制权', '投票权', '分红权',
        '融资', '投资', '估值', '对赌', '清算优先权', '领售权',
        '法律', '合规', '风险', '合同', '协议', '知识产权',
        '财务', '现金流', '利润', '成本', '税务', '资金',
        '管理', '战略', '运营', '团队', '领导', '组织',
        '市场', '营销', '客户', '产品', '品牌', '销售',
        '技术', '创新', '研发', '开发', '设计', '专利'
    ]
    
    found = []
    text_lower = text.lower()
    for keyword in keywords:
        if keyword in text_lower:
            found.append(keyword)
    return found

def find_zero_link_notes(base_dir):
    """找到零链接笔记"""
    zero_link_notes = []
    all_notes = {}
    exclude_dirs = ['默写本', '.obsidian', '.trash', '原子笔记', '知识卡片', 'template']
    
    for file_path in base_dir.rglob("*.md"):
        if any(ex in str(file_path).lower() for ex in exclude_dirs):
            continue
            
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
        except:
            continue
        
        title = file_path.stem
        links = extract_links(content)
        keywords = extract_keywords(f"{title} {content}")
        
        note_info = {
            'path': file_path,
            'title': title,
            'content': content,
            'links': links,
            'link_count': len(links),
            'keywords': keywords,
            'word_count': len(content)
        }
        
        all_notes[title] = note_info
        
        # 零链接笔记
        if len(links) == 0:
            zero_link_notes.append(note_info)
    
    return zero_link_notes, all_notes

def find_best_matches(target_note, all_notes, max_results=4):
    """为零链接笔记找到最佳匹配"""
    matches = []
    target_keywords = set(target_note['keywords'])
    target_words = set([w.lower() for w in target_note['title'].split() if len(w) > 2])
    
    for other_title, other_note in all_notes.items():
        if other_title == target_note['title']:
            continue
        
        other_keywords = set(other_note['keywords'])
        other_words = set([w.lower() for w in other_note['title'].split() if len(w) > 2])
        
        score = 0
        
        # 关键词匹配（权重最高）
        keyword_overlap = len(target_keywords & other_keywords)
        score += keyword_overlap * 15
        
        # 标题词匹配
        title_overlap = len(target_words & other_words)
        score += title_overlap * 12
        
        # 标题包含关系
        for word in target_words:
            if word in other_note['title'].lower():
                score += 8
        
        # 关键词在对方标题中出现
        for keyword in target_keywords:
            if keyword in other_note['title'].lower():
                score += 10
        
        # 内容相似度（简单检查前200字符）
        target_sample = target_note['content'][:200].lower()
        other_sample = other_note['content'][:200].lower()
        common_words = set(target_sample.split()) & set(other_sample.split())
        if len(common_words) > 5:
            score += len(common_words)
        
        if score > 0:  # 任何相关性都考虑
            matches.append((other_note, score))
    
    # 如果没找到匹配，使用热门笔记
    if len(matches) < 2:
        # 找到被链接最多的笔记作为备选
        all_links = []
        for note in all_notes.values():
            all_links.extend(note['links'])
        
        link_count = {}
        for link in all_links:
            if link in all_notes:  # 确保链接的笔记存在
                link_count[link] = link_count.get(link, 0) + 1
        
        popular_notes = sorted(link_count.items(), key=lambda x: x[1], reverse=True)[:10]
        
        # 从热门笔记中选择与当前笔记最相关的
        for note_title, _ in popular_notes:
            if note_title in all_notes and note_title != target_note['title']:
                other_note = all_notes[note_title]
                # 简单的相关性检查
                simple_score = 0
                for keyword in target_keywords:
                    if keyword in other_note['title'].lower() or keyword in other_note['content'][:300].lower():
                        simple_score += 5
                
                if simple_score > 0 or len(matches) < 2:  # 如果有相关性或者匹配太少
                    matches.append((other_note, simple_score + 1))  # 给一个基础分数
                
                if len(matches) >= 4:
                    break
    
    # 排序并返回
    matches.sort(key=lambda x: x[1], reverse=True)
    return matches[:max_results]

def add_comprehensive_links(note, matched_notes):
    """为笔记添加全面的链接"""
    if not matched_notes:
        return False
    
    content = note['content']
    
    # 添加相关笔记部分
    content += f"\n\n---\n\n## 相关笔记\n"
    content += f"*智能链接于 {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}*\n\n"
    
    for matched_note, score in matched_notes:
        content += f"- [[{matched_note['title']}]]\n"
    
    # 如果有关键词，添加关键词说明
    if note['keywords']:
        content += f"\n## 关键词\n"
        content += f"{', '.join(note['keywords'])}\n"
    
    # 保存文件
    try:
        with open(note['path'], 'w', encoding='utf-8') as f:
            f.write(content)
        return True
    except Exception as e:
        print(f"保存失败: {e}")
        return False

def main():
    """主函数"""
    base_dir = Path(r"c:\Users\<USER>\OneDrive\obsidian笔记系统\战略与战术")
    
    print("零链接笔记终结者")
    print("=" * 30)
    
    # 找到零链接笔记
    print("查找零链接笔记...")
    zero_link_notes, all_notes = find_zero_link_notes(base_dir)
    print(f"找到 {len(zero_link_notes)} 个零链接笔记")
    print(f"总笔记数: {len(all_notes)} 个")
    
    if len(zero_link_notes) == 0:
        print("🎉 太棒了！没有零链接笔记了！")
        return
    
    # 为每个零链接笔记添加链接
    print("\n开始终结零链接笔记...")
    success_count = 0
    
    for i, note in enumerate(zero_link_notes):
        print(f"处理 {i+1}/{len(zero_link_notes)}: {note['title'][:50]}...")
        
        # 找到最佳匹配
        matched_notes = find_best_matches(note, all_notes)
        
        if matched_notes:
            if add_comprehensive_links(note, matched_notes):
                success_count += 1
                print(f"  ✓ 添加了 {len(matched_notes)} 个链接")
            else:
                print(f"  ✗ 添加失败")
        else:
            print(f"  - 未找到匹配笔记")
    
    print(f"\n🎯 任务完成！")
    print(f"📊 处理了 {len(zero_link_notes)} 个零链接笔记")
    print(f"🔗 成功为 {success_count} 个笔记添加了链接")
    print(f"📈 成功率: {success_count/len(zero_link_notes)*100:.1f}%")
    
    if success_count == len(zero_link_notes):
        print(f"🏆 完美！所有零链接笔记都已处理！")
    else:
        remaining = len(zero_link_notes) - success_count
        print(f"⚠️  还有 {remaining} 个笔记需要手动处理")

if __name__ == "__main__":
    main()
