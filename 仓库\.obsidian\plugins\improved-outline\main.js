const { <PERSON>lug<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>downView } = require('obsidian');

const VIEW_TYPE_IMPROVED_OUTLINE = 'improved-outline-view';

class ImprovedOutlineView extends ItemView {
    constructor(leaf) {
        super(leaf);
        this.currentFile = null;
        this.headings = [];
        this.refreshInterval = null;
    }

    getViewType() {
        return VIEW_TYPE_IMPROVED_OUTLINE;
    }

    getDisplayText() {
        return '改进版大纲';
    }

    getIcon() {
        return 'list';
    }

    async onOpen() {
        const container = this.containerEl.children[1];
        container.empty();
        container.createEl('div', { cls: 'improved-outline-container' });
        
        // 监听文件切换
        this.registerEvent(
            this.app.workspace.on('active-leaf-change', () => {
                this.updateOutline();
            })
        );
        
        // 监听文件内容变化
        this.registerEvent(
            this.app.workspace.on('editor-change', () => {
                this.scheduleUpdate();
            })
        );
        
        this.updateOutline();
    }

    scheduleUpdate() {
        if (this.refreshInterval) {
            clearTimeout(this.refreshInterval);
        }
        this.refreshInterval = setTimeout(() => {
            this.updateOutline();
        }, 500);
    }

    async updateOutline() {
        const activeView = this.app.workspace.getActiveViewOfType(MarkdownView);
        if (!activeView || !activeView.file) {
            this.displayNoFile();
            return;
        }

        const file = activeView.file;
        if (this.currentFile === file) {
            // 文件没变，但内容可能变了，重新解析
        }
        this.currentFile = file;

        try {
            const content = await this.app.vault.read(file);
            this.headings = this.parseHeadings(content);
            this.renderOutline();
        } catch (error) {
            console.error('读取文件失败:', error);
            this.displayError('无法读取文件内容');
        }
    }

    parseHeadings(content) {
        const headings = [];
        const lines = content.split('\n');
        
        for (let i = 0; i < lines.length; i++) {
            const line = lines[i].trim();
            
            // 标准 Markdown 标题 (# ## ### 等)
            const markdownMatch = line.match(/^(#{1,6})\s+(.+)$/);
            if (markdownMatch) {
                // 移除标题文本中的星号
                const cleanText = markdownMatch[2].trim().replace(/\*/g, '');
                headings.push({
                    level: markdownMatch[1].length,
                    text: cleanText,
                    line: i + 1,
                    type: 'markdown'
                });
                continue;
            }
            
            // Setext 风格标题 (下划线)
            if (i < lines.length - 1) {
                const nextLine = lines[i + 1].trim();
                if (nextLine.match(/^=+$/)) {
                    // 移除标题文本中的星号
                    const cleanText = line.replace(/\*/g, '');
                    headings.push({
                        level: 1,
                        text: cleanText,
                        line: i + 1,
                        type: 'setext-h1'
                    });
                    i++; // 跳过下一行
                    continue;
                }
                if (nextLine.match(/^-+$/)) {
                    // 移除标题文本中的星号
                    const cleanText = line.replace(/\*/g, '');
                    headings.push({
                        level: 2,
                        text: cleanText,
                        line: i + 1,
                        type: 'setext-h2'
                    });
                    i++; // 跳过下一行
                    continue;
                }
            }
            
            // HTML 标题标签
            const htmlMatch = line.match(/<h([1-6])[^>]*>(.+?)<\/h[1-6]>/i);
            if (htmlMatch) {
                // 移除标题文本中的星号
                const cleanText = htmlMatch[2].replace(/<[^>]*>/g, '').trim().replace(/\*/g, '');
                headings.push({
                    level: parseInt(htmlMatch[1]),
                    text: cleanText,
                    line: i + 1,
                    type: 'html'
                });
                continue;
            }
            
            // 自定义格式：【标题】或 [标题]
            const customMatch = line.match(/^\s*[【\[](.+?)[】\]]\s*$/);
            if (customMatch) {
                // 移除标题文本中的星号
                const cleanText = customMatch[1].trim().replace(/\*/g, '');
                headings.push({
                    level: 3, // 默认作为三级标题
                    text: cleanText,
                    line: i + 1,
                    type: 'custom'
                });
                continue;
            }
            
            // 数字编号标题 (1. 2. 3. 等)
            const numberedMatch = line.match(/^\s*(\d+\.\s+)(.+)$/);
            if (numberedMatch && numberedMatch[2].length > 3) {
                // 移除标题文本中的星号
                const cleanText = numberedMatch[2].trim().replace(/\*/g, '');
                headings.push({
                    level: 4,
                    text: cleanText,
                    line: i + 1,
                    type: 'numbered'
                });
                continue;
            }
            
            // 粗体文本作为标题，但不显示星号
            const boldMatch = line.match(/^\s*\*\*(.+?)\*\*\s*$/);
            if (boldMatch) {
                // 已经去除了星号
                const cleanText = boldMatch[1].trim().replace(/\*/g, '');
                headings.push({
                    level: 5,
                    text: cleanText,
                    line: i + 1,
                    type: 'bold'
                });
                continue;
            }
        }
        
        return headings;
    }

    renderOutline() {
        const container = this.containerEl.querySelector('.improved-outline-container');
        container.empty();
        
        if (this.headings.length === 0) {
            container.createEl('div', {
                text: '此文档没有找到标题',
                cls: 'improved-outline-empty'
            });
            return;
        }
        
        const outlineList = container.createEl('div', { cls: 'improved-outline-list' });
        
        this.headings.forEach((heading, index) => {
            const headingEl = outlineList.createEl('div', {
                cls: `improved-outline-item improved-outline-level-${heading.level} improved-outline-${heading.type}`
            });
            
            // 缩进
            headingEl.style.paddingLeft = `${(heading.level - 1) * 16}px`;
            
            // 创建层级前缀
            let levelPrefix = '';
            if (heading.level === 1) {
                levelPrefix = '一、';
            } else if (heading.level === 2) {
                levelPrefix = '二、';
            } else if (heading.level === 3) {
                levelPrefix = '三、';
            } else if (heading.level === 4) {
                levelPrefix = '四、';
            } else if (heading.level === 5) {
                levelPrefix = '五、';
            } else if (heading.level === 6) {
                levelPrefix = '六、';
            }
            
            // 添加层级前缀
            if (levelPrefix) {
                const prefixEl = headingEl.createEl('span', {
                    text: levelPrefix,
                    cls: 'improved-outline-level-prefix'
                });
            }
            
            // 标题文本
            const textEl = headingEl.createEl('span', {
                text: heading.text,
                cls: 'improved-outline-text'
            });
            
            // 行号
            const lineEl = headingEl.createEl('span', {
                text: `L${heading.line}`,
                cls: 'improved-outline-line'
            });
            
            // 类型标识 - 只在非粗体类型时显示
            if (heading.type !== 'bold') {
                const typeEl = headingEl.createEl('span', {
                    text: this.getTypeIcon(heading.type),
                    cls: 'improved-outline-type'
                });
            }
            
            // 点击跳转
            headingEl.addEventListener('click', () => {
                this.jumpToLine(heading.line);
            });
            
            // 悬停效果
            headingEl.addEventListener('mouseenter', () => {
                headingEl.addClass('improved-outline-hover');
            });
            
            headingEl.addEventListener('mouseleave', () => {
                headingEl.removeClass('improved-outline-hover');
            });
        });
    }
    
    getTypeIcon(type) {
        const icons = {
            'markdown': '#',
            'setext-h1': '=',
            'setext-h2': '-',
            'html': '<>',
            'custom': '≡',
            'numbered': '1.',
            'bold': 'B'
        };
        return icons[type] || '?';
    }

    jumpToLine(lineNumber) {
        const activeView = this.app.workspace.getActiveViewOfType(MarkdownView);
        if (activeView && activeView.editor) {
            const editor = activeView.editor;
            const line = Math.max(0, lineNumber - 1);
            editor.setCursor({ line, ch: 0 });
            editor.scrollIntoView({ from: { line, ch: 0 }, to: { line, ch: 0 } }, true);
            
            // 高亮当前行
            setTimeout(() => {
                const lineEl = activeView.containerEl.querySelector(`.cm-line:nth-child(${lineNumber})`);
                if (lineEl) {
                    lineEl.style.backgroundColor = 'var(--text-selection)';
                    setTimeout(() => {
                        lineEl.style.backgroundColor = '';
                    }, 1000);
                }
            }, 100);
        }
    }

    displayNoFile() {
        const container = this.containerEl.querySelector('.improved-outline-container');
        container.empty();
        container.createEl('div', {
            text: '请打开一个 Markdown 文件',
            cls: 'improved-outline-empty'
        });
    }

    displayError(message) {
        const container = this.containerEl.querySelector('.improved-outline-container');
        container.empty();
        container.createEl('div', {
            text: message,
            cls: 'improved-outline-error'
        });
    }

    async onClose() {
        if (this.refreshInterval) {
            clearTimeout(this.refreshInterval);
        }
    }
}

class ImprovedOutlinePlugin extends Plugin {
    async onload() {
        console.log('改进版大纲插件已加载');
        
        // 注册视图
        this.registerView(
            VIEW_TYPE_IMPROVED_OUTLINE,
            (leaf) => new ImprovedOutlineView(leaf)
        );
        
        // 添加命令：打开大纲视图
        this.addCommand({
            id: 'open-improved-outline',
            name: '打开改进版大纲',
            callback: () => {
                this.activateView();
            }
        });
        
        // 添加功能区图标
        this.addRibbonIcon('list', '改进版大纲', () => {
            this.activateView();
        });
        
        // 启动时自动打开大纲视图
        this.app.workspace.onLayoutReady(() => {
            this.activateView();
        });
    }
    
    async activateView() {
        const { workspace } = this.app;
        
        let leaf = null;
        const leaves = workspace.getLeavesOfType(VIEW_TYPE_IMPROVED_OUTLINE);
        
        if (leaves.length > 0) {
            // 如果已经存在，就激活它
            leaf = leaves[0];
        } else {
            // 否则在右侧边栏创建新的
            leaf = workspace.getRightLeaf(false);
            await leaf.setViewState({ type: VIEW_TYPE_IMPROVED_OUTLINE, active: true });
        }
        
        workspace.revealLeaf(leaf);
    }
    
    onunload() {
        console.log('改进版大纲插件已卸载');
    }
}

module.exports = ImprovedOutlinePlugin;