#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
智库手册彻底链接器
彻底处理智库辅助手册中所有孤立笔记，不管任何条件
"""

import os
import re
from pathlib import Path
from datetime import datetime

def extract_links(content):
    """提取现有链接"""
    return re.findall(r'\[\[([^\]]+)\]\]', content)

def scan_all_handbook_notes():
    """扫描智库辅助手册中的所有笔记"""
    handbook_dir = Path(r"c:\Users\<USER>\OneDrive\obsidian笔记系统\战略与战术\智库辅助手册")
    all_notes = []

    print(f"扫描目录: {handbook_dir}")

    for file_path in handbook_dir.rglob("*.md"):
        # 排除脚本工具目录和日记类笔记
        if '脚本工具' in str(file_path):
            continue

        # 排除日记类笔记（日期格式的文件名）
        filename = file_path.stem
        if re.match(r'^\d{4}-\d{2}-\d{2}', filename):  # 2024-01-01 格式
            continue
        if re.match(r'^\d{4}\d{2}\d{2}', filename):     # 20240101 格式
            continue
        if filename.lower() in ['daily notes', 'daily', 'journal', '日记', '日志']:
            continue
            
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
        except:
            continue
        
        title = file_path.stem
        links = extract_links(content)
        
        all_notes.append({
            'path': file_path,
            'title': title,
            'content': content,
            'links': links,
            'link_count': len(links),
            'relative_path': str(file_path.relative_to(handbook_dir))
        })
    
    return all_notes

def get_universal_links():
    """获取通用链接"""
    return [
        "✅3个通用生存法则-从王石案例",
        "创业者必知的「吸血条款」清单",
        "股权分配的5大致命陷阱",
        "团队管理核心法则",
        "风险控制体系建设"
    ]

def force_add_links(note):
    """强制为笔记添加链接"""
    content = note['content']
    universal_links = get_universal_links()
    
    # 选择不在现有链接中的通用链接
    new_links = []
    for link in universal_links:
        if link not in note['links']:
            new_links.append(link)
            if len(new_links) >= 3:  # 最多添加3个
                break
    
    if not new_links:
        return False
    
    # 强制添加，不管是否已有相关笔记部分
    if '## 相关笔记' in content:
        # 在现有相关笔记部分添加
        lines = content.split('\n')
        new_lines = []
        found_section = False
        
        for line in lines:
            new_lines.append(line)
            if line.startswith('## 相关笔记') and not found_section:
                found_section = True
                new_lines.append(f"*彻底增强于 {datetime.now().strftime('%Y-%m-%d')}*")
                for link in new_links:
                    new_lines.append(f"- [[{link}]]")
        
        content = '\n'.join(new_lines)
    else:
        # 添加新的相关笔记部分
        content += f"\n\n---\n\n## 相关笔记\n"
        content += f"*彻底链接于 {datetime.now().strftime('%Y-%m-%d')}*\n\n"
        for link in new_links:
            content += f"- [[{link}]]\n"
    
    # 保存文件
    try:
        with open(note['path'], 'w', encoding='utf-8') as f:
            f.write(content)
        return True
    except:
        return False

def main():
    """主函数"""
    print("智库手册彻底链接器")
    print("=" * 30)
    
    # 扫描所有笔记
    all_notes = scan_all_handbook_notes()
    print(f"找到总笔记数: {len(all_notes)}")
    
    # 统计链接分布
    link_stats = {}
    isolated_notes = []
    
    for note in all_notes:
        count = note['link_count']
        if count not in link_stats:
            link_stats[count] = []
        link_stats[count].append(note)
        
        # 链接数少于3个的都视为需要处理
        if count < 3:
            isolated_notes.append(note)
    
    print(f"\n当前链接分布:")
    for i in range(10):  # 显示0-9个链接的分布
        if i in link_stats:
            print(f"  {i}个链接: {len(link_stats[i])}个笔记")
    
    more_than_9 = sum(len(notes) for count, notes in link_stats.items() if count >= 10)
    if more_than_9 > 0:
        print(f"  10+个链接: {more_than_9}个笔记")
    
    print(f"\n需要处理的笔记(少于3个链接): {len(isolated_notes)}个")
    
    if len(isolated_notes) == 0:
        print("🎉 所有笔记都有足够的链接！")
        return
    
    # 显示部分孤立笔记
    print(f"\n孤立笔记示例:")
    for i, note in enumerate(isolated_notes[:10]):
        print(f"  {i+1}. {note['title']} ({note['link_count']}个链接)")
    if len(isolated_notes) > 10:
        print(f"  ... 还有{len(isolated_notes)-10}个")
    
    # 确认是否处理
    print(f"\n⚠️  将为 {len(isolated_notes)} 个笔记强制添加链接")
    confirm = input("确认执行吗？(y/N): ").strip().lower()
    
    if confirm != 'y':
        print("已取消操作")
        return
    
    # 彻底处理所有孤立笔记
    print(f"\n开始彻底处理...")
    success_count = 0
    
    for i, note in enumerate(isolated_notes):
        if i % 20 == 0:
            print(f"进度: {i+1}/{len(isolated_notes)}")
        
        if force_add_links(note):
            success_count += 1
    
    print(f"\n🎯 彻底处理完成！")
    print(f"📊 需要处理: {len(isolated_notes)}个")
    print(f"🔗 成功处理: {success_count}个")
    print(f"📈 成功率: {success_count/len(isolated_notes)*100:.1f}%")
    
    if success_count == len(isolated_notes):
        print(f"🏆 完美！所有孤立笔记都已彻底处理！")

if __name__ == "__main__":
    main()
