/* 改进版大纲插件样式 */

.improved-outline-container {
    padding: 10px;
    height: 100%;
    overflow-y: auto;
}

.improved-outline-list {
    font-size: 14px;
    line-height: 1.4;
}

.improved-outline-item {
    display: flex;
    align-items: center;
    padding: 4px 8px;
    margin: 2px 0;
    border-radius: 4px;
    cursor: pointer;
    transition: all 0.2s ease;
    position: relative;
}

.improved-outline-item:hover,
.improved-outline-hover {
    background-color: var(--background-modifier-hover);
    transform: translateX(2px);
}

.improved-outline-item:active {
    background-color: var(--background-modifier-active-hover);
}

.improved-outline-text {
    flex: 1;
    margin-right: 8px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.improved-outline-line {
    font-size: 11px;
    color: var(--text-muted);
    margin-right: 6px;
    min-width: 30px;
    text-align: right;
}

.improved-outline-type {
    font-size: 10px;
    color: var(--text-accent);
    font-weight: bold;
    min-width: 20px;
    text-align: center;
    background: var(--background-modifier-border);
    border-radius: 3px;
    padding: 1px 3px;
}

/* 不同级别的标题样式 */
.improved-outline-level-1 {
    font-weight: bold;
    font-size: 15px;
}

.improved-outline-level-1 .improved-outline-text {
    color: var(--text-normal);
}

.improved-outline-level-2 {
    font-weight: 600;
    font-size: 14px;
}

.improved-outline-level-2 .improved-outline-text {
    color: var(--text-normal);
}

.improved-outline-level-3 {
    font-weight: 500;
}

.improved-outline-level-3 .improved-outline-text {
    color: var(--text-muted);
}

.improved-outline-level-4,
.improved-outline-level-5,
.improved-outline-level-6 {
    font-weight: normal;
    font-size: 13px;
}

.improved-outline-level-4 .improved-outline-text,
.improved-outline-level-5 .improved-outline-text,
.improved-outline-level-6 .improved-outline-text {
    color: var(--text-faint);
}

/* 不同类型的标题样式 */
.improved-outline-markdown .improved-outline-type {
    background: var(--color-blue);
    color: white;
}

.improved-outline-setext-h1 .improved-outline-type,
.improved-outline-setext-h2 .improved-outline-type {
    background: var(--color-green);
    color: white;
}

.improved-outline-html .improved-outline-type {
    background: var(--color-orange);
    color: white;
}

.improved-outline-custom .improved-outline-type {
    background: var(--color-purple);
    color: white;
}

.improved-outline-numbered .improved-outline-type {
    background: var(--color-yellow);
    color: var(--text-normal);
}

.improved-outline-bold .improved-outline-type {
    background: var(--color-red);
    color: white;
}

/* 层级前缀样式 */
.improved-outline-level-prefix {
    color: var(--text-muted);
    font-size: 0.8em;
    margin-right: 4px;
}

/* 空状态和错误状态 */
.improved-outline-empty,
.improved-outline-error {
    text-align: center;
    padding: 20px;
    color: var(--text-muted);
    font-style: italic;
}

.improved-outline-error {
    color: var(--text-error);
}

/* 滚动条样式 */
.improved-outline-container::-webkit-scrollbar {
    width: 6px;
}

.improved-outline-container::-webkit-scrollbar-track {
    background: var(--background-secondary);
}

.improved-outline-container::-webkit-scrollbar-thumb {
    background: var(--background-modifier-border);
    border-radius: 3px;
}

.improved-outline-container::-webkit-scrollbar-thumb:hover {
    background: var(--background-modifier-border-hover);
}

/* 响应式设计 */
@media (max-width: 768px) {
    .improved-outline-item {
        padding: 6px 4px;
    }
    
    .improved-outline-line {
        display: none;
    }
    
    .improved-outline-type {
        font-size: 9px;
        min-width: 16px;
    }
}

/* 深色主题适配 */
.theme-dark .improved-outline-item:hover {
    background-color: rgba(255, 255, 255, 0.1);
}

.theme-dark .improved-outline-type {
    background: rgba(255, 255, 255, 0.2);
}

/* 高对比度模式 */
@media (prefers-contrast: high) {
    .improved-outline-item {
        border: 1px solid var(--background-modifier-border);
    }
    
    .improved-outline-item:hover {
        border-color: var(--text-accent);
    }
}

/* 动画效果 */
@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(-10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.improved-outline-item {
    animation: fadeIn 0.3s ease-out;
}

/* 焦点样式 */
.improved-outline-item:focus {
    outline: 2px solid var(--text-accent);
    outline-offset: 2px;
}

/* 选中状态 */
.improved-outline-item.is-active {
    background-color: var(--background-modifier-active);
    border-left: 3px solid var(--text-accent);
    padding-left: 5px;
}