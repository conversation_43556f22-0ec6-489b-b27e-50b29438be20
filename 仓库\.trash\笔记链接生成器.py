import os
import re
from pathlib import Path
import json
from datetime import datetime
from tqdm import tqdm

class NoteLinker:
    def __init__(self, root_dir):
        self.root_dir = Path(root_dir)
        self.backup_dir = self.root_dir / '.note_linker_backups'
        self.backup_dir.mkdir(exist_ok=True)
        self.history_file = self.backup_dir / 'link_history.json'
        self.history = self.load_history()
        
    def load_history(self):
        """加载历史记录"""
        if self.history_file.exists():
            with open(self.history_file, 'r', encoding='utf-8') as f:
                return json.load(f)
        return []
    
    def save_history(self):
        """保存历史记录"""
        with open(self.history_file, 'w', encoding='utf-8') as f:
            json.dump(self.history, f, ensure_ascii=False, indent=2)
    
    def backup_file(self, file_path):
        """备份文件"""
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        backup_path = self.backup_dir / f'{file_path.name}.{timestamp}.bak'
        with open(file_path, 'r', encoding='utf-8') as src:
            content = src.read()
            with open(backup_path, 'w', encoding='utf-8') as dst:
                dst.write(content)
        return backup_path
    
    def find_all_notes(self):
        """查找所有Markdown笔记文件"""
        return list(self.root_dir.rglob('*.md'))
    
    def extract_topics(self, content):
        """提取笔记中的主题词"""
        # 提取标题
        title_pattern = re.compile(r'^#\s+(.+)$', re.MULTILINE)
        titles = title_pattern.findall(content)
        
        # 提取加粗文本
        bold_pattern = re.compile(r'\*\*(.+?)\*\*')
        bold_texts = bold_pattern.findall(content)
        
        # 提取其他关键词（可以根据需要扩展）
        keywords = set(titles + bold_texts)
        return keywords
    
    def process_files_in_batches(self, files, batch_size=50):
        """批量处理文件"""
        for i in range(0, len(files), batch_size):
            yield files[i:i + batch_size]
    
    def create_links(self, dry_run=True):
        """创建笔记之间的链接"""
        notes = self.find_all_notes()
        print(f'找到 {len(notes)} 个Markdown文件')
        note_topics = {}
        
        # 第一遍扫描：收集所有笔记的主题词
        print('正在扫描笔记主题词...')
        with tqdm(total=len(notes), desc='扫描进度') as pbar:
            for batch in self.process_files_in_batches(notes):
                for note in batch:
                    try:
                        with open(note, 'r', encoding='utf-8') as f:
                            content = f.read()
                            topics = self.extract_topics(content)
                            if topics:
                                note_topics[note] = topics
                                pbar.set_postfix({'主题词': len(topics)})
                    except Exception as e:
                        print(f'\n处理文件 {note.name} 时出错: {str(e)}')
                    pbar.update(1)
        
        changes = []
        # 第二遍扫描：建立链接
        print('\n开始建立笔记链接...')
        with tqdm(total=len(notes), desc='链接进度') as pbar:
            for batch in self.process_files_in_batches(notes):
                for note in batch:
                    try:
                        with open(note, 'r', encoding='utf-8') as f:
                            content = f.read()
                        
                        new_content = content
                        links_added = 0
                        for other_note, topics in note_topics.items():
                            if other_note == note:
                                continue
                                
                            for topic in topics:
                                pattern = f'(?<!\\[)(?<!\\]\\()({re.escape(topic)})(?!\\]\\))(?!`)'  
                                if re.search(pattern, new_content):
                                    link = f'[[{other_note.name}|{topic}]]'
                                    new_content = re.sub(pattern, link, new_content)
                                    links_added += 1
                        
                        if new_content != content:
                            if not dry_run:
                                backup_path = self.backup_file(note)
                                with open(note, 'w', encoding='utf-8') as f:
                                    f.write(new_content)
                                changes.append({
                                    'file': str(note),
                                    'backup': str(backup_path),
                                    'timestamp': datetime.now().isoformat()
                                })
                            pbar.set_postfix({'新增链接': links_added})
                    except Exception as e:
                        print(f'\n处理文件 {note.name} 时出错: {str(e)}')
                    pbar.update(1)
        
        if not dry_run:
            self.history.append({
                'changes': changes,
                'timestamp': datetime.now().isoformat()
            })
            self.save_history()
            print(f'\n完成！共更新了 {len(changes)} 个文件')
        else:
            print(f'\n试运行完成，将会修改 {len(changes)} 个文件')
        
        return changes
    
    def undo_last_change(self):
        """撤销上一次的链接操作"""
        if not self.history:
            print('没有可撤销的操作')
            return
        
        last_change = self.history.pop()
        with tqdm(total=len(last_change['changes']), desc='撤销进度') as pbar:
            for change in last_change['changes']:
                try:
                    file_path = Path(change['file'])
                    backup_path = Path(change['backup'])
                    
                    if backup_path.exists():
                        with open(backup_path, 'r', encoding='utf-8') as src:
                            content = src.read()
                            with open(file_path, 'w', encoding='utf-8') as dst:
                                dst.write(content)
                        pbar.set_postfix({'文件': file_path.name})
                    else:
                        print(f'\n警告：找不到备份文件 {backup_path}')
                except Exception as e:
                    print(f'\n恢复文件 {file_path.name} 时出错: {str(e)}')
                pbar.update(1)
        
        self.save_history()
        print('\n撤销完成')

def main():
    # 使用示例
    root_dir = Path('c:/Users/<USER>/OneDrive/obsidian笔记系统/战略与战术/智库辅助手册')
    linker = NoteLinker(root_dir)
    
    # 先进行试运行，查看将要进行的更改
    print('===== 试运行模式 =====')
    linker.create_links(dry_run=True)
    
    # 询问是否继续
    response = input('\n是否继续进行实际更改？(y/n): ')
    if response.lower() == 'y':
        print('\n===== 开始创建链接 =====')
        changes = linker.create_links(dry_run=False)
    else:
        print('操作已取消')

if __name__ == '__main__':
    main()