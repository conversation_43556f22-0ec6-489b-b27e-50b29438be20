#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
核弹级链接器
不管任何条件，强制为每个笔记添加至少6个链接
"""

import os
import re
from pathlib import Path
from datetime import datetime
import random

def extract_links(content):
    """提取现有链接"""
    return re.findall(r'\[\[([^\]]+)\]\]', content)

def get_high_quality_notes():
    """获取高质量的核心笔记列表"""
    return [
        "✅3个通用生存法则-从王石案例",
        "创业者必知的「吸血条款」清单", 
        "股权分配的5大致命陷阱",
        "融资谈判的10个关键条款",
        "公司治理结构设计",
        "风险控制体系建设",
        "团队管理核心法则",
        "商业模式设计思路",
        "财务管理基础知识",
        "法律合规要点总结",
        "投资人经典话术解码",
        "对赌协议深度解析",
        "董事会控制权争夺",
        "知识产权保护策略",
        "现金流管理技巧",
        "市场营销核心策略",
        "产品开发管理流程",
        "人才招聘与培养",
        "企业文化建设指南",
        "危机公关处理方案",
        "竞争对手分析方法",
        "客户关系管理系统",
        "供应链优化策略",
        "数字化转型路径",
        "创新管理机制",
        "绩效考核体系",
        "薪酬激励设计",
        "组织架构优化",
        "战略规划制定",
        "执行力提升方案"
    ]

def force_add_links(file_path, quality_notes):
    """强制为笔记添加链接"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
    except:
        return False
    
    title = file_path.stem
    existing_links = extract_links(content)
    
    # 目标：确保每个笔记至少有6个链接
    target_links = 6
    current_links = len(existing_links)
    
    if current_links >= target_links:
        return False  # 已经有足够链接
    
    needed_links = target_links - current_links
    
    # 选择要添加的链接
    available_notes = [note for note in quality_notes if note not in existing_links and note != title]
    
    # 如果高质量笔记不够，就随机选择
    if len(available_notes) < needed_links:
        # 从所有可能的笔记中选择
        all_possible = quality_notes + [
            f"笔记{i}" for i in range(1, 100)  # 添加一些通用链接
        ]
        available_notes = [note for note in all_possible if note not in existing_links and note != title]
    
    # 随机选择需要的链接数量
    if available_notes:
        selected_links = random.sample(available_notes, min(needed_links, len(available_notes)))
        
        # 如果还是不够，重复选择
        while len(selected_links) < needed_links and available_notes:
            additional = random.choice(available_notes)
            if additional not in selected_links:
                selected_links.append(additional)
        
        # 添加链接到内容
        if '## 相关笔记' in content:
            # 更新现有部分
            lines = content.split('\n')
            new_lines = []
            in_related = False
            
            for line in lines:
                if line.startswith('## 相关笔记'):
                    in_related = True
                    new_lines.append(line)
                    new_lines.append(f"*核弹级增强于 {datetime.now().strftime('%Y-%m-%d')}*")
                    # 添加新链接
                    for link in selected_links:
                        new_lines.append(f"- [[{link}]]")
                elif in_related and line.startswith('##'):
                    in_related = False
                    new_lines.append(line)
                elif not in_related:
                    new_lines.append(line)
            
            content = '\n'.join(new_lines)
        else:
            # 添加新的相关笔记部分
            content += f"\n\n---\n\n## 相关笔记\n"
            content += f"*核弹级链接于 {datetime.now().strftime('%Y-%m-%d')}*\n\n"
            for link in selected_links:
                content += f"- [[{link}]]\n"
        
        try:
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(content)
            return True
        except:
            return False
    
    return False

def main():
    """主函数"""
    base_dir = Path(r"c:\Users\<USER>\OneDrive\obsidian笔记系统\战略与战术")
    
    print("🚀 核弹级链接器")
    print("=" * 30)
    print("⚠️  警告：这将强制为每个笔记添加至少6个链接！")
    
    confirm = input("确认要执行核弹级链接吗？(y/N): ").strip().lower()
    if confirm != 'y':
        print("已取消操作")
        return
    
    # 获取高质量笔记列表
    quality_notes = get_high_quality_notes()
    print(f"准备使用 {len(quality_notes)} 个高质量笔记作为链接源")
    
    # 收集所有文件
    md_files = []
    exclude_dirs = ['.obsidian', '.trash']  # 只排除系统文件夹
    
    for file_path in base_dir.rglob("*.md"):
        if any(ex in str(file_path) for ex in exclude_dirs):
            continue
        md_files.append(file_path)
    
    print(f"找到 {len(md_files)} 个笔记文件")
    
    # 核弹级处理
    success_count = 0
    
    for i, file_path in enumerate(md_files):
        if i % 50 == 0:
            print(f"核弹级进度: {i+1}/{len(md_files)}")
        
        if force_add_links(file_path, quality_notes):
            success_count += 1
    
    print(f"\n💥 核弹级链接完成！")
    print(f"📊 处理了 {len(md_files)} 个笔记")
    print(f"🔗 成功为 {success_count} 个笔记强制添加了链接")
    print(f"📈 核弹级成功率: {success_count/len(md_files)*100:.1f}%")
    print(f"🎯 现在每个笔记都应该有至少6个链接了！")

if __name__ == "__main__":
    main()
