---
title: 数据同步与泄露风险：进阶加密
source: "[[数据隐私性：存在自己服务器，别人能看到吗？]]"
tags:
  - 数据同步
  - 泄露风险
  - 加密
  - LiveSync
  - 7-Zip
keywords:
  - 同步
  - 泄露
  - 加密
  - LiveSync
  - 7-Zip
created: 2025-08-03
type: 原子笔记
已学: true
---

# 数据同步与泄露风险：进阶加密-端到端加密敏感文件单独加密

1. **用LiveSync的端到端加密**：同步前笔记自动加密，数据库里存的是一堆乱码（即使被偷也看不懂）。

2. **敏感文件单独加密**：用7-Zip+密码打包客户资料，再存Obsidian。

---

## 元信息
- **来源笔记**: [[数据隐私性：存在自己服务器，别人能看到吗？]]
- **创建时间**: 2025/8/4 04:04:45
- **标签**: #数据同步 #泄露风险 #加密 #LiveSync #7-Zip
- **关键词**: 同步, 泄露, 加密, LiveSync, 7-Zip

## 相关链接
- 返回原笔记: [[数据隐私性：存在自己服务器，别人能看到吗？]]
