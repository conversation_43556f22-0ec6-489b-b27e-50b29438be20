# 默写本—扫描数据去向和扫描敏感数据是两个不同但相关的技术问题 - 原子笔记索引

> 本索引由原子笔记切分工具自动生成
> 生成时间: 2025/7/30 12:03:22
> 原始笔记: [[默写本—扫描数据去向和扫描敏感数据是两个不同但相关的技术问题]]

## 统计信息
- 原始笔记: [[默写本—扫描数据去向和扫描敏感数据是两个不同但相关的技术问题]]
- 切分出的原子笔记数量: 12
- 生成时间: 2025/7/30 12:03:22

## 原子笔记列表

1. [[扫描敏感数据概述]] - 扫描敏感数据概述
2. [[扫描数据去向概述]] - 扫描数据去向概述
3. [[数据库传输监控]] - 数据库传输监控
4. [[API调用监控]] - API调用监控
5. [[云服务配置监控]] - 云服务配置监控
6. [[前端埋点监控]] - 前端埋点监控
7. [[日志分析低成本方案-Python分析Nginx日志，通过正则表达式检测数据出境到第三方服务]] - 日志分析低成本方案
8. [[网络层监控方案]] - 网络层监控方案
9. [[敏感数据扫描与数据去向联动]] - 敏感数据扫描与数据去向联动
10. [[现成工具整合建议]] - 现成工具整合建议
11. [[专利角度描述]] - 专利角度描述
12. [[关键提醒]] - 关键提醒

## 标签分类

### #敏感数据扫描
- [[扫描敏感数据概述]]

### #数据安全
- [[扫描敏感数据概述]]

### #数据流向
- [[扫描数据去向概述]]
- [[专利角度描述]]

### #跨境合规
- [[扫描数据去向概述]]

### #数据监控
- [[扫描数据去向概述]]

### #数据库监控
- [[数据库传输监控]]

### #日志分析
- [[数据库传输监控]]
- [[日志分析低成本方案-Python分析Nginx日志，通过正则表达式检测数据出境到第三方服务]]

### #数据同步
- [[数据库传输监控]]

### #API监控
- [[API调用监控]]

### #网络抓包
- [[API调用监控]]

### #数据调用
- [[API调用监控]]

### #云服务监控
- [[云服务配置监控]]

### #SDK日志
- [[云服务配置监控]]

### #配置审计
- [[云服务配置监控]]

### #前端监控
- [[前端埋点监控]]

### #埋点技术
- [[前端埋点监控]]

### #数据发送
- [[前端埋点监控]]

### #Python
- [[日志分析低成本方案-Python分析Nginx日志，通过正则表达式检测数据出境到第三方服务]]

### #数据出境
- [[日志分析低成本方案-Python分析Nginx日志，通过正则表达式检测数据出境到第三方服务]]
- [[现成工具整合建议]]

### #网络监控
- [[网络层监控方案]]

### #Zeek
- [[网络层监控方案]]

### #跨境数据
- [[网络层监控方案]]

### #数据联动
- [[敏感数据扫描与数据去向联动]]

### #工作流
- [[敏感数据扫描与数据去向联动]]

### #违规报告
- [[敏感数据扫描与数据去向联动]]

### #工具整合
- [[现成工具整合建议]]

### #数据库审计
- [[现成工具整合建议]]

### #专利申请
- [[专利角度描述]]

### #技术方案
- [[专利角度描述]]

### #关键提醒
- [[关键提醒]]

### #跨境通道
- [[关键提醒]]

### #数据识别
- [[关键提醒]]

---
*此索引文件由原子笔记切分工具生成*
