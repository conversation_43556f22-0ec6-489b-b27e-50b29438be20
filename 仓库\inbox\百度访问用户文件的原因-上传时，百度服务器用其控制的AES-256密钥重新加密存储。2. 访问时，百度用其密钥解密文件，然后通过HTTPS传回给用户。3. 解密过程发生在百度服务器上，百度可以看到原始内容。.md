---
title: 百度访问用户文件的原因
source: "[[为什么AES-256存储加密+HTTPS传输仍然不够安全？]]"
tags:
  - 百度
  - 文件访问
  - 加密
  - HTTPS
keywords:
  - 百度
  - 文件访问
  - 加密
  - HTTPS
  - 解密
created: 2025-08-02
type: 原子笔记
已学: true
aliases:
  - |-
    百度访问用户文件的原因-上传时，百度服务器用其控制的AES-256密钥重新加密存储。
    2. 访问时，百度用其密钥解密文件，然后通过HTTPS传回给用户。
    3. 解密过程发生在百度服务器上，百度可以看到原始内容。
---

# 百度访问用户文件的原因-上传时，百度服务器用其控制的AES-256密钥重新加密存储。2. 访问时，百度用其密钥解密文件，然后通过HTTPS传回给用户。3. 解密过程发生在百度服务器上，百度可以看到原始内容。

百度能访问用户文件的原因包括：
1. 上传时，百度服务器用其控制的AES-256密钥重新加密存储。
2. 访问时，百度用其密钥解密文件，然后通过HTTPS传回给用户。
3. 解密过程发生在百度服务器上，百度可以看到原始内容。

---

## 元信息
- **来源笔记**: [[为什么AES-256存储加密+HTTPS传输仍然不够安全？]]
- **创建时间**: 2025/8/2 21:03:04
- **标签**: #百度 #文件访问 #加密 #HTTPS
- **关键词**: 百度, 文件访问, 加密, HTTPS, 解密

## 相关链接
- 返回原笔记: [[为什么AES-256存储加密+HTTPS传输仍然不够安全？]]
