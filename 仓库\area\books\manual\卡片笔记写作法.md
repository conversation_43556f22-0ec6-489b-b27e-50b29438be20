---
tags:
  - project
area: "[[阅读]]"
createTime: 2024-04-13T16:55:02
cover: "[[卡片笔记写作法.jpg]]"
author: xxx
isbn: "22222222222"
readingStatus: Done
doneTime: 2024-05-15T16:55:00
category:
  - 知识管理
progress: 100
---
# 高亮划线

## 推荐序一 像卢曼一样写卡片


- 📌 认知科学家将人的信息加工分为两大类，第一类是一阶操作，也就是“认知”本身，你感知的、你记忆的、你学习的、你思考的。第二类是二阶操作，称之为“元认知”，即对自己当前的认知进行监控：“我当前感知到了什么？我当前在记忆什么？我当前在学习什么？我当前在思考什么？”这就是元感知、元记忆、元学习、元思考等。“元认知”也就是认知的认知。 ^39136896-6-1627-1789
    - ⏱ 2023-05-03 19:48:28 

- 📌 什么是必要难度理论？人类记忆存在广泛且普遍的元认知错觉，会误将“记住了”当成“学会了”。 ^39136896-6-2214-2258
    - ⏱ 2023-05-03 19:51:06 

- 📌 第一类是主题索引。当某个主题的内容积累得足够丰富，卢曼就会做一张主题索引卡，对这个主题进行概览。 ^39136896-6-2889-2937
    - ⏱ 2023-05-03 19:52:18 

- 📌 第二类与主题索引类似，只不过不是对某一主题的概览，而是针对盒子里相近位置的卡片所涉及的所有不同主题进行概览 ^39136896-6-3033-3086
    - ⏱ 2023-05-03 19:55:39 

- 📌 第三类是在当前卡片上做索引，标明这条笔记逻辑上的前一条是什么、后一条是什么（这些卡片在盒子里的位置可能并不挨着） ^39136896-6-3114-3170
    - ⏱ 2023-05-03 19:55:47 

- 📌 第四类，也是最常用的索引形式，就是简单的“笔记-笔记”连接。两条笔记可能完全没有关系，把它们关联在一起，往往会产生出乎意料的新思路。 ^39136896-6-3198-3264
    - ⏱ 2023-05-03 19:55:59 
## 推荐序三 用卡片笔记积累你的知识复利

^15b7f6


- 📌 作者申克·阿伦斯还阐述了许多卢曼反直觉的思考方式，比如拒绝做知识的搬运工，必须用自己的话写下来；比如需要记录的是知识，而非信息；比如不需要进行机械分类，而是让关系慢慢地自动生长出来等 ^39136896-8-1012-1103
    - ⏱ 2023-05-03 20:03:36 
## 译者序


- 📌 书中的原型尼克拉斯·卢曼，原本就和如今的大多数人一样，做着朝九晚五的公务员工作，平日里回家以后就读一读自己喜欢的书，连最初做笔记的方式也和大多数人没有差别，比如在空白处写写评论。只是他很快意识到这样做笔记除了会得到大量笔记，不会有任何成果。于是，卢曼改变了记笔记的方式，转而将笔记记到卡片上，收集到卡片盒里，并且经常思考某些笔记如何与另一些笔记建立联系。在卢曼的卡片盒里，一个个原本孤立的想法渐渐变成了想法集群，并衍生出更多系统性的思想。这套笔记系统成了卢曼的生产力引擎，使他这个酿酒师的儿子从公务员变成了社会学教授，并使他凭借高质量、高产出成为20世纪最伟大的社会学家之一。 ^39136896-9-385-673
    - ⏱ 2023-05-03 20:04:53 

- 📌 为什么要记笔记？我们看书学习时会产生自己的思考，这样的洞见来之不易，如果不记笔记，往往会转瞬即逝，因此记笔记可以巩固我们的思考成果。自己的思考并不意味着完全不同于原始的知识，有时哪怕是精简一下语言，调整一下语序，使得下一次再查看时不需要花费和第一次一样多的时间，那么我们花在第一次学习上的时间也就有了价值 ^39136896-9-1626-1778
    - ⏱ 2023-05-03 20:07:34 

- 📌 为什么长久以来很少有人能用这套方法取得像卢曼一样杰出的成就呢？书中提到一个主要原因就是人们普遍认为巨大的成就一定来源于伟大的想法，这和译者的切身感受一致——一些人已经用Anki（一款很受欢迎的间隔复习软件）提高了学习质量，另一些人则还在寻找所谓的更厉害的工具。其实哪有什么惊人绝技，有的只是把简单有效的方法用到极致罢了 ^39136896-9-2276-2435
    - ⏱ 2023-05-03 20:08:47 

- 📌 学习心理学上关于成功模仿的四要素——观察、保持、复现和动机。书中提到了两种记忆方式，一种“提取强度”，采用Zettelkasten等工具，通过建立频繁的联系来增强记忆；另一种是“存储强度”，通过闪卡类工具，如译者长期在普及的Anki软件，通过间隔复习来增强记忆 ^39136896-9-3347-3477
    - ⏱ 2023-05-03 20:11:30 
## 导论


- 📌 写作在学习、研究和探究过程中都起着至关重要的作用。令人惊讶的是，我们对写作本身却思考得很少 ^39136896-10-657-702
    - ⏱ 2023-05-03 20:18:03 

- 📌 他们似乎忘记了，写作过程的开始远早于在空白的屏幕上落笔，而实际写下论点也只是这个过程中最小的部分 ^39136896-10-1125-1173
    - ⏱ 2023-05-03 20:20:40 

- 📌 人们不重视记笔记的另一个原因是，即使笔记记得不好，也不会立即得到任何负面的反馈。因此，没有直接的失败体验，也就不会有太多的改善需求 ^39136896-10-1606-1671
    - ⏱ 2023-05-03 20:21:54 

- 📌 大多数人苦于写作的原因都差不多，就是因为他们相信或者被迫相信写作是从一张空白页开始的。如果你认定手头确实没有什么可写，你当然就会感到恐慌。仅仅在脑子里感觉满满完全不够，因为把想法写在纸上才是最难的。这就是为什么良好的笔记是构建好的、有成效写作的基础。 ^39136896-10-2168-2293
    - ⏱ 2023-05-03 20:23:32 

- 📌 事实上，在智力的各种因素中，起最大作用的不是智商，而是一个人具备怎样的自律或自控力来处理手头的事务（Duckworth and Seligman,2005;Tangney,Baumeister,and Boone,2004）。 ^39136896-10-2745-2858
    - ⏱ 2023-05-03 20:24:48 

- 📌 意志力是一种有限的资源，它消耗得很快[1]，而且从长期来看，也没有那么多手段快速提升意志力（Baumeister,Bratslavsky,M uraven,and Tice,1998;M uraven,Tice,and Bau meister,1998;Schmeichel,Vohs,and Bau meister,2003;Moller,2006） ^39136896-10-3048-3264
    - ⏱ 2023-05-03 20:25:25 

- 📌 我们今天知道，自控和自律与环境的关系远比与个人的关系大得多（Thaler,2015），而环境是可以改变的。 ^39136896-10-3321-3374
    - ⏱ 2023-05-03 20:27:44 
## 第一章 绪论


- 📌 具有优秀结构的笔记是你可以信任的写作宝库。这样你就不必记住或记挂着每一件事，从而可以把你的大脑从沉重的记忆负担中解脱出来。如果你能信任笔记系统，就可以不必拼命用大脑记住所有东西，从而可以集中精神去思考重要的东西 ^39136896-11-678-783
    - ⏱ 2023-05-03 20:29:48 

- 📌 其实我们真正该做的是构建适合自己的工作流程，让洞见和新想法成为推动我们前进的驱动力。 ^39136896-11-1466-1508
    - ⏱ 2023-05-03 20:31:31 

- 📌 但是，并非读得越多，思路越宽，特别是在开始的时候，会发现翻阅的资料越多，能展开工作的切入点反而少了，因为大部分论点已经被别人想到了。 ^39136896-11-1977-2043
    - ⏱ 2023-05-03 20:33:25 

- 📌 我们不必也不需要在可用性和实用性之间做出取舍。恰恰相反，处理复杂的事情最好的方法是尽可能地保持简单 ^39136896-11-3265-3314
    - ⏱ 2023-05-03 20:38:30 

- 📌 常规工作需要简单的、可重复的任务，这些任务可以变得自动，并且无缝地结合在一起（Mata,Todd,and Lippke,2010）。只有当所有相关的工作都相互衔接成为一个整体，所有的瓶颈都被消除时，才能发生显著的变化 ^39136896-11-4026-4134
    - ⏱ 2023-05-14 18:12:34 

- 📌 遗憾的是，戴维·艾伦的技术不能简单地转化到有洞见的写作任务上。原因之一是，GTD依赖于明确界定的目标，而写作所需要的洞见则无法明确地预先界定。我们一开始的想法通常是非常模糊的，思路会在研究过程中才变得越来越清晰（Ahrens,2014）。因此，以洞见为目的的写作必须以更开放的方式组织 ^39136896-11-4694-4836
    - ⏱ 2023-05-03 20:41:40 

- 📌 一个成功组织的秘诀在于整体观。所有的事情都需要处理好，否则被忽视的那部分会绊住我们，导致不重要的任务也变得紧急。即使是最好的工具，孤立使用它也不会有太好的效果，只有把它们嵌入精心策划的工作流程中，工具才能发挥出它们的优势。如果这些工具不能配合使用，再好的工具也没有意义。 ^39136896-11-5313-5448
    - ⏱ 2023-05-03 20:43:06 

- 📌 戴维·艾伦的另一个见解：只有当信任系统，而且知道一切都可以处理好的时候，大脑才会放开，才能专注于手头的任务。 ^39136896-11-5576-5630
    - ⏱ 2023-05-03 20:44:02 

- 📌 卢曼很快就发展出了这些笔记的新类别。他意识到，一个想法、一条笔记只有在它的上下文语境中才有价值，而上下文并不一定是它的出处。所以，他开始思考一个想法，那就是如何让笔记与不同的上下文相联系并起到相应的作用。如果只是把笔记积累在一个地方，除了产生大量的文字，不会再有其他的成果 ^39136896-11-6277-6413
    - ⏱ 2023-05-03 20:49:10 