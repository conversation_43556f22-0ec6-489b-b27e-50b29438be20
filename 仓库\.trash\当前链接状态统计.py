#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
当前链接状态统计
显示当前笔记系统的真实链接分布情况
"""

import os
import re
from pathlib import Path

def extract_links(content):
    """提取现有链接"""
    return re.findall(r'\[\[([^\]]+)\]\]', content)

def main():
    """主函数"""
    base_dir = Path(r"c:\Users\<USER>\OneDrive\obsidian笔记系统\战略与战术")
    
    print("当前链接状态统计")
    print("=" * 25)
    
    # 统计数据
    link_distribution = {
        0: [],
        1: [],
        2: [],
        3: [],
        4: [],
        5: []
    }
    more_than_5 = []
    
    total_notes = 0
    
    for file_path in base_dir.rglob("*.md"):
        # 排除特定目录
        if any(ex in str(file_path).lower() for ex in ['默写本', '.obsidian', '.trash', '原子笔记', '知识卡片']):
            continue
        
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
        except:
            continue
        
        total_notes += 1
        links = extract_links(content)
        link_count = len(links)
        
        if link_count <= 5:
            link_distribution[link_count].append(file_path.stem)
        else:
            more_than_5.append((file_path.stem, link_count))
    
    print(f"总笔记数: {total_notes}")
    print(f"\n链接分布:")
    
    isolated_total = 0
    for i in range(6):
        count = len(link_distribution[i])
        percentage = count / total_notes * 100 if total_notes > 0 else 0
        print(f"  {i}个链接: {count}个 ({percentage:.1f}%)")
        
        if i <= 2:  # 0-2个链接视为孤立
            isolated_total += count
    
    more_count = len(more_than_5)
    more_percentage = more_count / total_notes * 100 if total_notes > 0 else 0
    print(f"  6+个链接: {more_count}个 ({more_percentage:.1f}%)")
    
    isolated_percentage = isolated_total / total_notes * 100 if total_notes > 0 else 0
    print(f"\n孤立笔记总计(0-2个链接): {isolated_total}个 ({isolated_percentage:.1f}%)")
    
    # 显示孤立笔记详情
    if isolated_total > 0:
        print(f"\n孤立笔记详情:")
        for i in range(3):  # 0, 1, 2个链接
            notes = link_distribution[i]
            if notes:
                print(f"\n{i}个链接的笔记 ({len(notes)}个):")
                for note in notes[:10]:  # 只显示前10个
                    print(f"  - {note}")
                if len(notes) > 10:
                    print(f"  ... 还有{len(notes)-10}个")
    else:
        print(f"\n🎉 太棒了！没有孤立笔记了！")
    
    # 连接良好的笔记统计
    well_connected = sum(len(link_distribution[i]) for i in range(3, 6)) + more_count
    well_percentage = well_connected / total_notes * 100 if total_notes > 0 else 0
    print(f"\n连接良好的笔记(3+个链接): {well_connected}个 ({well_percentage:.1f}%)")

if __name__ == "__main__":
    main()
