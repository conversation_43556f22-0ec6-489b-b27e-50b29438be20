/* @settings
name: Composer
id: obsidian-composer-theme
settings:
    -
        id: learn-about-components-plugin
        title: About Components
        title.zh: 关于 Components
        description: "[https://cp.cc1234.cc/](https://cp.cc1234.cc/)"
        type: info-text
        markdown: true
    - id: theme-variant
      title: Color Scheme
      title.zh: 配色方案
      description: Choose a color scheme for the theme
      description.zh: 选择一个预设的配色方案
      type: class-select
      allowEmpty: false
      default: composer--DefaultScheme
      options:
          - label: Sù 素
            value: composer--DefaultScheme
          - label: Xiá 霞
            value: composer--XiaScheme
          - label: Qīng 青
            value: composer--QingScheme
    - id: typograph-settings
      title: Typograph Settings
      title.zh: 排版设置
      type: heading
      level: 2
      collapsed: true
    - id: file-line-width
      title: Content Area width
      title.zh: 内容区域宽度
      description: The maximum line width
      description.zh: 最大行宽
      type: variable-number-slider
      default: 700
      min: 420
      max: 1200
      format: px
      step: 1
    - id: composer--ParagraphIndent
      title: Paragraph Indent
      title.zh: 段落首行缩进
      description: indent the first line of a paragraph
      description.zh: 段落首行自动缩进 2 个字符
      type: class-toggle
    - id: paragraph-line-height
      title: Paragraph Line Height
      title.zh: 段落行高
      description: Set the line height of the paragraph
      description.zh: 设置段落的行高
      type: variable-number
      default: 1.7
      min: 1.2
      max: 2.5
    - id: paragraph-letter-spacing
      title: Paragraph Letter Spacing
      title.zh: 段落字间距
      description: Set the letter spacing of the paragraph, you should specify the unit em or px
      description.zh: 设置段落的字间距，需要指定单位 em 或 px
      type: variable-text
      default: 0.035em
    - id: paragraph-word-spacing
      title: Paragraph Word Spacing
      title.zh: 段落词间距
      description: Set the word spacing of the paragraph, you should specify the unit em or px
      description.zh: 设置段落的词间距，需要指定单位 em 或 px
      type: variable-text
      default: 0.035em
    - id: typograph-heading-size
      title: Heading (H1-H6)
      title.zh: 标题（H1-H6）
      type: heading
      level: 3
      collapsed: true
    - id: h1-size
      title: H1 Size
      title.zh: 一级标题大小
      description: you should specify the unit em or px
      description.zh: 需要指定单位 em 或 px
      type: variable-text
      default: 1.69em
    - id: h2-size
      title: H2 Size
      title.zh: 二级标题大小
      description: you should specify the unit em or px
      description.zh: 需要指定单位 em 或 px
      type: variable-text
      default: 1.51em
    - id: h3-size
      title: H3 Size
      title.zh: 三级标题大小
      description: you should specify the unit em or px
      description.zh: 需要指定单位 em 或 px
      type: variable-text
      default: 1.35em
    - id: h4-size
      title: H4 Size
      title.zh: 四级标题大小
      description: you should specify the unit em or px
      description.zh: 需要指定单位 em 或 px
      type: variable-text
      default: 1.21em
    - id: h6-size
      title: H6 Size
      title.zh: 五级标题大小
      description: you should specify the unit em or px
      description.zh: 需要指定单位 em 或 px
      type: variable-text
      default: 1.1em
    - id: h6-size
      title: H6 Size
      title.zh: 六级标题大小
      description: you should specify the unit em or px
      description.zh: 需要指定单位 em 或 px
      type: variable-text
      default: 1em

    - id: navbar-settings
      title: Navbar Settings
      title.zh: 导航栏设置
      type: heading
      level: 2
      collapsed: true
    - id: composer--DisableNavHeaderAutoHide
      title: Disable Nav Header Auto Hide
      title.zh: 关闭自动隐藏导航栏
      description: Enable this option to disable the auto-hide of the nav header
      description.zh: 启用以后，相关视图的导航栏将会常驻（比如文件列表、标签、目录栏的导航栏菜单），不会在失去焦点时自动隐藏
      type: class-toggle
    - id: color-settings
      title: Color Settings
      title.zh: 色彩设置
      type: heading
      level: 2
      collapsed: true
    - id: background-primary
      title: Background Primary
      title.zh: 主要背景色
      type: variable-themed-color
      format: hex
      default-light: "#fafafa"
      default-dark: "#1a1e24"
    - id: background-primary-alt
      title: Background Primary Alt
      title.zh: 主要背景色（备用）
      type: variable-themed-color
      format: hex
      default-light: "#e8eaee"
      default-dark: "#252a32"
    - id: background-secondary
      title: Background Secondary
      title.zh: 次要背景色
      type: variable-themed-color
      format: hex
      default-light: "#f1f0ee"
      default-dark: "#2d333b"
    - id: background-secondary-alt
      title: Background Secondary Alt
      title.zh: 次要背景色（备用）
      type: variable-themed-color
      format: hex
      default-light: "#dddfe3"
      default-dark: "#373e48"
    - id: plugin-components
      title: Components Plugin
      title.zh: Components 插件
      type: heading
      level: 2
      collapsed: true
    - id: dynamic-dataview-component
      title: Dynamic Dataview
      title.zh: 数据视图
      type: heading
      level: 3
      collapsed: true
    - id: composer--ComponentsSimpleTag
      title: Simple Tag Style
      title.zh: 简单标签样式
      description: switch to simple tag style to select / multi-select / group-name...
      description.zh: 改变数据视图中单选、多选项的样式
      type: class-toggle
    - id: composer--ComponentsFilledGallaryCover
      title: Notion Style Gallary Cover
      title.zh: Notion 风格的封面
      description: remove Gallary dynamic data view's cover padding
      description.zh: 移除动态数据视图中的封面内边距
      type: class-toggle
    - id: count-component
      title: Count Component
      title.zh: 统计数字组件
      type: heading
      level: 3
      collapsed: true
    - id: components--count-component-value-font-size
      title: Value Font Size
      title.zh: 数值字体大小
      type: variable-number
      default: 1.6
      format: rem
    - id:  quote-component
      title: Quote Component
      title.zh: 摘录组件
      type: heading
      level: 3
      collapsed: true
    - id: components-quote-title-font-size
      title: Title Font Size
      title.zh: 标题字体大小
      description: "rem"
      type: variable-number
      default: 1
      format: rem
    - id: components-quote-content-font-size
      title: Content Font Size
      title.zh: 内容字体大小
      description: "rem"
      type: variable-number
      default: 0.875
      format: rem
    - id:  card-component
      title: Card Component
      title.zh: 卡片组件
      type: heading
      level: 3
      collapsed: true
    - id: components--card-component-title-icon-size
      title: Title Icon Size
      title.zh: 标题图标大小
      description: "rem"
      type: variable-number
      default: 1
      format: rem
    - id: components--card-component-title-font-size
      title: Title Font Size
      title.zh: 标题字体大小
      description: "rem"
      type: variable-number
      default: 1
      format: rem
    - id: components--card-component-description-font-size
      title: Content Font Size
      title.zh: 描述字体大小
      description: "rem"
      type: variable-number
      default: 0.8125
      format: rem
*/

::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-thumb {
  background-color: transparent;
  border-radius: var(--size-4-1);
}

::-webkit-scrollbar-thumb {
  background-color: transparent;
}

:hover::-webkit-scrollbar-thumb {
  background-color: var(--background-modifier-hover);
}

body {
  --inline-title-weight: 500;
  --inline-title-size: 1.6rem;
  --inline-title-color: var(--text-normal);
  --header-height: 36px;
  --modal-bg-blur: 3px;
  --metadata-input-font-size: 14px;
  --file-margins: 24px;

  /* font-size */
  --h1-size: 1.69em;
  --h2-size: 1.51em;
  --h3-size: 1.35em;
  --h4-size: 1.21em;
  --h5-size: 1.1em;
  --h6-size: 1em;
  --heading-spacing: calc(var(--p-spacing) * 1.8);

  --link-external-color: rgba(55, 53, 47, 0.85);
  --link-external-color-hover: var(--text-normal);
  --link-decoration: none;
  --link-decoration-hover: none;
  --link-external-decoration: underline;
  --link-external-decoration-hover: underline;

  --indentation-guide-color: transparent;
  /* callout */
  --callout-color: transparent;
  --callout-radius: 8px;

  /* Typography */
  --paragraph-letter-spacing: 0.035em;
  --paragraph-line-height: 1.7;
  --paragraph-word-spacing: 0.035em;
  --line-height-normal: var(--paragraph-line-height);

  /* list */
  --list-spacing: 0.225em;
  /* embed */
  --embed-border-start: 2px solid #cbd5e0;
  /* suggestion */
  --suggesttion-highlight-color: var(--code-normal);

  --ribbon-padding: 0px;
  --ribbon-background: transparent;
  --titlebar-background: transparent;
  --titlebar-background-focused: transparent;
  --tab-container-background: transparent;
  --tab-background-active: rgba(31, 34, 37, 0.03);

  /* tag */
  --tag-radius: 2em;
  --tag-padding-y: 0.2em;
  --tag-border-width: 1px;
  --tag-size: 0.8em;

  --background-modifier-form-field: hsl(var(--accent-h), var(--accent-s), 0.1);
  --background-modifier-message: var(--background-secondary);
  --ribbon-background-collapsed: transparent;

  --heading-indicator-spacing: 6px;

  --workspace-header-background: transparent;
  --ribbon-background-collapsed: transparent;
  --mod-left-ribbon-background: transparent;
  --mod-left-ribbon-background-collapsed: transparent;

  --mod-root-background: transparent;
  --mod-root-border: 0px 1px 0px 1px solid var(--background-modifier-border);
  --mod-root-header-background: transparent;
  --mod-root-header-margin: 0px 0px 4px 0px;

  --mod-left-split-background: transparent;
  --mod-right-split-background: transparent;

  --root-workspace-padding-bottom: 28px;
  --root-workspace-radius: var(--radius-m);
  --root-workspace-shadow: var(--background-modifier-border) 0px 0px 3px 0px,
    var(--background-modifier-border) 0px 1px 2px 0px;
  --root-workspace-background: transparent;
  --root-workspace-margin-spacing: 16px;
  --side-dock-actions-border-color: var(--background-modifier-border);
}

/* body {
	--ribbon-background-collapsed: transparent;
	--mod-left-ribbon-background: transparent;
	--mod-left-ribbon-background-collapsed: var(--background-primary);
	--mod-root-header-background: var(--background-primary);
	--mod-root-header-margin: 0px;
	--root-workspace-padding-bottom: 0px;
	--root-workspace-radius: 0px;
	--root-workspace-shadow: none;
	--root-workspace-margin-spacing: 0px;
	--side-dock-actions-border-color: transparent;
  } */

.theme-light {
  --accent-h: 204;
  --accent-s: 15%;
  --accent-l: 58%;

  --background-primary: #fafafa;
  --background-primary-alt: #e8eaee;
  --background-secondary: #f1f0ee;
  --background-secondary-alt: #dddfe3;

  --background-gradient: radial-gradient(
    ellipse at 50% 30%,
    hsl(0, 0%, 100%) 20%,
    hsl(220, 16%, 95%) 80%
  );

  --background-modifier-border: rgba(225, 219, 214, 0.6);

  /* text */
  --color-base-100: #3a4659;
  --bold-color: #2d3748;
  --bold-modifier: 200;

  /* title */
  --h1-color: #2d3748;
  --h2-color: #4a556b;
  --h3-color: #66788d;
  --h4-color: #7f8a9b;
  --h5-color: #9aa5b5;
  --h6-color: #808080;
  --h1-indicator-color: #2d3748;
  --h2-indicator-color: #4a556b;
  --h3-indicator-color: #66788d;
  --h4-indicator-color: #7f8a9b;
  --h5-indicator-color: #9aa5b5;
  --h6-indicator-color: #808080;

  /* inline code */
  --code-normal: #eb5757;
  --code-border-color: transparent;
  --code-background: hsla(31, 41%, 90%, 0.6);

  /* text-highlight */
  --text-highlight-background: hsla(41, 61%, 92%);
  --text-highlight-border-color: hsl(47, 48%, 42%);
  --text-highlight-color: hsl(220, 25%, 25%);
  --text-highlight-border-radius: 4px;

  /* quote */
  --blockquote-border-color: hsl(220, 15%, 80%);
  --blockquote-background-color: hsl(220, 20%, 97%);
  --blockquote-color: hsl(220, 25%, 35%);

  /* codeblock */
  --codeblock-background: #d6d4d440;
  --codeblock-caret-color: #4a556b;
  --codeblock-border-radius: 8px;

  /* basic color */
  --color-red-rgb: 174, 62, 76;
  --color-blue-rgb: 74, 85, 107;
  --color-purple-rgb: 94, 72, 146;
  --color-cyan-rgb: 52, 136, 152;
  --color-blue-rgb: 95, 164, 218;
  --color-orange-rgb: 184, 110, 51;
  --color-green-rgb: 62, 132, 98;
  --color-yellow-rgb: 191, 143, 63;
  --color-pink-rgb: 184, 92, 132;
  /* callout */
  --callout-bug: var(--color-red-rgb);
  --callout-default: var(--color-blue-rgb);
  --callout-error: 158, 48, 57;
  --callout-example: var(--color-purple-rgb);
  --callout-fail: var(--color-red-rgb);
  --callout-important: var(--color-cyan-rgb);
  --callout-info: var(--color-blue-rgb);
  --callout-question: var(--color-orange-rgb);
  --callout-success: var(--color-green-rgb);
  --callout-summary: var(--color-cyan-rgb);
  --callout-tip: var(--color-cyan-rgb);
  --callout-todo: var(--color-blue-rgb);
  --callout-warning: 191, 127, 63;
  --callout-quote: 158, 158, 158;

  /* customize */
  --nav-button-container-bg: rgba(31, 34, 37, 0.03);
  --header-active-bg: rgba(31, 34, 37, 0.03);
}

.theme-dark {
  --accent-h: 204;
  --accent-s: 18%;
  --accent-l: 42%;

  --background-primary: #1a1e24;
  --background-primary-alt: #252a32;
  --background-secondary: #2d333b;
  --background-secondary-alt: #373e48;

  --background-gradient: radial-gradient(
    ellipse at 50% 30%,
    hsl(220, 12%, 18%) 20%,
    hsl(220, 14%, 12%) 80%
  );

  --background-modifier-border: rgba(67, 73, 83, 0.6);

  /* 文本系统 */
  --color-base-100: #d4d9e0;
  --bold-color: #f0f4f8;
  --bold-modifier: 200;

  /* title */
  --h1-color: #e2e6eb;
  --h2-color: #c5cad4;
  --h3-color: #a8aeb9;
  --h4-color: #8f96a1;
  --h5-color: #787f8a;
  --h6-color: #666d78;
  --h1-indicator-color: var(--h1-color);
  --h2-indicator-color: var(--h2-color);
  --h3-indicator-color: var(--h3-color);
  --h4-indicator-color: var(--h4-color);
  --h5-indicator-color: var(--h5-color);
  --h6-indicator-color: var(--h6-color);

  /* inline code */
  --code-normal: #ff7b72;
  --code-border-color: transparent;
  --code-background: hsla(204, 15%, 25%, 0.4);

  /* text highlight */
  --text-highlight-background: hsla(40, 30%, 25%, 0.4);
  --text-highlight-border-color: hsl(40, 27%, 61%);
  --text-highlight-color: hsl(40, 60%, 85%);
  --text-highlight-border-radius: 4px;

  /* quote */
  --blockquote-border-color: hsl(204, 15%, 35%);
  --blockquote-background-color: hsla(204, 15%, 15%, 0.3);
  --blockquote-color: hsl(204, 15%, 75%);

  /* codeblock */
  --codeblock-background: #2b303880;
  --codeblock-caret-color: #c5cad4;
  --codeblock-border-radius: 8px;
  --codeblock-border: #e5e7eb;

  /* link */
  --link-external-color: rgba(196, 196, 196, 0.85);
  --link-external-color-hover: var(--text-normal);

  /* basic color */
  --color-red-rgb: 255, 123, 114;
  --color-blue-rgb: 123, 175, 222;
  --color-purple-rgb: 182, 155, 255;
  --color-cyan-rgb: 123, 204, 214;
  --color-orange-rgb: 255, 182, 104;
  --color-green-rgb: 123, 204, 148;
  --color-yellow-rgb: 255, 203, 107;
  --color-pink-rgb: 255, 157, 194;

  /* callout */
  --callout-error: 255, 100, 92;
  --callout-warning: 255, 170, 92;
  --callout-quote: 158, 158, 158;

  /* 自定义组件 */
  --nav-button-container-bg: rgba(255, 255, 255, 0.03);
  --header-active-bg: rgba(255, 255, 255, 0.05);
}

/* ---- Xia ---- */
.composer--XiaScheme.theme-light {
  --accent-h: 12;
  --accent-s: 31%;
  --accent-l: 60%;
  --background-modifier-border: hsla(25, 20%, 70%, 0.4);

  --background-primary: rgb(243, 237, 233);
  --background-primary-alt: rgb(238, 230, 225);
  --background-secondary: rgb(232, 223, 217);
  --background-secondary-alt: rgb(225, 215, 208);
  --background-gradient: linear-gradient(
    rgb(217, 224, 227),
    rgb(235, 215, 210)
  );

  /* quote */
  --blockquote-border-color: hsl(25, 20%, 70%);
  --blockquote-background-color: rgba(237, 228, 222, 0.811);
  --blockquote-color: hsl(25, 20%, 30%);

  /* codeblock */
  --codeblock-background: rgba(234, 223, 215, 0.749);
  --codeblock-caret-color: hsl(25, 20%, 30%);

  /* text highlight */
  --text-highlight-background: rgba(248, 227, 217, 0.9);
  --text-highlight-border-color: rgb(167, 98, 64);
  --text-highlight-color: rgb(102, 80, 63);
}

.composer--XiaScheme.theme-dark {
  --accent-h: 30;
  --accent-s: 40%;
  --accent-l: 65%;

  --background-gradient: linear-gradient(
    hsl(215, 18%, 18%),
    hsl(255, 12%, 28%)
  );
  --background-primary: hsl(215, 18%, 15%);
  --background-primary-alt: hsl(215, 18%, 19%);
  --background-secondary: hsl(255, 12%, 25%);
  --background-secondary-alt: hsl(255, 12%, 29%);

  --text-normal: #dedbd3;

  /* border */
  --background-modifier-border: rgb(69, 74, 84);

  /* quote */
  --blockquote-border-color: #67594c;
  --blockquote-background-color: rgba(42, 49, 60, 0.6);
  --blockquote-color: rgb(207, 197, 175);

  /* codeblock*/
  --codeblock-background: rgba(37, 44, 55, 0.85);
  --codeblock-caret-color: rgb(201, 166, 130);

  /* text highlight */
  --text-highlight-background: hsla(38, 27%, 54%, 0.5);
  --text-highlight-border-color: hsl(31, 48%, 66%);
  --text-highlight-color: hsl(40, 20%, 90%);
}

/* ---- Qing ---- */
.composer--QingScheme.theme-light {
  --accent-h: 198;
  --accent-s: 22%;
  --accent-l: 55%;

  --background-primary: #f8fbfb;
  --background-primary-alt: #ecf2f4;
  --background-secondary: #e0e8eb;
  --background-secondary-alt: #d4dfe2;
  --background-gradient: linear-gradient(
    160deg,
    hsl(190, 30%, 95%) 0%,
    hsl(190, 20%, 90%) 100%
  );

  --background-modifier-border: #dce3e5;

  --blockquote-border-color: hsl(190, 15%, 70%);
  --blockquote-background-color: hsla(190, 20%, 95%, 0.8);
  --blockquote-color: hsl(190, 25%, 35%);

  --codeblock-background: hsla(190, 15%, 92%, 0.9);
  --codeblock-caret-color: hsl(192, 33%, 15%);

  --text-highlight-background: hsla(201, 39%, 84%, 0.45);
  --text-highlight-border-color: hsl(198, 25%, 70%);
  --text-highlight-color: hsl(197, 35%, 30%);
}

.composer--QingScheme.theme-dark {
  --accent-h: 198;
  --accent-s: 28%;
  --accent-l: 45%;

  --background-primary: hsl(198, 25%, 18%);
  --background-primary-alt: hsl(198, 22%, 21%);
  --background-secondary: hsl(198, 20%, 24%);
  --background-secondary-alt: hsl(198, 18%, 27%);

  --background-gradient: linear-gradient(
    165deg,
    hsl(198, 28%, 16%) 0%,
    hsl(198, 24%, 20%) 50%,
    hsl(198, 20%, 23%) 100%
  );

  --blockquote-border-color: hsl(200, 20%, 35%);
  --blockquote-background-color: hsla(200, 25%, 18%, 0.8);
  --blockquote-color: hsl(200, 32%, 71%);

  --codeblock-background: hsla(200, 20%, 22%, 0.9);
  --codeblock-caret-color: hsl(200, 30%, 65%);

  --text-highlight-background: hsla(200, 38%, 74%, 0.4);
  --text-highlight-border-color: hsl(199, 55%, 88%);
  --text-highlight-color: hsl(200, 13%, 91%);
}

.notice,
.tooltip {
  color: var(--text-normal);
  border: 1px solid var(--background-modifier-border);
}

.notice {
  box-shadow: var(--background-modifier-border) 0px 1px 3px 1px,
    var(--background-modifier-border) 0px 1px 3px 1px;
}

.markdown-source-view,
.markdown-preview-view {
  letter-spacing: var(--paragraph-letter-spacing);
  line-height: var(--paragraph-line-height);
  word-spacing: var(--paragraph-word-spacing);
}

/* reduce spacing between metadata-container and first cm-line */
.metadata-container
  + .cm-contentContainer
  .cm-content
  > div[contenteditable="false"]:first-child
  + .cm-line {
  padding-top: 1rem;
}

/* blockquote */
.markdown-source-view.mod-cm6 .cm-line.HyperMD-quote,
.HyperMD-quote {
  color: var(--blockquote-color);
  padding-top: 4px;
  padding-bottom: 4px;
}

/* quote reading view */
.el-blockquote blockquote {
  padding-top: 4px;
  padding-bottom: 4px;
  padding-inline-start: 14px;
}

/* title */
.markdown-source-view.mod-cm6 .cm-fold-indicator .collapse-indicator {
  padding-inline-end: 12px;
}

.markdown-rendered .heading-collapse-indicator {
  margin-inline-start: -28px;
}

.markdown-rendered > h1::before,
.markdown-rendered > h2::before,
.markdown-rendered > h3::before,
.markdown-rendered > h4::before,
.markdown-rendered > h5::before,
.markdown-rendered > h6::before,
.markdown-rendered .el-h1 > h1::before,
.markdown-rendered .el-h2 > h2::before,
.markdown-rendered .el-h3 > h3::before,
.markdown-rendered .el-h4 > h4::before,
.markdown-rendered .el-h5 > h5::before,
.markdown-rendered .el-h6 > h6::before {
  content: " ";
  height: calc(1.2em - 6px);
  display: inline-block;
  width: 4px;
  border-radius: var(--size-4-1);
  margin-right: var(--heading-indicator-spacing);
  background-color: var(--h1-indicator-color);
  transform: translateY(4px);
}

.markdown-rendered > h4::before,
.markdown-rendered .el-h4 > h4::before {
  transform: translateY(2px);
}
.markdown-rendered > h5::before,
.markdown-rendered > h6::before,
.markdown-rendered .el-h5 > h5::before,
.markdown-rendered .el-h6 > h6::before {
  transform: translateY(0px);
}
.markdown-rendered > h2::before,
.markdown-rendered .el-h2 > h2::before {
  background-color: var(--h2-indicator-color);
}
.markdown-rendered > h3::before,
.markdown-rendered .el-h3 > h3::before {
  background-color: var(--h3-indicator-color);
}

.markdown-rendered > h4::before,
.markdown-rendered .el-h4 > h4::before {
  background-color: var(--h4-indicator-color);
}

.markdown-rendered > h5::before,
.markdown-rendered .el-h5 > h5::before {
  background-color: var(--h5-indicator-color);
}

.markdown-rendered > h6::before,
.markdown-rendered .el-h6 > h6::before {
  background-color: var(--h6-indicator-color);
}
.markdown-source-view.mod-cm6 .HyperMD-header {
  padding-top: var(--heading-spacing);
  padding-bottom: 1rem;
}

.markdown-source-view.mod-cm6 .HyperMD-header.cm-active {
  background-color: var(--header-active-bg);
}

.is-live-preview .HyperMD-header::before {
  content: " ";
  height: calc(1.2em - 6px);
  display: inline-block;
  width: 4px;
  border-radius: var(--size-4-1);
  margin-right: var(--heading-indicator-spacing);
  background-color: var(--h1-indicator-color);
  transform: translateY(4px);
}

.is-live-preview .HyperMD-header-4.HyperMD-header::before {
  transform: translateY(6px);
}
.is-live-preview .HyperMD-header-5.HyperMD-header::before,
.is-live-preview .HyperMD-header-6.HyperMD-header::before {
  transform: translateY(7px);
}

.is-live-preview .HyperMD-header-2.HyperMD-header::before {
  background-color: var(--h2-indicator-color);
}
.is-live-preview .HyperMD-header-3.HyperMD-header::before {
  background-color: var(--h3-indicator-color);
}
.is-live-preview .HyperMD-header-4.HyperMD-header::before {
  background-color: var(--h4-indicator-color);
}
.is-live-preview .HyperMD-header-5.HyperMD-header::before {
  background-color: var(--h5-indicator-color);
}
.is-live-preview .HyperMD-header-6.HyperMD-header::before {
  background-color: var(--h6-indicator-color);
}

/* list */
/* add top margin at the start of list */
.markdown-source-view .cm-line:not(.HyperMD-header) + .HyperMD-list-line-1 {
  padding-top: 0.5rem !important;
}

.markdown-source-view .cm-line.HyperMD-list-line + .HyperMD-list-line-1 {
  padding-top: 0px !important;
}

/* reading view */
.markdown-rendered ul > li {
  margin-inline-start: 26px;
}

/* callout */
.callout {
  border: 1px solid rgba(var(--callout-color), 0.35);
}
.callout:hover {
  border-color: rgba(var(--callout-color), 0.8);
}

.markdown-source-view.mod-cm6 .cm-embed-block:not(.cm-table-widget):hover {
  box-shadow: initial;
  border-radius: initial;
  overflow: hidden;
  cursor: text;
}

/* code */
body .cm-s-obsidian .cm-scroller .cm-inline-code:not(.cm-formatting) {
  padding: 0.15em 0.3em;
  color: var(--code-normal);
}

/* codeblock */
.markdown-source-view.mod-cm6 .code-block-flair {
  color: var(--text-normal);
}

.markdown-source-view .HyperMD-codeblock {
  line-height: 1.5;
  letter-spacing: initial;
  word-spacing: initial;
}

.HyperMD-codeblock.HyperMD-codeblock-begin.HyperMD-codeblock-begin-bg {
  border-top-left-radius: var(--codeblock-border-radius);
  border-top-right-radius: var(--codeblock-border-radius);
  min-height: 24px;
}

.HyperMD-codeblock.HyperMD-codeblock-end.HyperMD-codeblock-end-bg {
  border-bottom-left-radius: var(--codeblock-border-radius);
  border-bottom-right-radius: var(--codeblock-border-radius);
}

.markdown-source-view.mod-cm6 .code-block-flair:hover {
  color: var(--code-normal);
}

.cm-s-obsidian div.HyperMD-codeblock-bg {
  background-color: var(--codeblock-background);
  caret-color: var(--codeblock-caret-color);
}

/* codeblock reading view */
.markdown-rendered pre {
  background-color: var(--codeblock-background);
  caret-color: var(--codeblock-caret-color);
  border-radius: var(--codeblock-border-radius);
}

:not(pre) > code[class*="language-"],
pre[class*="language-"] {
  background-color: var(--codeblock-background);
  caret-color: var(--codeblock-caret-color);
  border-radius: var(--codeblock-border-radius);
}

/* highlight */
body .cm-s-obsidian .cm-scroller span.cm-formatting-highlight,
body .cm-s-obsidian .cm-scroller span.cm-highlight {
  color: var(--text-highlight-color);
  background-color: var(--text-highlight-background);
  border-bottom: 1px dashed var(--text-highlight-border-color);
  padding: 2px 4px;
}
.cm-s-obsidian span.cm-formatting-highlight {
  border: none;
  background-color: initial;
}

/* highlight for reading view */
body .markdown-rendered mark {
  color: var(--text-highlight-color);
  background-color: var(--text-highlight-background);
  border-bottom: 1px dashed var(--text-highlight-border-color);
  padding: 2px 4px;
}

/* table */
.el-table table th,
.el-table table td,
.table-wrapper table th,
.table-wrapper table td {
  border-right: none;
  border-left: none;
}

.markdown-source-view.mod-cm6 .cm-table-widget .table-cell-wrapper,
.cm-html-embed thead > tr > th,
.markdown-rendered thead > tr > th,
.cm-html-embed tbody > tr > td,
.markdown-rendered tbody > tr > td {
  padding: var(--size-4-2);
}

.markdown-source-view.mod-cm6 .cm-table-widget .table-col-btn,
.markdown-source-view.mod-cm6 .cm-table-widget .table-row-btn {
  background-color: var(--background-secondary);
}

/* link/internal */
.metadata-container .internal-link,
.markdown-rendered .internal-link,
.cm-hmd-internal-link a,
.internal-link,
.markdown-embed-link {
  text-decoration-line: none;
}

.metadata-container .internal-link {
  color: var(--text-muted);
}

/* link/external */
.external-link {
  background-image: linear-gradient(transparent, transparent),
    url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24' fill='none' stroke='rgba(136, 136, 136)' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cline x1='7' y1='17' x2='17' y2='7'%3E%3C/line%3E%3Cpolyline points='7 7 17 7 17 17'%3E%3C/polyline%3E%3C/svg%3E");
  background-size: calc(var(--font-text-size) + 0.2em), 1em;
  background-position: right center;
  background-repeat: no-repeat;
  line-height: var(--line-height-normal);
  padding-inline-end: 16px;
  background-size: 14px 14px;
}

.markdown-reading-view .external-link {
  text-decoration: none;
}

.markdown-source-view.mod-cm6 .cm-link .cm-underline:hover,
.markdown-source-view.mod-cm6 .cm-url .cm-underline:hover {
  border-bottom-color: var(--link-external-color);
}

/* ======== For UI Components ======== */
/* Workspace leaf Tab  */
/* ribbon */
.workspace-ribbon {
  --divider-color: transparent;
  --divider-width: 0px;
}

body:not(.is-mobile) .workspace-ribbon.mod-left {
  background-color: var(--mod-left-ribbon-background);
  margin-top: 0px;
  padding-top: var(--header-height);
}

body:not(.is-mobile) .workspace-ribbon.mod-left.is-collapsed {
  background-color: var(--mod-left-ribbon-background-collapsed);
}

.workspace-ribbon.mod-left:before {
  background-color: var(--mod-left-ribbon-background);
  display: none;
}
body.is-focused .titlebar,
body.is-focused .workspace-ribbon.mod-left {
  border: none;
}

.side-dock-settings .side-dock-ribbon-action,
.side-dock-actions .side-dock-ribbon-action {
  margin: 0 auto;
  --icon-size: var(--icon-xs);
}

.side-dock-actions {
  border-width: 1px 1px 1px 0px;
  border-style: solid;
  border-color: var(--side-dock-actions-border-color);
  border-radius: 0px var(--radius-m) var(--radius-m) 0px;
  overflow: hidden;
  padding: 2px;
  margin-top: 2px;
}

/* Workspace nav header */
body:not(.is-mobile) .nav-header {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: var(--size-4-1);
  padding-left: var(--size-4-3);
}

body:not(.is-mobile) .nav-buttons-container {
  background-color: var(--nav-button-container-bg);
  border-radius: var(--radius-m);
  padding: 2px 4px;
  width: fit-content;
}

body:not(.is-mobile):not(.composer--DisableNavHeaderAutoHide)
  .mod-sidedock
  .workspace-leaf-content
  .nav-header
  .nav-buttons-container {
  background-color: transparent;
}

body:not(.is-mobile):not(.composer--DisableNavHeaderAutoHide)
  .mod-sidedock
  .workspace-leaf-content
  .nav-header:hover
  .nav-buttons-container {
  background-color: var(--nav-button-container-bg);
}

body:not(.is-mobile):not(.composer--DisableNavHeaderAutoHide)
  .mod-sidedock
  .workspace-leaf
  .workspace-leaf-content
  .nav-header
  .nav-action-button:hover {
  background-color: var(--background-modifier-hover);
  opacity: var(--icon-opacity);
  border-radius: var(--clickable-icon-radius);
}

body:not(.is-mobile):not(.composer--DisableNavHeaderAutoHide)
  .mod-sidedock
  .workspace-leaf-content
  .nav-header
  .nav-action-button {
  flex-grow: 0;
  max-width: 5px;
  max-height: 5px;
  overflow: hidden;
  border-radius: 50%;
  padding: 0px;
  background-color: var(--text-faint);
  transition: max-width 240ms cubic-bezier(0.4, 0, 0.2, 1),
    max-height 240ms cubic-bezier(0.4, 0, 0.2, 1),
    opacity 240ms cubic-bezier(0.4, 0, 0.2, 1),
    padding 240ms cubic-bezier(0.4, 0, 0.2, 1), background-color 120ms linear;
}

body:not(.is-mobile):not(.composer--DisableNavHeaderAutoHide)
  .mod-sidedock
  .workspace-leaf-content
  .nav-header
  .nav-action-button.is-active {
  background-color: var(--icon-color-active);
}

body:not(.is-mobile):not(.composer--DisableNavHeaderAutoHide)
  .mod-sidedock
  .workspace-leaf-content
  .nav-header:hover
  .nav-action-button {
  max-width: 32px;
  max-height: 64px;
  opacity: 0.85;
  flex-grow: 1;
  background-color: transparent;
  padding: var(--size-2-2) var(--size-2-3);
}

/* workspace tab header */
.mod-left-split .workspace-tab-header-container-inner,
.mod-right-split .workspace-tab-header-container-inner {
  gap: 4px;
  opacity: 1;
}

.nav-header .nav-buttons-container,
.clickable-icon.side-dock-ribbon-action .svg-icon,
.mod-left-split .workspace-tab-header-inner-icon .svg-icon,
.mod-right-split .workspace-tab-header-inner-icon .svg-icon {
  --icon-size: var(--icon-s);
}

/* tab for all area */
body:not(.is-phone) .workspace {
  background-color: var(--background-secondary);
  background: var(--background-gradient);
}

body.is-phone .workspace {
  background-color: var(--background-secondary);
}

.workspace-tabs .workspace-leaf {
  background-color: transparent;
}

.workspace-tab-header-container {
  border-bottom: none;
  background-color: var(--workspace-header-background);
}

.workspace-tab-header.is-active:hover {
  background-color: var(--tab-background-active);
}

.workspace-tab-header-inner {
  padding: 0 4px;
}

body:not(.is-phone) .workspace-tab-container {
  background-color: var(--background-primary);
}

.workspace .mod-left-split .workspace-tab-container {
  background-color: transparent;
}
.workspace .mod-right-split .workspace-tab-container {
  background-color: transparent;
}
.mod-root .workspace-tabs .workspace-leaf {
  background-color: transparent;
}

/* Root tab */
.workspace .mod-root {
  background-color: var(--mod-root-background);
  border: var(--mod-root-border);
}

.workspace .mod-root .workspace-tab-header-container {
  background-color: var(--mod-root-header-background);
}

.workspace .mod-root .workspace-tab-header {
  padding: 2px;
  border: 1px solid transparent;
  border-radius: var(--radius-s);
}

.workspace .mod-root .workspace-tab-header:not(.is-active):hover {
  color: var(--text-accent);
  background-color: var(--tab-background-active);
}

.workspace .mod-root .workspace-tab-header.is-active {
  box-shadow: none;
  color: var(--tab-text-color-active);
  background-color: var(--tab-background-active);
  border: 1px solid var(--background-modifier-border);
  border-radius: var(--radius-s);
}

.workspace
  .mod-root
  .workspace-tab-header:not(.is-active):hover
  .workspace-tab-header-inner {
  border: none;
  background-color: transparent;
}

.workspace .mod-root .workspace-tab-header-inner::after,
.workspace-split.mod-root .workspace-tab-header.is-active::before,
.workspace-split.mod-root .workspace-tab-header.is-active::after {
  display: none;
}

.mod-macos.is-hidden-frameless:not(.is-popout-window)
  .sidebar-toggle-button.mod-right {
  background-color: transparent;
}

/* content leaf */
body:not(.is-mobile) .workspace-split.mod-vertical,
body:not(.is-mobile) .workspace-split.mod-horizontal {
  gap: 16px;
}

body:not(.is-mobile) .mod-root .workspace-tabs {
  overflow: visible;
}

body:not(.is-mobile) .mod-root .workspace-tab-container {
  border-radius: var(--root-workspace-radius);
  box-shadow: var(--root-workspace-shadow);
  background-color: var(--root-workspace-background);
}

body:not(.is-phone) .mod-root .workspace-tab-header-container {
  padding: 0px 12px 4px 12px;
  margin: var(--mod-root-header-margin);
}

body:not(.is-phone)
  .workspace
  .mod-root
  .workspace-tabs.mod-stacked
  .workspace-tab-container
  .workspace-tab-header {
  border-radius: 0px;
  border: none;
  border-right: 1.5px dashed var(--background-modifier-border);
  box-shadow: none;
}

body:not(.is-phone)
  .workspace:is(.is-left-sidedock-open.is-right-sidedock-open)
  .mod-root.workspace-split.mod-vertical {
  margin-left: 0;
  margin-right: 0;
  padding-bottom: var(--root-workspace-padding-bottom);
}

body:not(.is-phone)
  .workspace:is(.is-left-sidedock-open):not(.is-right-sidedock-open)
  .mod-root.workspace-split.mod-vertical {
  margin-left: 0;
  margin-right: var(--root-workspace-margin-spacing);
  padding-bottom: var(--root-workspace-padding-bottom);
}

body:not(.is-phone)
  .workspace:is(.is-right-sidedock-open):not(.is-left-sidedock-open)
  .mod-root.workspace-split.mod-vertical {
  margin-left: var(--root-workspace-margin-spacing);
  margin-right: 0;
  padding-bottom: var(--root-workspace-padding-bottom);
}

body:not(.is-phone)
  .workspace:not(.is-right-sidedock-open):not(.is-left-sidedock-open)
  .mod-root.workspace-split.mod-vertical {
  margin-left: var(--root-workspace-margin-spacing);
  margin-right: var(--root-workspace-margin-spacing);
  padding-bottom: var(--root-workspace-padding-bottom);
}

.workspace-split.mod-left-split > .workspace-leaf-resize-handle,
.workspace-split.mod-right-split > .workspace-leaf-resize-handle,
.workspace-split.mod-vertical > * > .workspace-leaf-resize-handle {
  opacity: 0 !important;
}

.workspace-split.mod-left-split > .workspace-leaf-resize-handle:hover,
.workspace-split.mod-right-split > .workspace-leaf-resize-handle:hover,
.workspace-split.mod-vertical > * > .workspace-leaf-resize-handle:hover {
  opacity: 1 !important;
}

.workspace-split.mod-horizontal:not(.mod-left-split, .mod-right-split)
  > *
  > .workspace-leaf-resize-handle {
  opacity: 0 !important;
}

.workspace-split.mod-horizontal:not(.mod-left-split, .mod-right-split)
  > *
  > .workspace-leaf-resize-handle:hover {
  opacity: 1 !important;
}

/* left and right */
.workspace-split.mod-left-split {
  padding-right: 12px;
  background-color: var(--mod-left-split-background);
}
.workspace-split.mod-right-split {
  padding-left: 12px;
  background-color: var(--mod-right-split-background);
}

/* vault profile */
body:not(.is-mobile)
  .workspace-split.mod-left-split
  .workspace-sidedock-vault-profile {
  border: none;
  background-color: transparent;
}

/* status bar */
.status-bar {
  max-height: 24px;
  padding: 0px;
  border: none;
  background-color: var(--background-primary);
  box-shadow: var(--root-workspace-shadow);
}

/* markdown */
/* file header title */
body:not(.is-phone) .view-header-title-parent {
  width: 0;
  opacity: 0;
}

@media (hover: hover) {
  body:not(.is-phone)
    .view-header-title-container:hover
    .view-header-title-parent {
    width: auto;
    opacity: 1;
    transition: opacity 0.5s;
  }
}

/* outline */
.workspace-leaf-content[data-type="outline"] {
  border-radius: var(--radius-m);
}

.workspace-leaf-content[data-type="outline"] .tree-item-children {
  border-inline-start: none;
}

.workspace-leaf-content[data-type="outline"] .tree-item-self .tree-item-icon {
  opacity: 0;
}

.workspace-leaf-content[data-type="outline"] .tree-item-self.is-active {
  color: var(--text-accent);
}

.workspace-leaf-content[data-type="outline"]
  .tree-item-self:hover
  .tree-item-icon,
.workspace-leaf-content[data-type="outline"]
  .tree-item-self
  .tree-item-icon.is-collapsed {
  opacity: 1;
}

/* windows titlebar button */
.is-hidden-frameless:not(.is-fullscreen).is-focused
  .titlebar-button-container.mod-right,
.is-hidden-frameless:not(.is-fullscreen) .titlebar-button-container.mod-right {
  background-color: transparent;
}

/* inline title */
.inline-title:not([data-level]) {
  color: var(--inline-title-color);
  border-bottom: 1px solid var(--background-modifier-border);
  padding-bottom: 16px;
}

/* property */
.metadata-container {
  margin-block-end: 0rem;
}

.metadata-container + .cm-contentContainer > div > .cm-line:first-child {
  padding-top: 8px;
  margin-top: 8px;
}

.metadata-properties-title {
  color: var(--text-muted);
}

.metadata-content {
  --metadata-gap: 4px;
  --metadata-property-radius-hover: 0px;
  --metadata-property-radius-focus: 0px;
}

.metadata-properties {
  padding-bottom: 16px;
  border-bottom: 1px solid var(--metadata-divider-color);
}

.is-mobile .metadata-properties {
  padding-bottom: 8px;
}

.metadata-property {
  border-radius: 0px;
  border-bottom: 0px;
}

.metadata-property .metadata-property-key,
.metadata-property .metadata-property-value {
  border: none;
}

@media (hover: hover) {
  .metadata-property:hover {
    --metadata-divider-color: var(--metadata-divider-color-hover);
    background-color: var(--metadata-property-background-hover);
    box-shadow: none;
  }
  .metadata-property-key:hover {
    background-color: var(--background-modifier-hover);
  }
  .metadata-property-value:hover {
    background-color: var(--background-modifier-hover);
  }
}

.metadata-property-icon {
  --icon-size: var(--icon-xs);
}

.metadata-property-key {
  align-items: center;
}

.metadata-property-value a,
.metadata-property-value .external-link {
  border: none;
}

.metadata-container .metadata-show-source-button,
.metadata-container .metadata-add-button {
  margin-top: 0.3em;
  opacity: 0;
}

.metadata-content:hover .metadata-add-button {
  opacity: 1;
  transition: opacity 0.5s;
}

/* file list */
.nav-file-title,
.nav-folder-title {
  margin-bottom: 4px;
}

/* modal */
.modal-container {
  backdrop-filter: blur(var(--modal-bg-blur));
}
/* suggest */
.suggestion-highlight {
  color: var(--suggesttion-highlight-color);
}

/* typograph */
/* 
	* Paragraph indent
	* thanks for https://forum-zh.obsidian.md/t/topic/40452
	*/
.composer--ParagraphIndent {
  --paragraph-indent: calc(var(--font-text-size) * 2);
}
.composer--ParagraphIndent
  .markdown-reading-view
  :is([class="cm-line"], [class="cm-active cm-line"], p),
.composer--ParagraphIndent
  .is-live-preview
  :is([class="cm-line"], [class="cm-active cm-line"], p) {
  text-indent: var(--paragraph-indent);
}

.composer--ParagraphIndent .markdown-reading-view p > br,
.composer--ParagraphIndent .is-live-preview p > br {
  content: "";
  white-space: pre;
}

.composer--ParagraphIndent .markdown-reading-view p > br::after,
.composer--ParagraphIndent .is-live-preview p > br::after {
  content: "\000A\200B";
  margin-inline-end: var(--paragraph-indent);
}

/* ====== Basic UI Component ====== */

/* ====== Third Plugin integration ====== */
/* note toolbar */
.cg-note-toolbar-callout {
  --link-external-color: transparent;
}

/* style settings */
.style-settings-container {
  padding: 4px 6px;
  border-radius: var(--radius-m);
  background-color: var(--background-primary-alt);
}
.style-settings-container .style-settings-heading {
  border-bottom: 0px;
  margin-bottom: 12px;
}
.style-settings-container
  .setting-item-heading.style-settings-heading:not(.is-collapsed)
  .setting-item-name {
  color: var(--text-accent);
}
.style-settings-container .setting-item:not(.style-settings-heading) {
  border: 1px dashed var(--background-modifier-border);
  border-radius: var(--radius-m);
  padding: 4px 8px;
  margin-top: 6px;
}

/* components */
.components--DialogPanel {
  background: var(--background-gradient);
}

.composer--ComponentsSimpleTag .theme-dark .components--tag,
.composer--ComponentsSimpleTag .components--tag {
  background-color: transparent;
  border: 1px solid var(--background-modifier-border);
}

.composer--ComponentsFilledGallaryCover .components--DynamicDataView-PageCard {
  padding: 0px;
  overflow: hidden;
  border: 0px;
}

.composer--ComponentsFilledGallaryCover
  .components--DynamicDataView-PageCardCover,
.components--DynamicDataView-PageCardCover .components--Picture {
  border-radius: initial;
}

.composer--ComponentsFilledGallaryCover
  .components--DynamicDataView-PageCard
  .components--DynamicDataView-PageTitle {
  padding-top: 4px;
}

/* 
  Checkbox Styles is from Minimal Theme by @kepano
  ---------------------------------------------------------------------------
  
  Minimal Theme by @kepano
  
  User interface replacement for Obsidian.
  
  Designed to be used with the Minimal Theme Settings 
  plugin and the Hider plugin.
  
  Sponsor my work:
  https://www.buymeacoffee.com/kepano
  
  Readme:
  https://github.com/kepano/obsidian-minimal
  
  -----------------------------------------------------------------------------
  
  MIT License
  
  Copyright (c) 2020-2024 Steph Ango (@kepano)
  
  Permission is hereby granted, free of charge, to any person obtaining a copy
  of this software and associated documentation files (the "Software"), to deal
  in the Software without restriction, including without limitation the rights
  to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
  copies of the Software, and to permit persons to whom the Software is
  furnished to do so, subject to the following conditions:
  
  The above copyright notice and this permission notice shall be included in 
  all copies or substantial portions of the Software.
  
  THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
  IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
  FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
  AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
  LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
  OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE
  SOFTWARE.
  */
/* checkbox styles from minimal theme */
input[data-task=">"]:checked,
input[data-task="!"]:checked,
input[data-task="-"]:checked,
input[data-task="<"]:checked,
input[data-task="l"]:checked,
input[data-task="*"]:checked,
input[data-task="I"]:checked,
input[data-task="p"]:checked,
input[data-task="f"]:checked,
input[data-task="k"]:checked,
input[data-task="u"]:checked,
input[data-task="w"]:checked,
input[data-task="c"]:checked,
input[data-task="d"]:checked,
input[data-task="b"]:checked,
li[data-task=">"] > input:checked,
li[data-task="!"] > input:checked,
li[data-task="-"] > input:checked,
li[data-task="<"] > input:checked,
li[data-task="l"] > input:checked,
li[data-task="*"] > input:checked,
li[data-task="I"] > input:checked,
li[data-task="p"] > input:checked,
li[data-task="f"] > input:checked,
li[data-task="k"] > input:checked,
li[data-task="u"] > input:checked,
li[data-task="d"] > input:checked,
li[data-task="w"] > input:checked,
li[data-task="c"] > input:checked,
li[data-task="b"] > input:checked,
li[data-task=">"] > p > input:checked,
li[data-task="!"] > p > input:checked,
li[data-task="-"] > p > input:checked,
li[data-task="<"] > p > input:checked,
li[data-task="l"] > p > input:checked,
li[data-task="*"] > p > input:checked,
li[data-task="I"] > p > input:checked,
li[data-task="p"] > p > input:checked,
li[data-task="f"] > p > input:checked,
li[data-task="k"] > p > input:checked,
li[data-task="u"] > p > input:checked,
li[data-task="d"] > p > input:checked,
li[data-task="w"] > p > input:checked,
li[data-task="c"] > p > input:checked,
li[data-task="b"] > p > input:checked {
  --checkbox-marker-color: transparent;
  border: none;
  border-radius: 0;
  background-image: none;
  background-color: currentColor;
  -webkit-mask-size: var(--checkbox-icon);
  -webkit-mask-position: 50% 50%;
}

/* [>] Forwarded */
input[data-task=">"]:checked,
li[data-task=">"] > input:checked,
li[data-task=">"] > p > input:checked {
  color: var(--text-faint);
  transform: rotate(90deg);
  -webkit-mask-position: 50% 100%;
  -webkit-mask-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' class='h-5 w-5' viewBox='0 0 20 20' fill='currentColor'%3E%3Cpath d='M10.894 2.553a1 1 0 00-1.788 0l-7 14a1 1 0 001.169 1.409l5-1.429A1 1 0 009 15.571V11a1 1 0 112 0v4.571a1 1 0 00.725.962l5 1.428a1 1 0 001.17-1.408l-7-14z' /%3E%3C/svg%3E");
}

/* [<] Schedule */
input[data-task="<"]:checked,
li[data-task="<"] > input:checked,
li[data-task="<"] > p > input:checked {
  color: var(--text-faint);
  -webkit-mask-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' class='h-5 w-5' viewBox='0 0 20 20' fill='currentColor'%3E%3Cpath fill-rule='evenodd' d='M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z' clip-rule='evenodd' /%3E%3C/svg%3E");
  -webkit-mask-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' class='h-5 w-5' viewBox='0 0 20 20' fill='currentColor'%3E%3Cpath fill-rule='evenodd' d='M6 2a1 1 0 00-1 1v1H4a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2h-1V3a1 1 0 10-2 0v1H7V3a1 1 0 00-1-1zm0 5a1 1 0 000 2h8a1 1 0 100-2H6z' clip-rule='evenodd' /%3E%3C/svg%3E");
}

/* [?] Question */
input[data-task="?"]:checked,
li[data-task="?"] > input:checked,
li[data-task="?"] > p > input:checked {
  --checkbox-marker-color: transparent;
  background-color: rgba(var(--color-yellow-rgb), 1);
  border-color: rgba(var(--color-yellow-rgb), 1);
  background-position: 50% 50%;
  background-size: 200% 90%;
  background-image: url('data:image/svg+xml,%3Csvg xmlns="http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg" width="20" height="20" preserveAspectRatio="xMidYMid meet" viewBox="0 0 16 16"%3E%3Cpath fill="white" fill-rule="evenodd" d="M4.475 5.458c-.284 0-.514-.237-.47-.517C4.28 3.24 5.576 2 7.825 2c2.25 0 3.767 1.36 3.767 3.215c0 1.344-.665 2.288-1.79 2.973c-1.1.659-1.414 1.118-1.414 2.01v.03a.5.5 0 0 1-.5.5h-.77a.5.5 0 0 1-.5-.495l-.003-.2c-.043-1.221.477-2.001 1.645-2.712c1.03-.632 1.397-1.135 1.397-2.028c0-.979-.758-1.698-1.926-1.698c-1.009 0-1.71.529-1.938 1.402c-.066.254-.278.461-.54.461h-.777ZM7.496 14c.622 0 1.095-.474 1.095-1.09c0-.618-.473-1.092-1.095-1.092c-.606 0-1.087.474-1.087 1.091S6.89 14 7.496 14Z"%2F%3E%3C%2Fsvg%3E');
}
.theme-dark input[data-task="?"]:checked,
.theme-dark li[data-task="?"] > input:checked,
.theme-dark li[data-task="?"] > p > input:checked {
  background-image: url('data:image/svg+xml,%3Csvg xmlns="http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg" width="20" height="20" preserveAspectRatio="xMidYMid meet" viewBox="0 0 16 16"%3E%3Cpath fill="black" fill-opacity="0.8" fill-rule="evenodd" d="M4.475 5.458c-.284 0-.514-.237-.47-.517C4.28 3.24 5.576 2 7.825 2c2.25 0 3.767 1.36 3.767 3.215c0 1.344-.665 2.288-1.79 2.973c-1.1.659-1.414 1.118-1.414 2.01v.03a.5.5 0 0 1-.5.5h-.77a.5.5 0 0 1-.5-.495l-.003-.2c-.043-1.221.477-2.001 1.645-2.712c1.03-.632 1.397-1.135 1.397-2.028c0-.979-.758-1.698-1.926-1.698c-1.009 0-1.71.529-1.938 1.402c-.066.254-.278.461-.54.461h-.777ZM7.496 14c.622 0 1.095-.474 1.095-1.09c0-.618-.473-1.092-1.095-1.092c-.606 0-1.087.474-1.087 1.091S6.89 14 7.496 14Z"%2F%3E%3C%2Fsvg%3E');
}

/* [/] Incomplete */
input[data-task="/"]:checked,
li[data-task="/"] > input:checked,
li[data-task="/"] > p > input:checked {
  background-image: none;
  background-color: transparent;
  position: relative;
  overflow: hidden;
}
input[data-task="/"]:checked:after,
li[data-task="/"] > input:checked:after,
li[data-task="/"] > p > input:checked:after {
  top: 0;
  left: 0;
  content: " ";
  display: block;
  position: absolute;
  background-color: var(--background-modifier-accent);
  width: calc(50% - 0.5px);
  height: 100%;
  -webkit-mask-image: none;
}

/* [!] Important */
input[data-task="!"]:checked,
li[data-task="!"] > input:checked,
li[data-task="!"] > p > input:checked {
  color: rgba(var(--color-orange-rgb), 1);
  -webkit-mask-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' class='h-5 w-5' viewBox='0 0 20 20' fill='currentColor'%3E%3Cpath fill-rule='evenodd' d='M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z' clip-rule='evenodd' /%3E%3C/svg%3E");
}

/* ["] Quote */
input[data-task="“"]:checked,
li[data-task="“"] > input:checked,
li[data-task="“"] > p > input:checked,
input[data-task='"']:checked,
li[data-task='"'] > input:checked,
li[data-task='"'] > p > input:checked {
  --checkbox-marker-color: transparent;
  background-position: 50% 50%;
  background-color: rgba(var(--color-cyan-rgb), 1);
  border-color: rgba(var(--color-cyan-rgb), 1);
  background-size: 75%;
  background-repeat: no-repeat;
  background-image: url('data:image/svg+xml,%3Csvg xmlns="http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg" width="20" height="20" preserveAspectRatio="xMidYMid meet" viewBox="0 0 24 24"%3E%3Cpath fill="white" d="M6.5 10c-.223 0-.437.034-.65.065c.069-.232.14-.468.254-.68c.114-.308.292-.575.469-.844c.148-.291.409-.488.601-.737c.201-.242.475-.403.692-.604c.213-.21.492-.315.714-.463c.232-.133.434-.28.65-.35l.539-.222l.474-.197l-.485-1.938l-.597.144c-.191.048-.424.104-.689.171c-.271.05-.56.187-.882.312c-.318.142-.686.238-1.028.466c-.344.218-.741.4-1.091.692c-.339.301-.748.562-1.05.945c-.33.358-.656.734-.909 1.162c-.293.408-.492.856-.702 1.299c-.19.443-.343.896-.468 1.336c-.237.882-.343 1.72-.384 2.437c-.034.718-.014 1.315.028 1.747c.015.204.043.402.063.539l.025.168l.026-.006A4.5 4.5 0 1 0 6.5 10zm11 0c-.223 0-.437.034-.65.065c.069-.232.14-.468.254-.68c.114-.308.292-.575.469-.844c.148-.291.409-.488.601-.737c.201-.242.475-.403.692-.604c.213-.21.492-.315.714-.463c.232-.133.434-.28.65-.35l.539-.222l.474-.197l-.485-1.938l-.597.144c-.191.048-.424.104-.689.171c-.271.05-.56.187-.882.312c-.317.143-.686.238-1.028.467c-.344.218-.741.4-1.091.692c-.339.301-.748.562-1.05.944c-.33.358-.656.734-.909 1.162c-.293.408-.492.856-.702 1.299c-.19.443-.343.896-.468 1.336c-.237.882-.343 1.72-.384 2.437c-.034.718-.014 1.315.028 1.747c.015.204.043.402.063.539l.025.168l.026-.006A4.5 4.5 0 1 0 17.5 10z"%2F%3E%3C%2Fsvg%3E');
}
.theme-dark input[data-task="“"]:checked,
.theme-dark li[data-task="“"] > input:checked,
.theme-dark li[data-task="“"] > p > input:checked,
.theme-dark input[data-task='"']:checked,
.theme-dark li[data-task='"'] > input:checked,
.theme-dark li[data-task='"'] > p > input:checked {
  background-image: url('data:image/svg+xml,%3Csvg xmlns="http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg" width="20" height="20" preserveAspectRatio="xMidYMid meet" viewBox="0 0 24 24"%3E%3Cpath fill="black" fill-opacity="0.7" d="M6.5 10c-.223 0-.437.034-.65.065c.069-.232.14-.468.254-.68c.114-.308.292-.575.469-.844c.148-.291.409-.488.601-.737c.201-.242.475-.403.692-.604c.213-.21.492-.315.714-.463c.232-.133.434-.28.65-.35l.539-.222l.474-.197l-.485-1.938l-.597.144c-.191.048-.424.104-.689.171c-.271.05-.56.187-.882.312c-.318.142-.686.238-1.028.466c-.344.218-.741.4-1.091.692c-.339.301-.748.562-1.05.945c-.33.358-.656.734-.909 1.162c-.293.408-.492.856-.702 1.299c-.19.443-.343.896-.468 1.336c-.237.882-.343 1.72-.384 2.437c-.034.718-.014 1.315.028 1.747c.015.204.043.402.063.539l.025.168l.026-.006A4.5 4.5 0 1 0 6.5 10zm11 0c-.223 0-.437.034-.65.065c.069-.232.14-.468.254-.68c.114-.308.292-.575.469-.844c.148-.291.409-.488.601-.737c.201-.242.475-.403.692-.604c.213-.21.492-.315.714-.463c.232-.133.434-.28.65-.35l.539-.222l.474-.197l-.485-1.938l-.597.144c-.191.048-.424.104-.689.171c-.271.05-.56.187-.882.312c-.317.143-.686.238-1.028.467c-.344.218-.741.4-1.091.692c-.339.301-.748.562-1.05.944c-.33.358-.656.734-.909 1.162c-.293.408-.492.856-.702 1.299c-.19.443-.343.896-.468 1.336c-.237.882-.343 1.72-.384 2.437c-.034.718-.014 1.315.028 1.747c.015.204.043.402.063.539l.025.168l.026-.006A4.5 4.5 0 1 0 17.5 10z"%2F%3E%3C%2Fsvg%3E');
}

/* [-] Canceled */
input[data-task="-"]:checked,
li[data-task="-"] > input:checked,
li[data-task="-"] > p > input:checked {
  color: var(--text-faint);
  -webkit-mask-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' class='h-5 w-5' viewBox='0 0 20 20' fill='currentColor'%3E%3Cpath fill-rule='evenodd' d='M3 10a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1z' clip-rule='evenodd' /%3E%3C/svg%3E");
}

body:not(.tasks)
  .markdown-source-view.mod-cm6
  .HyperMD-task-line[data-task]:is([data-task="-"]),
body:not(.tasks)
  .markdown-preview-view
  ul
  li[data-task="-"].task-list-item.is-checked,
body:not(.tasks) li[data-task="-"].task-list-item.is-checked {
  color: var(--text-faint);
  text-decoration: line-through solid var(--text-faint) 1px;
}

/* [*] Star */
input[data-task="*"]:checked,
li[data-task="*"] > input:checked,
li[data-task="*"] > p > input:checked {
  color: rgba(var(--color-yellow-rgb), 1);
  -webkit-mask-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' class='h-5 w-5' viewBox='0 0 20 20' fill='currentColor'%3E%3Cpath d='M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z' /%3E%3C/svg%3E");
}

/* [l] Location */
input[data-task="l"]:checked,
li[data-task="l"] > input:checked,
li[data-task="l"] > p > input:checked {
  color: rgba(var(--color-red-rgb), 1);
  -webkit-mask-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' class='h-5 w-5' viewBox='0 0 20 20' fill='currentColor'%3E%3Cpath fill-rule='evenodd' d='M5.05 4.05a7 7 0 119.9 9.9L10 18.9l-4.95-4.95a7 7 0 010-9.9zM10 11a2 2 0 100-4 2 2 0 000 4z' clip-rule='evenodd' /%3E%3C/svg%3E");
}

/* [i] Info */
input[data-task="i"]:checked,
li[data-task="i"] > input:checked,
li[data-task="i"] > p > input:checked {
  --checkbox-marker-color: transparent;
  background-color: rgba(var(--color-blue-rgb), 1);
  border-color: rgba(var(--color-blue-rgb), 1);
  background-position: 50%;
  background-size: 100%;
  background-image: url('data:image/svg+xml,%3Csvg xmlns="http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg" width="20" height="20" preserveAspectRatio="xMidYMid meet" viewBox="0 0 512 512"%3E%3Cpath fill="none" stroke="white" stroke-linecap="round" stroke-linejoin="round" stroke-width="40" d="M196 220h64v172"%2F%3E%3Cpath fill="none" stroke="white" stroke-linecap="round" stroke-miterlimit="10" stroke-width="40" d="M187 396h138"%2F%3E%3Cpath fill="white" d="M256 160a32 32 0 1 1 32-32a32 32 0 0 1-32 32Z"%2F%3E%3C%2Fsvg%3E');
}
.theme-dark input[data-task="i"]:checked,
.theme-dark li[data-task="i"] > input:checked,
.theme-dark li[data-task="i"] > p > input:checked {
  background-image: url('data:image/svg+xml,%3Csvg xmlns="http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg" width="20" height="20" preserveAspectRatio="xMidYMid meet" viewBox="0 0 512 512"%3E%3Cpath fill="none" stroke="black" stroke-opacity="0.8" stroke-linecap="round" stroke-linejoin="round" stroke-width="40" d="M196 220h64v172"%2F%3E%3Cpath fill="none" stroke="black" stroke-opacity="0.8" stroke-linecap="round" stroke-miterlimit="10" stroke-width="40" d="M187 396h138"%2F%3E%3Cpath fill="black" fill-opacity="0.8" d="M256 160a32 32 0 1 1 32-32a32 32 0 0 1-32 32Z"%2F%3E%3C%2Fsvg%3E');
}

/* [S] Amount/savings/money */
input[data-task="S"]:checked,
li[data-task="S"] > input:checked,
li[data-task="S"] > p > input:checked {
  --checkbox-marker-color: transparent;
  border-color: rgba(var(--color-green-rgb), 1);
  background-color: rgba(var(--color-green-rgb), 1);
  background-size: 100%;
  background-image: url('data:image/svg+xml,%3Csvg xmlns="http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg" width="20" height="20" preserveAspectRatio="xMidYMid meet" viewBox="0 0 48 48"%3E%3Cpath fill="white" fill-rule="evenodd" d="M26 8a2 2 0 1 0-4 0v2a8 8 0 1 0 0 16v8a4.002 4.002 0 0 1-3.773-2.666a2 2 0 0 0-3.771 1.332A8.003 8.003 0 0 0 22 38v2a2 2 0 1 0 4 0v-2a8 8 0 1 0 0-16v-8a4.002 4.002 0 0 1 3.773 2.666a2 2 0 0 0 3.771-1.332A8.003 8.003 0 0 0 26 10V8Zm-4 6a4 4 0 0 0 0 8v-8Zm4 12v8a4 4 0 0 0 0-8Z" clip-rule="evenodd"%2F%3E%3C%2Fsvg%3E');
}
.theme-dark input[data-task="S"]:checked,
.theme-dark li[data-task="S"] > input:checked,
.theme-dark li[data-task="S"] > p > input:checked {
  background-image: url('data:image/svg+xml,%3Csvg xmlns="http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg" width="20" height="20" preserveAspectRatio="xMidYMid meet" viewBox="0 0 48 48"%3E%3Cpath fill-opacity="0.8" fill="black" fill-rule="evenodd" d="M26 8a2 2 0 1 0-4 0v2a8 8 0 1 0 0 16v8a4.002 4.002 0 0 1-3.773-2.666a2 2 0 0 0-3.771 1.332A8.003 8.003 0 0 0 22 38v2a2 2 0 1 0 4 0v-2a8 8 0 1 0 0-16v-8a4.002 4.002 0 0 1 3.773 2.666a2 2 0 0 0 3.771-1.332A8.003 8.003 0 0 0 26 10V8Zm-4 6a4 4 0 0 0 0 8v-8Zm4 12v8a4 4 0 0 0 0-8Z" clip-rule="evenodd"%2F%3E%3C%2Fsvg%3E');
}

/* [I] Idea/lightbulb */
input[data-task="I"]:checked,
li[data-task="I"] > input:checked,
li[data-task="I"] > p > input:checked {
  color: rgba(var(--color-yellow-rgb), 1);
  -webkit-mask-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' class='h-5 w-5' viewBox='0 0 20 20' fill='currentColor'%3E%3Cpath d='M11 3a1 1 0 10-2 0v1a1 1 0 102 0V3zM15.657 5.757a1 1 0 00-1.414-1.414l-.707.707a1 1 0 001.414 1.414l.707-.707zM18 10a1 1 0 01-1 1h-1a1 1 0 110-2h1a1 1 0 011 1zM5.05 6.464A1 1 0 106.464 5.05l-.707-.707a1 1 0 00-1.414 1.414l.707.707zM5 10a1 1 0 01-1 1H3a1 1 0 110-2h1a1 1 0 011 1zM8 16v-1h4v1a2 2 0 11-4 0zM12 14c.015-.34.208-.646.477-.859a4 4 0 10-4.954 0c.27.213.462.519.476.859h4.002z' /%3E%3C/svg%3E");
}

/* [f] Fire */
input[data-task="f"]:checked,
li[data-task="f"] > input:checked,
li[data-task="f"] > p > input:checked {
  color: rgba(var(--color-red-rgb), 1);
  -webkit-mask-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' class='h-5 w-5' viewBox='0 0 20 20' fill='currentColor'%3E%3Cpath fill-rule='evenodd' d='M12.395 2.553a1 1 0 00-1.45-.385c-.345.23-.614.558-.822.88-.214.33-.403.713-.57 1.116-.334.804-.614 1.768-.84 2.734a31.365 31.365 0 00-.613 3.58 2.64 2.64 0 01-.945-1.067c-.328-.68-.398-1.534-.398-2.654A1 1 0 005.05 6.05 6.981 6.981 0 003 11a7 7 0 1011.95-4.95c-.592-.591-.98-.985-1.348-1.467-.363-.476-.724-1.063-1.207-2.03zM12.12 15.12A3 3 0 017 13s.879.5 2.5.5c0-1 .5-4 1.25-4.5.5 1 .786 1.293 1.371 1.879A2.99 2.99 0 0113 13a2.99 2.99 0 01-.879 2.121z' clip-rule='evenodd' /%3E%3C/svg%3E");
}

/* [k] Key */
input[data-task="k"]:checked,
li[data-task="k"] > input:checked,
li[data-task="k"] > p > input:checked {
  color: rgba(var(--color-yellow-rgb), 1);
  -webkit-mask-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' class='h-5 w-5' viewBox='0 0 20 20' fill='currentColor'%3E%3Cpath fill-rule='evenodd' d='M18 8a6 6 0 01-7.743 5.743L10 14l-1 1-1 1H6v2H2v-4l4.257-4.257A6 6 0 1118 8zm-6-4a1 1 0 100 2 2 2 0 012 2 1 1 0 102 0 4 4 0 00-4-4z' clip-rule='evenodd' /%3E%3C/svg%3E");
}

/* [u] Up */
input[data-task="u"]:checked,
li[data-task="u"] > input:checked,
li[data-task="u"] > p > input:checked {
  color: rgba(var(--color-red-green), 1);
  -webkit-mask-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' class='h-5 w-5' viewBox='0 0 20 20' fill='currentColor'%3E%3Cpath fill-rule='evenodd' d='M12 7a1 1 0 110-2h5a1 1 0 011 1v5a1 1 0 11-2 0V8.414l-4.293 4.293a1 1 0 01-1.414 0L8 10.414l-4.293 4.293a1 1 0 01-1.414-1.414l5-5a1 1 0 011.414 0L11 10.586 14.586 7H12z' clip-rule='evenodd' /%3E%3C/svg%3E");
}

/* [d] Down */
input[data-task="d"]:checked,
li[data-task="d"] > input:checked,
li[data-task="d"] > p > input:checked {
  color: rgba(var(--color-red-rgb), 1);
  -webkit-mask-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' class='h-5 w-5' viewBox='0 0 20 20' fill='currentColor'%3E%3Cpath fill-rule='evenodd' d='M12 13a1 1 0 100 2h5a1 1 0 001-1V9a1 1 0 10-2 0v2.586l-4.293-4.293a1 1 0 00-1.414 0L8 9.586 3.707 5.293a1 1 0 00-1.414 1.414l5 5a1 1 0 001.414 0L11 9.414 14.586 13H12z' clip-rule='evenodd' /%3E%3C/svg%3E");
}

/* [w] Win */
input[data-task="w"]:checked,
li[data-task="w"] > input:checked,
li[data-task="w"] > p > input:checked {
  color: rgba(var(--color-purple-rgb), 1);
  -webkit-mask-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' class='h-5 w-5' viewBox='0 0 20 20' fill='currentColor'%3E%3Cpath fill-rule='evenodd' d='M6 3a1 1 0 011-1h.01a1 1 0 010 2H7a1 1 0 01-1-1zm2 3a1 1 0 00-2 0v1a2 2 0 00-2 2v1a2 2 0 00-2 2v.683a3.7 3.7 0 011.055.485 1.704 1.704 0 001.89 0 3.704 3.704 0 014.11 0 1.704 1.704 0 001.89 0 3.704 3.704 0 014.11 0 1.704 1.704 0 001.89 0A3.7 3.7 0 0118 12.683V12a2 2 0 00-2-2V9a2 2 0 00-2-2V6a1 1 0 10-2 0v1h-1V6a1 1 0 10-2 0v1H8V6zm10 8.868a3.704 3.704 0 01-4.055-.036 1.704 1.704 0 00-1.89 0 3.704 3.704 0 01-4.11 0 1.704 1.704 0 00-1.89 0A3.704 3.704 0 012 14.868V17a1 1 0 001 1h14a1 1 0 001-1v-2.132zM9 3a1 1 0 011-1h.01a1 1 0 110 2H10a1 1 0 01-1-1zm3 0a1 1 0 011-1h.01a1 1 0 110 2H13a1 1 0 01-1-1z' clip-rule='evenodd' /%3E%3C/svg%3E");
}

/* [p] Pros */
input[data-task="p"]:checked,
li[data-task="p"] > input:checked,
li[data-task="p"] > p > input:checked {
  color: rgba(var(--color-green-rgb), 1);
  -webkit-mask-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' class='h-5 w-5' viewBox='0 0 20 20' fill='currentColor'%3E%3Cpath d='M2 10.5a1.5 1.5 0 113 0v6a1.5 1.5 0 01-3 0v-6zM6 10.333v5.43a2 2 0 001.106 1.79l.05.025A4 4 0 008.943 18h5.416a2 2 0 001.962-1.608l1.2-6A2 2 0 0015.56 8H12V4a2 2 0 00-2-2 1 1 0 00-1 1v.667a4 4 0 01-.8 2.4L6.8 7.933a4 4 0 00-.8 2.4z' /%3E%3C/svg%3E");
}

/* [c] Cons */
input[data-task="c"]:checked,
li[data-task="c"] > input:checked,
li[data-task="c"] > p > input:checked {
  color: rgba(var(--color-orange-rgb), 1);
  -webkit-mask-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' class='h-5 w-5' viewBox='0 0 20 20' fill='currentColor'%3E%3Cpath d='M18 9.5a1.5 1.5 0 11-3 0v-6a1.5 1.5 0 013 0v6zM14 9.667v-5.43a2 2 0 00-1.105-1.79l-.05-.025A4 4 0 0011.055 2H5.64a2 2 0 00-1.962 1.608l-1.2 6A2 2 0 004.44 12H8v4a2 2 0 002 2 1 1 0 001-1v-.667a4 4 0 01.8-2.4l1.4-1.866a4 4 0 00.8-2.4z' /%3E%3C/svg%3E");
}

/* [b] Bookmark */
input[data-task="b"]:checked,
li[data-task="b"] > input:checked,
li[data-task="b"] > p > input:checked {
  color: rgba(var(--color-orange-rgb), 1);
  -webkit-mask-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' class='h-5 w-5' viewBox='0 0 20 20' fill='currentColor'%3E%3Cpath d='M5 4a2 2 0 012-2h6a2 2 0 012 2v14l-5-2.5L5 18V4z' /%3E%3C/svg%3E");
}
