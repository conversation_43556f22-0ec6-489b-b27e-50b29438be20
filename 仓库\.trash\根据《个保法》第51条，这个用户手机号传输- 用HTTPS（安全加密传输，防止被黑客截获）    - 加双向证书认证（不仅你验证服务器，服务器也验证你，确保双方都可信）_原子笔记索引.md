# 根据《个保法》第51条，这个用户手机号传输- 用HTTPS（安全加密传输，防止被黑客截获）    - 加双向证书认证（不仅你验证服务器，服务器也验证你，确保双方都可信） - 原子笔记索引

> 本索引由原子笔记切分工具自动生成
> 生成时间: 2025/8/1 15:25:35
> 原始笔记: [[根据《个保法》第51条，这个用户手机号传输- 用HTTPS（安全加密传输，防止被黑客截获）    - 加双向证书认证（不仅你验证服务器，服务器也验证你，确保双方都可信）]]

## 统计信息
- 原始笔记: [[根据《个保法》第51条，这个用户手机号传输- 用HTTPS（安全加密传输，防止被黑客截获）    - 加双向证书认证（不仅你验证服务器，服务器也验证你，确保双方都可信）]]
- 切分出的原子笔记数量: 5
- 生成时间: 2025/8/1 15:25:35

## 原子笔记列表

1. [[个保法第51条对用户手机号传输的要求]] - 个保法第51条对用户手机号传输的要求
2. [[HTTPS在用户手机号传输中的应用]] - HTTPS在用户手机号传输中的应用
3. [[双向证书认证在用户手机号传输中的作用]] - 双向证书认证在用户手机号传输中的作用
4. [[GB_T_35273-2020对个人信息安全的要求]] - GB/T 35273-2020对个人信息安全的要求
5. [[未加密传输手机号的法律风险]] - 未加密传输手机号的法律风险

## 标签分类

### #个人信息保护法
- [[个保法第51条对用户手机号传输的要求]]

### #个保法第51条
- [[个保法第51条对用户手机号传输的要求]]

### #手机号传输
- [[个保法第51条对用户手机号传输的要求]]
- [[未加密传输手机号的法律风险]]

### #HTTPS
- [[HTTPS在用户手机号传输中的应用]]

### #加密
- [[HTTPS在用户手机号传输中的应用]]

### #数据传输
- [[HTTPS在用户手机号传输中的应用]]

### #手机号安全
- [[HTTPS在用户手机号传输中的应用]]
- [[双向证书认证在用户手机号传输中的作用]]
- [[GB_T_35273-2020对个人信息安全的要求]]

### #双向证书认证
- [[双向证书认证在用户手机号传输中的作用]]

### #身份认证
- [[双向证书认证在用户手机号传输中的作用]]

### #通信安全
- [[双向证书认证在用户手机号传输中的作用]]

### #GB/T 35273-2020
- [[GB_T_35273-2020对个人信息安全的要求]]

### #信息安全技术
- [[GB_T_35273-2020对个人信息安全的要求]]

### #个人信息安全
- [[GB_T_35273-2020对个人信息安全的要求]]

### #个保法
- [[未加密传输手机号的法律风险]]

### #法律风险
- [[未加密传输手机号的法律风险]]

### #违法
- [[未加密传输手机号的法律风险]]

---
*此索引文件由原子笔记切分工具生成*
