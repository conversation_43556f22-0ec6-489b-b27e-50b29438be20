# 官方同步 vs 自建同步：为什么更安全？ - 原子笔记索引

> 本索引由原子笔记切分工具自动生成
> 生成时间: 2025/8/2 20:22:30
> 原始笔记: [[官方同步 vs 自建同步：为什么更安全？]]

## 统计信息
- 原始笔记: [[官方同步 vs 自建同步：为什么更安全？]]
- 切分出的原子笔记数量: 14
- 生成时间: 2025/8/2 20:22:30

## 原子笔记列表

1. [[什么是“自建HTTP”？]] - 什么是“自建HTTP”？
2. [[为什么需要HTTPS？]] - 为什么需要HTTPS？
3. [[自建HTTP同步的典型场景]] - 自建HTTP同步的典型场景
4. [[普通人该怎么选？]] - 普通人该怎么选？
5. [[官方同步为什么安全？]] - 官方同步为什么安全？
6. [[自建方案（如LiveSync）为什么安全？]] - 自建方案（如LiveSync）为什么安全？
7. [[为什么免费方案（如iCloud_Remotely_Save）容易泄露？]] - 为什么免费方案（如iCloud/Remotely Save）容易泄露？
8. [[安全等级对比]] - 安全等级对比
9. [[如何最大化安全？]] - 如何最大化安全？
10. [[自建LiveSync的服务器归属问题]] - 自建LiveSync的服务器归属问题
11. [[Fly.io（你说的F）到底是什么角色？]] - Fly.io（你说的F）到底是什么角色？
12. [[为什么说自建更安全？你随时可以 搬家（把数据库导出到其他服务器）    - 你随时可以 销毁数据（`docker rm -f couchdb` 一秒删库）🔒]] - 为什么说自建更安全？
13. [[风险提醒]] - 风险提醒
14. [[下一步建议]] - 下一步建议

## 标签分类

### #自建HTTP
- [[什么是“自建HTTP”？]]
- [[自建HTTP同步的典型场景]]

### #同步方式
- [[什么是“自建HTTP”？]]

### #HTTPS
- [[为什么需要HTTPS？]]

### #加密
- [[为什么需要HTTPS？]]
- [[Fly.io（你说的F）到底是什么角色？]]

### #同步安全
- [[为什么需要HTTPS？]]

### #同步场景
- [[自建HTTP同步的典型场景]]

### #安全方案
- [[自建HTTP同步的典型场景]]

### #同步选择
- [[普通人该怎么选？]]

### #安全性
- [[普通人该怎么选？]]
- [[官方同步为什么安全？]]
- [[自建方案（如LiveSync）为什么安全？]]

### #适用人群
- [[普通人该怎么选？]]

### #官方同步
- [[官方同步为什么安全？]]
- [[安全等级对比]]
- [[如何最大化安全？]]

### #保障措施
- [[官方同步为什么安全？]]
- [[自建方案（如LiveSync）为什么安全？]]

### #自建方案
- [[自建方案（如LiveSync）为什么安全？]]

### #免费方案
- [[为什么免费方案（如iCloud_Remotely_Save）容易泄露？]]

### #安全风险
- [[为什么免费方案（如iCloud_Remotely_Save）容易泄露？]]

### #泄露原因
- [[为什么免费方案（如iCloud_Remotely_Save）容易泄露？]]

### #安全等级
- [[安全等级对比]]

### #对比
- [[安全等级对比]]

### #自建LiveSync
- [[安全等级对比]]
- [[自建LiveSync的服务器归属问题]]

### #iCloud/OneDrive
- [[安全等级对比]]

### #安全最大化
- [[如何最大化安全？]]

### #自建服务器
- [[如何最大化安全？]]
- [[风险提醒]]

### #安全措施
- [[如何最大化安全？]]

### #服务器归属
- [[自建LiveSync的服务器归属问题]]

### #Fly.io
- [[自建LiveSync的服务器归属问题]]
- [[Fly.io（你说的F）到底是什么角色？]]
- [[为什么说自建更安全？你随时可以 搬家（把数据库导出到其他服务器）    - 你随时可以 销毁数据（`docker rm -f couchdb` 一秒删库）🔒]]
- [[风险提醒]]
- [[下一步建议]]

### #Obsidian官方
- [[自建LiveSync的服务器归属问题]]
- [[Fly.io（你说的F）到底是什么角色？]]
- [[为什么说自建更安全？你随时可以 搬家（把数据库导出到其他服务器）    - 你随时可以 销毁数据（`docker rm -f couchdb` 一秒删库）🔒]]

### #角色
- [[Fly.io（你说的F）到底是什么角色？]]

### #数据控制
- [[Fly.io（你说的F）到底是什么角色？]]

### #自建安全
- [[为什么说自建更安全？你随时可以 搬家（把数据库导出到其他服务器）    - 你随时可以 销毁数据（`docker rm -f couchdb` 一秒删库）🔒]]

### #隐私性
- [[为什么说自建更安全？你随时可以 搬家（把数据库导出到其他服务器）    - 你随时可以 销毁数据（`docker rm -f couchdb` 一秒删库）🔒]]

### #可控性
- [[为什么说自建更安全？你随时可以 搬家（把数据库导出到其他服务器）    - 你随时可以 销毁数据（`docker rm -f couchdb` 一秒删库）🔒]]

### #风险提醒
- [[风险提醒]]

### #数据安全
- [[风险提醒]]

### #下一步建议
- [[下一步建议]]

### #设置
- [[下一步建议]]

### #备份策略
- [[下一步建议]]

---
*此索引文件由原子笔记切分工具生成*
