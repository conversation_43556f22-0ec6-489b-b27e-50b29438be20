#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单孤立笔记链接器
为孤立笔记（链接少的笔记）添加相关链接
"""

import os
import re
from pathlib import Path
from datetime import datetime

class SimpleNoteLinkGenerator:
    def __init__(self, base_dir):
        self.base_dir = Path(base_dir)
        self.all_notes = {}
        self.processed_count = 0
        
    def get_keywords(self):
        """简化的关键词库"""
        return [
            '股权', '融资', '投资', '对赌', '控制权', '董事会',
            '法律', '合规', '风险', '合同', '协议',
            '财务', '现金流', '利润', '成本', '税务',
            '管理', '战略', '运营', '团队', '领导',
            '市场', '营销', '客户', '产品', '品牌'
        ]
    
    def extract_links(self, content):
        """提取现有链接"""
        return re.findall(r'\[\[([^\]]+)\]\]', content)
    
    def extract_keywords(self, text):
        """提取关键词"""
        found = []
        keywords = self.get_keywords()
        for keyword in keywords:
            if keyword in text:
                found.append(keyword)
        return found
    
    def should_exclude(self, file_path):
        """判断是否排除文件"""
        path_str = str(file_path).lower()
        excludes = ['默写本', '.obsidian', '.trash', '原子笔记', '知识卡片', 'template']
        return any(ex in path_str for ex in excludes)
    
    def analyze_note(self, file_path):
        """分析笔记"""
        if self.should_exclude(file_path):
            return None
            
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
        except:
            return None
        
        title = file_path.stem
        links = self.extract_links(content)
        keywords = self.extract_keywords(f"{title} {content}")
        
        return {
            'path': file_path,
            'title': title,
            'content': content,
            'links': links,
            'link_count': len(links),
            'keywords': keywords
        }
    
    def find_similar_notes(self, target_note, max_results=3):
        """找到相似笔记"""
        similar = []
        target_keywords = set(target_note['keywords'])
        target_words = set(target_note['title'].split())
        
        for other_title, other_note in self.all_notes.items():
            if other_title == target_note['title']:
                continue
                
            # 如果已经有链接，跳过
            if other_title in target_note['links']:
                continue
            
            other_keywords = set(other_note['keywords'])
            other_words = set(other_note['title'].split())
            
            # 计算相似度
            score = 0
            
            # 关键词匹配
            keyword_match = len(target_keywords & other_keywords)
            score += keyword_match * 10
            
            # 标题词匹配
            title_match = len(target_words & other_words)
            score += title_match * 5
            
            # 标题包含
            for word in target_words:
                if len(word) > 2 and word in other_note['title']:
                    score += 3
            
            if score >= 8:  # 相似度阈值
                similar.append((other_note, score))
        
        # 排序并返回前几个
        similar.sort(key=lambda x: x[1], reverse=True)
        return similar[:max_results]
    
    def add_links(self, note, similar_notes):
        """为笔记添加链接"""
        if not similar_notes:
            return False
            
        content = note['content']
        
        # 添加相关笔记部分
        if '## 相关笔记' not in content:
            content += f"\n\n---\n\n## 相关笔记\n"
            content += f"*自动生成于 {datetime.now().strftime('%Y-%m-%d')}*\n\n"
            
            for similar_note, score in similar_notes:
                content += f"- [[{similar_note['title']}]]\n"
        
        # 保存文件
        try:
            with open(note['path'], 'w', encoding='utf-8') as f:
                f.write(content)
            return True
        except:
            return False
    
    def run(self):
        """运行链接器"""
        print("简单孤立笔记链接器")
        print("=" * 30)
        
        # 收集笔记
        print("收集笔记...")
        md_files = list(self.base_dir.rglob("*.md"))
        
        for file_path in md_files:
            note = self.analyze_note(file_path)
            if note:
                self.all_notes[note['title']] = note
        
        print(f"找到 {len(self.all_notes)} 个笔记")
        
        # 找到孤立笔记（链接数 <= 1）
        isolated_notes = []
        for note in self.all_notes.values():
            if note['link_count'] <= 1:
                isolated_notes.append(note)
        
        print(f"发现 {len(isolated_notes)} 个孤立笔记")
        
        # 为孤立笔记添加链接
        print("\n开始添加链接...")
        for note in isolated_notes:
            print(f"处理: {note['title']}")
            
            similar_notes = self.find_similar_notes(note)
            
            if similar_notes:
                if self.add_links(note, similar_notes):
                    self.processed_count += 1
                    print(f"  ✓ 添加了 {len(similar_notes)} 个链接")
                else:
                    print(f"  ✗ 添加失败")
            else:
                print(f"  - 未找到相关笔记")
        
        print(f"\n完成！成功为 {self.processed_count} 个笔记添加了链接")

if __name__ == "__main__":
    base_dir = Path(r"c:\Users\<USER>\OneDrive\obsidian笔记系统\战略与战术")
    
    generator = SimpleNoteLinkGenerator(base_dir)
    generator.run()
