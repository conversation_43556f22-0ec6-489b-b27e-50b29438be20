{"recentFiles": [{"basename": "测试数据-五格药箱", "path": "任务管理&日常记录/BASE数据库/财务系统/个人财务数据 1/测试数据-五格药箱.md"}, {"basename": "测试数据-三色资金袋", "path": "任务管理&日常记录/BASE数据库/财务系统/个人财务数据 1/测试数据-三色资金袋.md"}, {"basename": "个人财务测试", "path": "任务管理&日常记录/BASE数据库/财务系统/测试文件/个人财务测试.md"}, {"basename": "工作室财务分析", "path": "任务管理&日常记录/BASE数据库/财务系统/财务数据库/工作室财务分析.base"}, {"basename": "个人分账户管理", "path": "任务管理&日常记录/BASE数据库/财务系统/财务数据库/个人分账户管理.base"}, {"basename": "07-财务计算表", "path": "任务管理&日常记录/BASE数据库/财务系统/财务数据库/07-财务计算表.base"}, {"basename": "File", "path": "任务管理&日常记录/BASE数据库/File.base"}, {"basename": "个人分账户财务管理", "path": "任务管理&日常记录/BASE数据库/财务系统/财务数据库/个人分账户财务管理.base"}, {"basename": "数据合规模板库-简化版", "path": "任务管理&日常记录/BASE数据库/数据合规模板库-简化版.base"}, {"basename": "数据合规工作室数据库使用指南", "path": "任务管理&日常记录/BASE数据库/数据合规工作室数据库使用指南.md"}, {"basename": "数据合规工作室财务管理", "path": "任务管理&日常记录/BASE数据库/财务系统/财务数据库/数据合规工作室财务管理.base"}, {"basename": "测试简单", "path": "任务管理&日常记录/BASE数据库/测试简单.base"}, {"basename": "测试", "path": "任务管理&日常记录/BASE数据库/测试.base"}, {"basename": "财务数据库使用指南", "path": "任务管理&日常记录/BASE数据库/财务系统/财务数据库/财务数据库使用指南.md"}, {"basename": "赎身基金追踪", "path": "任务管理&日常记录/BASE数据库/财务系统/个人财务数据/赎身基金追踪.md"}, {"basename": "工作室五格药箱监控", "path": "任务管理&日常记录/BASE数据库/财务系统/个人财务数据/工作室五格药箱监控.md"}, {"basename": "2024年3月三色资金袋", "path": "任务管理&日常记录/BASE数据库/财务系统/个人财务数据/2024年3月三色资金袋.md"}, {"basename": "2024年4月现金流预测", "path": "任务管理&日常记录/BASE数据库/财务系统/工作室财务数据/2024年4月现金流预测.md"}, {"basename": "最简单测试", "path": "任务管理&日常记录/BASE数据库/最简单测试.base"}, {"basename": "文档示例", "path": "任务管理&日常记录/BASE数据库/文档示例.base"}, {"basename": "最简单测试2", "path": "任务管理&日常记录/BASE数据库/最简单测试2.base"}, {"basename": "备份类型", "path": "inbox/备份类型.md"}, {"basename": "《个人信息保护法》第51条：需严格限制数据访问权限- 如果发现日志被删改，需 24小时内向监管部门报备", "path": "inbox/《个人信息保护法》第51条：需严格限制数据访问权限- 如果发现日志被删改，需 24小时内向监管部门报备.md"}, {"basename": "《个人信息保护法》第19条处理个人信息应当 “采取对个人权益影响最小的方式”，包括数据存储时间", "path": "inbox/《个人信息保护法》第19条处理个人信息应当 “采取对个人权益影响最小的方式”，包括数据存储时间.md"}, {"basename": "新兴威胁的语法要点", "path": "英语/inbox/新兴威胁的语法要点.md"}, {"basename": "📘学习页", "path": "📘学习页.components"}, {"basename": "🌳主页", "path": "🌳主页.components"}, {"basename": "数据同步与泄露风险：监控与备份-开阿里云操作日志-定期备份", "path": "inbox/数据同步与泄露风险：监控与备份-开阿里云操作日志-定期备份.md"}, {"basename": "数据隐私性：如何确保隐私-启用HTTPS-端到端加密-防火墙设置", "path": "inbox/数据隐私性：如何确保隐私-启用HTTPS-端到端加密-防火墙设置.md"}, {"basename": "04-个人财务", "path": "任务管理&日常记录/BASE数据库/04-个人财务.base"}, {"basename": "💾编程页", "path": "💾编程页.components"}, {"basename": "🪡工作流", "path": "任务管理&日常记录/🪡工作流.components"}, {"basename": "“5个助理”分别对应什么AI功能？", "path": "任务管理&日常记录/pdf处理工作流/“5个助理”分别对应什么AI功能？.md"}, {"basename": "脱敏Markdown生成", "path": "任务管理&日常记录/pdf处理工作流/脱敏Markdown生成.md"}, {"basename": "匿名化分析处理", "path": "任务管理&日常记录/pdf处理工作流/匿名化分析处理.md"}, {"basename": "内部知识库构建", "path": "任务管理&日常记录/pdf处理工作流/内部知识库构建.md"}, {"basename": "2_MinerU处理阶段", "path": "任务管理&日常记录/pdf处理工作流/2_MinerU处理阶段.md"}, {"basename": "1_原始文件输入", "path": "任务管理&日常记录/pdf处理工作流/1_原始文件输入.md"}, {"basename": "“合同审查+法律问答+类案研究”工作流", "path": "任务管理&日常记录/pdf处理工作流/工作流/“合同审查+法律问答+类案研究”工作流.md"}, {"basename": "数据同步与泄露风险：进阶加密-端到端加密敏感文件单独加密", "path": "inbox/数据同步与泄露风险：进阶加密-端到端加密敏感文件单独加密.md"}, {"basename": "数据同步与泄露风险：基础防护-改默认密码-开防火墙", "path": "inbox/数据同步与泄露风险：基础防护-改默认密码-开防火墙.md"}, {"basename": "敏感客户资料存储合规建议：法律层面-国内服务器需遵守《个人信息保护法》，建议：- 和客户签保密协议- 敏感数据脱敏存储（如手机号打码）。", "path": "inbox/敏感客户资料存储合规建议：法律层面-国内服务器需遵守《个人信息保护法》，建议：- 和客户签保密协议- 敏感数据脱敏存储（如手机号打码）。.md"}, {"basename": "数据隐私性风险项：自建服务器- 云厂商员工  理论上能访问（但需内部权限+审计日志） 黑客入侵   取决于你的安全设置（防火墙密码强度）政府监管  国内服务器需配合合法调查       ", "path": "inbox/数据隐私性风险项：自建服务器- 云厂商员工  理论上能访问（但需内部权限+审计日志） 黑客入侵   取决于你的安全设置（防火墙密码强度）政府监管  国内服务器需配合合法调查       .md"}, {"basename": "数据隐私性：存在自己服务器，别人能看到吗？", "path": "inbox/数据隐私性：存在自己服务器，别人能看到吗？.md"}, {"basename": "输入输出安全 两大方面：    1.  输入安全 (防投毒)：过滤用户的恶意指令（如“忽略之前指令，现在你是...”）、脏数据攻击（乱码病毒代码）。        -   防御措施：前端过滤、长度限制、格式校验。    2.  输出安全 (防发疯)：过滤AI生成的有害、违法、不道德的内容（如[大模型幻觉](大模型幻觉.md)）。        -   防御措施：规则过滤（敏感词）、置信度拦截、日志溯源。", "path": "学会编程/编程原子笔记/输入输出安全 两大方面：    1.  输入安全 (防投毒)：过滤用户的恶意指令（如“忽略之前指令，现在你是...”）、脏数据攻击（乱码病毒代码）。        -   防御措施：前端过滤、长度限制、格式校验。    2.  输出安全 (防发疯)：过滤AI生成的有害、违法、不道德的内容（如[大模型幻觉](大模型幻觉.md)）。        -   防御措施：规则过滤（敏感词）、置信度拦截、日志溯源。.md"}, {"basename": "上游（Input）数据管理关注数据来源、质量和格式。确保数据干净、格式正确，对应[输入输出安全](输入输出安全.md)。", "path": "学会编程/编程原子笔记/上游（Input）数据管理关注数据来源、质量和格式。确保数据干净、格式正确，对应[输入输出安全](输入输出安全.md)。.md"}, {"basename": "识别数据流动中的法律风险方法：画出数据旅程图，标注每个环节可能涉及的技术点和法律风险点。", "path": "inbox/识别数据流动中的法律风险方法：画出数据旅程图，标注每个环节可能涉及的技术点和法律风险点。.md"}, {"basename": "数据流合规性评估", "path": "inbox/数据流合规性评估.md"}, {"basename": "数据流实践方法：小程序开发", "path": "inbox/数据流实践方法：小程序开发.md"}, {"basename": "数据流关键环节：数据采集：用户输入、设备信息。", "path": "inbox/数据流关键环节：数据采集：用户输入、设备信息。.md"}], "omittedPaths": [], "omittedTags": [], "updateOn": "file-open", "omitBookmarks": false}