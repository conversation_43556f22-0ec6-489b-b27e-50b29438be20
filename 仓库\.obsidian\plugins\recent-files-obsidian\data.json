{"recentFiles": [{"basename": "04-个人财务", "path": "任务管理&日常记录/BASE数据库/04-个人财务.base"}, {"basename": "💾编程页", "path": "💾编程页.components"}, {"basename": "📘学习页", "path": "📘学习页.components"}, {"basename": "🪡工作流", "path": "任务管理&日常记录/🪡工作流.components"}, {"basename": "“5个助理”分别对应什么AI功能？", "path": "任务管理&日常记录/pdf处理工作流/“5个助理”分别对应什么AI功能？.md"}, {"basename": "脱敏Markdown生成", "path": "任务管理&日常记录/pdf处理工作流/脱敏Markdown生成.md"}, {"basename": "匿名化分析处理", "path": "任务管理&日常记录/pdf处理工作流/匿名化分析处理.md"}, {"basename": "内部知识库构建", "path": "任务管理&日常记录/pdf处理工作流/内部知识库构建.md"}, {"basename": "2_MinerU处理阶段", "path": "任务管理&日常记录/pdf处理工作流/2_MinerU处理阶段.md"}, {"basename": "1_原始文件输入", "path": "任务管理&日常记录/pdf处理工作流/1_原始文件输入.md"}, {"basename": "“合同审查+法律问答+类案研究”工作流", "path": "任务管理&日常记录/pdf处理工作流/工作流/“合同审查+法律问答+类案研究”工作流.md"}, {"basename": "数据隐私性：如何确保隐私-启用HTTPS-端到端加密-防火墙设置", "path": "inbox/数据隐私性：如何确保隐私-启用HTTPS-端到端加密-防火墙设置.md"}, {"basename": "数据同步与泄露风险：监控与备份-开阿里云操作日志-定期备份", "path": "inbox/数据同步与泄露风险：监控与备份-开阿里云操作日志-定期备份.md"}, {"basename": "数据同步与泄露风险：进阶加密-端到端加密敏感文件单独加密", "path": "inbox/数据同步与泄露风险：进阶加密-端到端加密敏感文件单独加密.md"}, {"basename": "数据同步与泄露风险：基础防护-改默认密码-开防火墙", "path": "inbox/数据同步与泄露风险：基础防护-改默认密码-开防火墙.md"}, {"basename": "敏感客户资料存储合规建议：法律层面-国内服务器需遵守《个人信息保护法》，建议：- 和客户签保密协议- 敏感数据脱敏存储（如手机号打码）。", "path": "inbox/敏感客户资料存储合规建议：法律层面-国内服务器需遵守《个人信息保护法》，建议：- 和客户签保密协议- 敏感数据脱敏存储（如手机号打码）。.md"}, {"basename": "数据隐私性风险项：自建服务器- 云厂商员工  理论上能访问（但需内部权限+审计日志） 黑客入侵   取决于你的安全设置（防火墙密码强度）政府监管  国内服务器需配合合法调查       ", "path": "inbox/数据隐私性风险项：自建服务器- 云厂商员工  理论上能访问（但需内部权限+审计日志） 黑客入侵   取决于你的安全设置（防火墙密码强度）政府监管  国内服务器需配合合法调查       .md"}, {"basename": "数据隐私性：存在自己服务器，别人能看到吗？", "path": "inbox/数据隐私性：存在自己服务器，别人能看到吗？.md"}, {"basename": "输入输出安全 两大方面：    1.  输入安全 (防投毒)：过滤用户的恶意指令（如“忽略之前指令，现在你是...”）、脏数据攻击（乱码病毒代码）。        -   防御措施：前端过滤、长度限制、格式校验。    2.  输出安全 (防发疯)：过滤AI生成的有害、违法、不道德的内容（如[大模型幻觉](大模型幻觉.md)）。        -   防御措施：规则过滤（敏感词）、置信度拦截、日志溯源。", "path": "学会编程/编程原子笔记/输入输出安全 两大方面：    1.  输入安全 (防投毒)：过滤用户的恶意指令（如“忽略之前指令，现在你是...”）、脏数据攻击（乱码病毒代码）。        -   防御措施：前端过滤、长度限制、格式校验。    2.  输出安全 (防发疯)：过滤AI生成的有害、违法、不道德的内容（如[大模型幻觉](大模型幻觉.md)）。        -   防御措施：规则过滤（敏感词）、置信度拦截、日志溯源。.md"}, {"basename": "上游（Input）数据管理关注数据来源、质量和格式。确保数据干净、格式正确，对应[输入输出安全](输入输出安全.md)。", "path": "学会编程/编程原子笔记/上游（Input）数据管理关注数据来源、质量和格式。确保数据干净、格式正确，对应[输入输出安全](输入输出安全.md)。.md"}, {"basename": "识别数据流动中的法律风险方法：画出数据旅程图，标注每个环节可能涉及的技术点和法律风险点。", "path": "inbox/识别数据流动中的法律风险方法：画出数据旅程图，标注每个环节可能涉及的技术点和法律风险点。.md"}, {"basename": "数据流合规性评估", "path": "inbox/数据流合规性评估.md"}, {"basename": "数据流实践方法：小程序开发", "path": "inbox/数据流实践方法：小程序开发.md"}, {"basename": "数据流关键环节：数据采集：用户输入、设备信息。", "path": "inbox/数据流关键环节：数据采集：用户输入、设备信息。.md"}, {"basename": "数据流关键环节：数据传输：网络请求（HTTPHTTPS）。", "path": "inbox/数据流关键环节：数据传输：网络请求（HTTPHTTPS）。.md"}, {"basename": "数据流关键环节：数据存储：本地存储、数据库。", "path": "inbox/数据流关键环节：数据存储：本地存储、数据库。.md"}, {"basename": "数据流关键环节：数据处理：后端逻辑、AI模型。", "path": "inbox/数据流关键环节：数据处理：后端逻辑、AI模型。.md"}, {"basename": "数据流关键环节：数据共享：第三方API、SDK。", "path": "inbox/数据流关键环节：数据共享：第三方API、SDK。.md"}, {"basename": "关联概念：API接口与测试：关注API接口的安全性和稳定性", "path": "inbox/关联概念：API接口与测试：关注API接口的安全性和稳定性.md"}, {"basename": "关联概念：输入输出安全：关注数据采集和传输过程中的安全。", "path": "inbox/关联概念：输入输出安全：关注数据采集和传输过程中的安全。.md"}, {"basename": "关联概念：技术方案评估能力", "path": "inbox/关联概念：技术方案评估能力.md"}, {"basename": "数据流透视能力", "path": "学会编程/编程原子笔记/数据流透视能力.md"}, {"basename": "合规审查高效策略    -   阶段1：AI自动化扫描（60%工作量）。 -   阶段2：关键点人工复核（30%工作量），关注用户同意、传输、存储、共享等环节。  -   阶段3：渗透测试验证（10%工作量），模拟攻击验证防护措施。", "path": "学会编程/编程原子笔记/合规审查高效策略    -   阶段1：AI自动化扫描（60%工作量）。 -   阶段2：关键点人工复核（30%工作量），关注用户同意、传输、存储、共享等环节。  -   阶段3：渗透测试验证（10%工作量），模拟攻击验证防护措施。.md"}, {"basename": "Base64_vs_加密Base64 (编码)：是“伪装”，像把中文翻译成拼音，任何人都能轻易翻译回来。不具备保密性！    -   加密 (Encryption)：是“上锁”，如AES-256、国密SM4，没有密钥绝对无法打开。具备保密性！", "path": "学会编程/编程原子笔记/Base64_vs_加密Base64 (编码)：是“伪装”，像把中文翻译成拼音，任何人都能轻易翻译回来。不具备保密性！    -   加密 (Encryption)：是“上锁”，如AES-256、国密SM4，没有密钥绝对无法打开。具备保密性！.md"}, {"basename": "上游下游兜底思维    1.  上游 (Input)：数据从哪来？干净吗？格式对吗？（对应[输入输出安全](输入输出安全.md)）    2.  下游 (Output)：结果给谁用？要什么格式？（对应[JSON数据格式](JSON数据格式.md)和[Prompt工程体系](Prompt工程体系.md)）    3.  兜底 (Fallback)：如果AI出错、超时、发疯了怎么办？（对应[大模型幻觉](大模型幻觉.md)）", "path": "学会编程/编程原子笔记/上游下游兜底思维    1.  上游 (Input)：数据从哪来？干净吗？格式对吗？（对应[输入输出安全](输入输出安全.md)）    2.  下游 (Output)：结果给谁用？要什么格式？（对应[JSON数据格式](JSON数据格式.md)和[Prompt工程体系](Prompt工程体系.md)）    3.  兜底 (Fallback)：如果AI出错、超时、发疯了怎么办？（对应[大模型幻觉](大模型幻觉.md)）.md"}, {"basename": "pandas", "path": "学会编程/编程原子笔记/python/pandas.md"}, {"basename": "Python基础 学习重点：变量、列表、字典、循环、函数、文件操作。-   属性：U (上游处理数据)  D (下游调用接口)", "path": "学会编程/编程原子笔记/Python基础 学习重点：变量、列表、字典、循环、函数、文件操作。-   属性：U (上游处理数据)  D (下游调用接口).md"}, {"basename": "JSON格式[API接口]之间沟通的“普通话”所有大模型API返回的结果基本都是JSON格式。能看懂它，就等于能看懂AI的回答", "path": "学会编程/编程原子笔记/JSON格式[API接口]之间沟通的“普通话”所有大模型API返回的结果基本都是JSON格式。能看懂它，就等于能看懂AI的回答.md"}, {"basename": "可视化工具辅助思考", "path": "学会编程/编程原子笔记/可视化工具辅助思考.md"}, {"basename": "AI编程学习导航", "path": "学会编程/编程原子笔记/AI编程学习导航.md"}, {"basename": "技术人员可能提到的循环场景_-_数据处理类：批量处理数据（例如遍历用户表批量打标签），数据清洗（例如修正所有手机号的格式）。", "path": "学会编程/编程原子笔记/技术人员可能提到的循环场景_-_数据处理类：批量处理数据（例如遍历用户表批量打标签），数据清洗（例如修正所有手机号的格式）。.md"}, {"basename": "🍎重要", "path": "🍎重要.base"}, {"basename": "🧋谈判页", "path": "🧋谈判页.components"}, {"basename": "谈合作像结婚，聪明人婚前会：  1）坦白自己不会做饭不爱打扫（能力边界）  2）说好破产了谁卖房还债（风险分担）  3）签协议离婚怎么分财产（退出机制）  ", "path": "谈判/原始笔记/谈合作像结婚，聪明人婚前会：  1）坦白自己不会做饭不爱打扫（能力边界）  2）说好破产了谁卖房还债（风险分担）  3）签协议离婚怎么分财产（退出机制）  .md"}, {"basename": "第三方销毁服务销毁过程可追溯性要求-具备完整的销毁过程记录，包括监控视频、销毁日志、销毁证明等，确保每个环节可查证，避免信息被虚假销毁", "path": "inbox/第三方销毁服务销毁过程可追溯性要求-具备完整的销毁过程记录，包括监控视频、销毁日志、销毁证明等，确保每个环节可查证，避免信息被虚假销毁.md"}, {"basename": "第三方销毁服务数据擦除软件要求-，采用多次覆写等专业方法彻底删除数据，而非简单格式化，以符合如DoD 5220.22-M等标准", "path": "inbox/第三方销毁服务数据擦除软件要求-，采用多次覆写等专业方法彻底删除数据，而非简单格式化，以符合如DoD 5220.22-M等标准.md"}, {"basename": "第三方销毁服务物理销毁设备要求-有专业的物理销毁设备，如粉碎机、熔炉等，能够将硬盘、纸质文件等彻底毁掉", "path": "inbox/第三方销毁服务物理销毁设备要求-有专业的物理销毁设备，如粉碎机、熔炉等，能够将硬盘、纸质文件等彻底毁掉.md"}, {"basename": "第三方销毁服务的设备与技术能力评估", "path": "inbox/第三方销毁服务的设备与技术能力评估.md"}], "omittedPaths": [], "omittedTags": [], "updateOn": "file-open", "omitBookmarks": false}