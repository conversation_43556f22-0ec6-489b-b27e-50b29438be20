# 工作室财务分析测试页面

## 📊 完整财务仪表盘
![[工作室财务分析.base]]

---

## 📋 表1：项目利润分析表
这是你原始模板中的第一个核心表，显示每个项目的盈利情况：

![[工作室财务分析.base#表1：项目利润分析表]]

### 关键指标说明
- **项目毛利率** = (合同金额 - 人力成本 - 外包成本) / 合同金额
- **应收款** = 合同金额 - 已收款
- **逾期状态** = 自动判断是否逾期并标红

---

## 👥 表2：人力效率监控表
这是你原始模板中的第二个核心表，监控顾问工作效率：

![[工作室财务分析.base#表2：人力效率监控表]]

### 关键指标说明
- **人效比** = 创收金额 / 人力成本（>7健康，<5需要优化）
- **闲置率** = 闲置工时 / (项目工时 + 闲置工时)
- **效率状态** = 自动评估顾问效率等级

---

## 💰 表3：现金流预警表
这是你原始模板中的第三个核心表，预测未来现金流：

![[工作室财务分析.base#表3：现金流预警表]]

### 关键指标说明
- **现金盈余** = 预计收入 - 预计支出
- **现金状态** = 基于现金结余自动评估安全等级
- **行动优先级** = 🔴紧急 / 🟡关注 / 🟢正常

---

## 🚨 预警卡片视图

### 逾期项目预警
![[工作室财务分析.base#逾期项目预警]]

### 低效顾问提醒
![[工作室财务分析.base#低效顾问提醒]]

### 现金流紧急预警
![[工作室财务分析.base#现金流紧急预警]]

---

## 📈 使用说明

### 如何添加新数据
1. 在 `工作室财务数据/` 文件夹中创建新的 `.md` 文件
2. 按照示例格式填写 frontmatter 属性
3. 数据会自动显示在对应的表格中

### 示例数据格式
```yaml
---
table_type: "项目利润分析"  # 必填：项目利润分析/人力效率监控/现金流预警
project_name: "新项目名称"
client_type: "外企"         # 外企/国企/民企
contract_amount: 50         # 合同金额(万元)
received_amount: 15         # 已收款(万元)
human_cost: 20             # 人力成本(万元)
outsource_cost: 5          # 外包成本(万元)
payment_cycle: "60天"       # 付款账期
is_overdue: true           # 是否逾期
---
```

### 自动计算功能
- ✅ 项目毛利率自动计算
- ✅ 应收款自动计算
- ✅ 人效比自动计算
- ✅ 现金流状态自动评估
- ✅ 预警信息自动标红

### 核心优势
1. **比传统三张表更聚焦** - 专注解决你的行业痛点（人效、回款）
2. **按项目、按顾问拆解** - 不是按科目汇总，而是精细化管理
3. **老板每日快速决策** - 不是给会计做账用，而是给你决策用

---

## 🎯 你的行业财务口诀
> **"人效决定毛利，回款决定生死"**

- 如果发现：
  - **毛利率下降** → 检查是不是人力成本失控（比如项目超时）
  - **现金流紧张** → 立刻收紧账期，停止垫资做项目
  - **销售费用太高** → 重点维护老客户，减少无效社交
