#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
少链接笔记增强器
专门处理只有1-2个链接的孤立笔记，为它们增加更多相关链接
"""

import os
import re
from pathlib import Path
from datetime import datetime

def extract_links(content):
    """提取现有链接"""
    return re.findall(r'\[\[([^\]]+)\]\]', content)

def extract_simple_keywords(text):
    """提取简单关键词"""
    keywords = [
        '股权', '融资', '投资', '对赌', '控制权', '董事会', '股东',
        '法律', '合规', '风险', '合同', '协议', '知识产权',
        '财务', '现金流', '利润', '成本', '税务', '资金',
        '管理', '战略', '运营', '团队', '领导', '组织',
        '市场', '营销', '客户', '产品', '品牌', '销售'
    ]
    
    found = []
    for keyword in keywords:
        if keyword in text:
            found.append(keyword)
    return found

def find_isolated_notes(base_dir):
    """找到只有1-2个链接的笔记"""
    isolated_notes = []
    all_notes = {}
    exclude_dirs = ['默写本', '.obsidian', '.trash', '原子笔记', '知识卡片', 'template']
    
    # 收集所有笔记
    for file_path in base_dir.rglob("*.md"):
        if any(ex in str(file_path).lower() for ex in exclude_dirs):
            continue
            
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
        except:
            continue
        
        title = file_path.stem
        links = extract_links(content)
        keywords = extract_simple_keywords(f"{title} {content}")
        
        note_info = {
            'path': file_path,
            'title': title,
            'content': content,
            'links': links,
            'link_count': len(links),
            'keywords': keywords
        }
        
        all_notes[title] = note_info
        
        # 只有1-2个链接的笔记
        if 1 <= len(links) <= 2:
            isolated_notes.append(note_info)
    
    return isolated_notes, all_notes

def find_similar_notes(target_note, all_notes, max_results=3):
    """找到相似的笔记"""
    similar = []
    target_keywords = set(target_note['keywords'])
    target_words = set([w for w in target_note['title'].split() if len(w) > 2])
    
    for other_title, other_note in all_notes.items():
        if other_title == target_note['title']:
            continue
            
        # 如果已经有链接，跳过
        if other_title in target_note['links']:
            continue
        
        other_keywords = set(other_note['keywords'])
        other_words = set([w for w in other_note['title'].split() if len(w) > 2])
        
        # 计算相似度
        score = 0
        
        # 关键词匹配
        keyword_match = len(target_keywords & other_keywords)
        score += keyword_match * 5
        
        # 标题词匹配
        title_match = len(target_words & other_words)
        score += title_match * 8
        
        # 标题包含关系
        for word in target_words:
            if word in other_note['title']:
                score += 3
        
        # 关键词在标题中出现
        for keyword in target_keywords:
            if keyword in other_note['title']:
                score += 4
        
        if score >= 3:  # 降低阈值
            similar.append((other_note, score))
    
    # 排序并返回
    similar.sort(key=lambda x: x[1], reverse=True)
    return similar[:max_results]

def add_more_links(note, similar_notes):
    """为笔记添加更多链接"""
    if not similar_notes:
        return False
        
    content = note['content']
    
    # 检查是否已有相关笔记部分
    if '## 相关笔记' in content:
        # 在现有部分添加
        lines = content.split('\n')
        new_lines = []
        found_section = False
        
        for line in lines:
            new_lines.append(line)
            if line.startswith('## 相关笔记') and not found_section:
                found_section = True
                # 添加新链接
                for similar_note, score in similar_notes:
                    new_lines.append(f"- [[{similar_note['title']}]]")
        
        content = '\n'.join(new_lines)
    else:
        # 添加新的相关笔记部分
        content += f"\n\n---\n\n## 相关笔记\n"
        content += f"*增强链接于 {datetime.now().strftime('%Y-%m-%d')}*\n\n"
        
        for similar_note, score in similar_notes:
            content += f"- [[{similar_note['title']}]]\n"
    
    # 保存文件
    try:
        with open(note['path'], 'w', encoding='utf-8') as f:
            f.write(content)
        return True
    except:
        return False

def main():
    """主函数"""
    base_dir = Path(r"c:\Users\<USER>\OneDrive\obsidian笔记系统\战略与战术")
    
    print("少链接笔记增强器")
    print("=" * 30)
    
    # 找到孤立笔记
    print("查找孤立笔记（1-2个链接）...")
    isolated_notes, all_notes = find_isolated_notes(base_dir)
    print(f"找到 {len(isolated_notes)} 个孤立笔记")
    print(f"总笔记数: {len(all_notes)} 个")
    
    if len(isolated_notes) == 0:
        print("没有找到孤立笔记！")
        return
    
    # 为孤立笔记添加更多链接
    print("\n开始增强链接...")
    success_count = 0
    
    for i, note in enumerate(isolated_notes):
        print(f"处理 {i+1}/{len(isolated_notes)}: {note['title'][:50]}...")
        print(f"  当前链接数: {note['link_count']}")
        
        # 找到相似笔记
        similar_notes = find_similar_notes(note, all_notes)
        
        if similar_notes:
            if add_more_links(note, similar_notes):
                success_count += 1
                print(f"  ✓ 添加了 {len(similar_notes)} 个新链接")
            else:
                print(f"  ✗ 添加失败")
        else:
            print(f"  - 未找到相似笔记")
    
    print(f"\n✅ 完成！")
    print(f"📊 处理了 {len(isolated_notes)} 个孤立笔记")
    print(f"🔗 成功为 {success_count} 个笔记增强了链接")
    print(f"📈 成功率: {success_count/len(isolated_notes)*100:.1f}%")

if __name__ == "__main__":
    main()
