# 用户协议里写的‘数据共享’到底是什么意
#### **场景1：基础概念厘清**

**问**：  
"天天老师，用户协议里写的‘数据共享’到底是什么意思？我们法务和技术的理解完全不一样！"

**答**：  
合同里的「数据共享」必须明确3个层次：

1. **共享给谁**（具体公司名称，不能写"关联方"模糊处理）
    
2. **共享什么**（比如只共享手机号，还是连带消费记录）
    
3. **用来干嘛**（比如"仅用于物流配送"，不能写"提升服务质量"这种空话）
    

**举个栗子**：  
❌ 错误写法："可能与合作伙伴共享数据"  
✅ 正确写法："向顺丰共享收件人手机号（仅用于快递通知）"

---

#### **场景2：责任划分**

**问**：  
"我们委托外包公司做数据分析，合同写‘数据安全由乙方负责’，这样够了吗？"

**答**：  
这么写会坑死你！必须拆解成：

1. **乙方义务**：
    
    - 数据加密存储（明确算法如AES-256）
        
    - 操作留痕（谁什么时候查了数据）
        
    - 违约赔偿（比如泄露1条数据赔5万元）
        
2. **甲方权利**：
    
    - 随时突击检查乙方服务器
        
    - 发生泄露时，乙方需2小时内通知
        

**血泪教训**：  
某公司因模糊条款，外包商泄露数据后被罚500万，法院判甲方承担70%责任！

---

#### **场景3：应对监管变化**

**问**：  
"《个人信息保护法》出新规定了，我们要不要全部合同重签？"

**答**：  
抓大放小！优先改3类合同：

1. **有跨境数据**的（新增"数据出境安全评估"条款）
    
2. **处理敏感信息**的（如人脸数据，需单独同意）
    
3. **合作方权限大**的（如能导出完整数据库的供应商）
    

**模板条款**：  
"若法律法规变更导致本合同部分条款失效，双方应在30日内协商修订，否则甲方有权终止合作。"

---

#### **场景4：技术落地**

**问**：  
"IT说完全合规成本太高，能不能折中？"

**答**：  
给你个「低成本合规三板斧」：

1. **敏感数据分级**：
    
    - 一级（必须加密）：身份证、银行卡
        
    - 二级（脱敏即可）：手机号（显示138****1234）
        
2. **权限动态管控**：
    
    - 客服上班时才能查客户信息
        
    - 离职自动销号
        
3. **日志精简留存**：
    
    - 访问记录存6个月（满足法律最低要求）
        
    - 用廉价对象存储（比如阿里云OSS）
        

---

#### **场景5：争议预判**

**问**：  
"用户投诉我们偷偷收集地理位置，可协议里明明写了啊！"

**答**：  
法院只看你有没有做到**显著告知**！检查4点：

1. **单独弹窗**：不要埋在20页协议里
    
2. **明确用途**：写"用于推荐附近门店"，不能写"提升用户体验"
    
3. **一键拒绝**：不能设置"不同意就退出APP"
    
4. **随时关闭**：在设置页提供关闭开关
    

**真实判例**：  
某APP因"不同意收集位置就不能使用"被罚120万！

---

### **你的超能力**

把枯燥的法条变成：

- 给法务的** checklist**
    
- 给技术的**参数表**（如加密算法选型）
    
- 给老板的**风险账本**（合规成本 vs 罚款概率）
    

记住口诀：  
**「法条拆三点，风险标价格，技术给参数」**  
（完全复刻原问答中"盯死不含税价/税率/税额"的杀伤力！）
