/* AI 自动属性插件样式 */

.ai-tags-settings .setting-item {
    padding: 10px;
    border-bottom: 1px solid var(--background-modifier-border);
}

.ai-tags-settings .setting-item-description {
    color: var(--text-muted);
}

.ai-tags-settings .setting-item-name {
    font-weight: bold;
}

.ai-tags-loading {
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 10px 0;
}

.ai-tags-loading-spinner {
    border: 3px solid var(--background-modifier-border);
    border-top: 3px solid var(--interactive-accent);
    border-radius: 50%;
    width: 20px;
    height: 20px;
    animation: ai-tags-spin 1s linear infinite;
    margin-right: 10px;
}

@keyframes ai-tags-spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* 确认对话框样式 */
.ai-confirm-modal {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    max-width: 500px;
    width: 100%;
    box-shadow: 0 4px 14px rgba(0, 0, 0, 0.15);
}

.ai-confirm-modal .confirm-message {
    margin: 1em 0;
    line-height: 1.5;
}

.ai-confirm-modal .setting-item {
    justify-content: center;
    padding: 1em 0;
}

.ai-confirm-modal .setting-item-control {
    display: flex;
    gap: 10px;
}

/* 使用说明样式覆盖 */
.ai-tags-settings h3:contains("使用说明") + .setting-item-description {
    display: none !important;
}

.ai-tags-settings h3:contains("使用说明")::after {
    content: "";
    display: block;
    margin-top: 10px;
}

.ai-tags-settings h3:contains("使用说明") ~ .setting-item-description::before {
    content: "本插件使用 AI 为您的笔记自动生成标签和其他属性。您可以通过以下方式使用：";
    display: block;
    margin-bottom: 10px;
}

.ai-usage-guide {
    display: block !important;
    margin-top: 10px;
    padding: 10px;
    border: 1px solid var(--background-modifier-border);
    border-radius: 4px;
}

.ai-usage-guide ol {
    padding-left: 20px;
    margin: 10px 0;
}

.ai-usage-guide li {
    margin-bottom: 8px;
}

.ai-usage-guide ul {
    padding-left: 20px;
    margin: 5px 0;
}

.ai-usage-guide p:last-child {
    margin-top: 10px;
    font-style: italic;
    color: var(--text-muted);
}
