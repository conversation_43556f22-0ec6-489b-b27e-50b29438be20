#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单孤立笔记修复器
找到链接少于2个的笔记，直接添加3个常用链接
"""

import os
import re
from pathlib import Path

def extract_links(content):
    """提取现有链接"""
    return re.findall(r'\[\[([^\]]+)\]\]', content)

def main():
    """主函数"""
    base_dir = Path(r"c:\Users\<USER>\OneDrive\obsidian笔记系统\战略与战术")
    
    print("简单孤立笔记修复器")
    print("=" * 25)
    
    # 常用链接
    common_links = [
        "✅3个通用生存法则-从王石案例",
        "创业者必知的「吸血条款」清单", 
        "股权分配的5大致命陷阱"
    ]
    
    # 统计和处理
    processed = 0
    success = 0
    
    for file_path in base_dir.rglob("*.md"):
        # 排除特定目录
        if any(ex in str(file_path).lower() for ex in ['默写本', '.obsidian', '.trash', '原子笔记', '知识卡片']):
            continue
        
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
        except:
            continue
        
        # 检查链接数
        links = extract_links(content)
        if len(links) >= 2:  # 已经有2个或更多链接，跳过
            continue
        
        processed += 1
        title = file_path.stem
        
        print(f"处理: {title} (当前{len(links)}个链接)")
        
        # 如果已经有相关笔记部分，跳过
        if '## 相关笔记' in content:
            print(f"  - 已有相关笔记部分，跳过")
            continue
        
        # 添加链接
        content += f"\n\n---\n\n## 相关笔记\n\n"
        for link in common_links:
            if link not in links:  # 避免重复
                content += f"- [[{link}]]\n"
        
        # 保存文件
        try:
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(content)
            success += 1
            print(f"  ✓ 已添加3个链接")
        except:
            print(f"  ✗ 保存失败")
    
    print(f"\n完成！")
    print(f"发现孤立笔记: {processed}个")
    print(f"成功处理: {success}个")

if __name__ == "__main__":
    main()
