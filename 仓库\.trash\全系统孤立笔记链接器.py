#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
全系统孤立笔记链接器
处理整个obsidian系统中0-3个链接的孤立笔记
"""

import os
import re
from pathlib import Path
from datetime import datetime

def extract_links(content):
    """提取现有链接"""
    return re.findall(r'\[\[([^\]]+)\]\]', content)

def extract_keywords(text):
    """提取关键词"""
    keywords = [
        # 商业相关
        '股权', '融资', '投资', '对赌', '控制权', '董事会', '股东',
        '法律', '合规', '风险', '合同', '协议', '知识产权',
        '财务', '现金流', '利润', '成本', '税务', '资金',
        '管理', '战略', '运营', '团队', '领导', '组织',
        '市场', '营销', '客户', '产品', '品牌', '销售',
        '技术', '创新', '研发', '开发', '设计', '专利',
        # 学习相关
        '学习', '笔记', '总结', '思考', '方法', '技巧',
        '阅读', '书籍', '文章', '资料', '知识', '经验',
        # 项目相关
        '项目', '任务', '计划', '目标', '进度', '完成',
        '工作', '职业', '发展', '成长', '提升', '改进'
    ]
    
    found = []
    text_lower = text.lower()
    for keyword in keywords:
        if keyword in text_lower:
            found.append(keyword)
    return found

def collect_all_notes(base_dir):
    """收集整个系统的所有笔记"""
    all_notes = {}
    isolated_notes = []
    exclude_dirs = ['.obsidian', '.trash']  # 只排除系统文件夹
    
    print("收集全系统笔记...")
    
    for file_path in base_dir.rglob("*.md"):
        # 只排除系统文件夹
        if any(ex in str(file_path).lower() for ex in exclude_dirs):
            continue
            
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
        except:
            continue
        
        title = file_path.stem
        links = extract_links(content)
        keywords = extract_keywords(f"{title} {content}")
        
        note_info = {
            'path': file_path,
            'title': title,
            'content': content,
            'links': links,
            'link_count': len(links),
            'keywords': keywords,
            'word_count': len(content),
            'folder': str(file_path.parent.relative_to(base_dir))
        }
        
        all_notes[title] = note_info
        
        # 0-3个链接的笔记视为孤立
        if len(links) <= 3:
            isolated_notes.append(note_info)
    
    return all_notes, isolated_notes

def calculate_similarity(note1, note2):
    """计算两个笔记的相似度"""
    score = 0
    
    # 关键词匹配
    keywords1 = set(note1['keywords'])
    keywords2 = set(note2['keywords'])
    keyword_overlap = len(keywords1 & keywords2)
    score += keyword_overlap * 8
    
    # 标题词匹配
    title_words1 = set([w.lower() for w in note1['title'].split() if len(w) > 2])
    title_words2 = set([w.lower() for w in note2['title'].split() if len(w) > 2])
    title_overlap = len(title_words1 & title_words2)
    score += title_overlap * 10
    
    # 文件夹相同加分
    if note1['folder'] == note2['folder']:
        score += 5
    
    # 标题包含关系
    for word in title_words1:
        if word in note2['title'].lower():
            score += 6
    
    # 关键词在标题中出现
    for keyword in keywords1:
        if keyword in note2['title'].lower():
            score += 7
    
    return score

def find_related_notes(target_note, all_notes, max_results=4):
    """找到相关笔记"""
    related = []
    
    for other_title, other_note in all_notes.items():
        if other_title == target_note['title']:
            continue
            
        # 如果已经有链接，跳过
        if other_title in target_note['links']:
            continue
        
        # 计算相似度
        score = calculate_similarity(target_note, other_note)
        
        if score >= 5:  # 相似度阈值
            related.append((other_note, score))
    
    # 如果找不到足够的相关笔记，降低阈值
    if len(related) < 2:
        for other_title, other_note in all_notes.items():
            if other_title == target_note['title']:
                continue
            if other_title in target_note['links']:
                continue
            
            score = calculate_similarity(target_note, other_note)
            if score >= 2:  # 更低的阈值
                related.append((other_note, score))
    
    # 排序并返回
    related.sort(key=lambda x: x[1], reverse=True)
    return related[:max_results]

def add_links_to_note(note, related_notes):
    """为笔记添加链接"""
    if not related_notes:
        return False
        
    content = note['content']
    
    # 检查是否已有相关笔记部分
    if '## 相关笔记' in content:
        # 更新现有部分
        lines = content.split('\n')
        new_lines = []
        in_related_section = False
        
        for line in lines:
            if line.startswith('## 相关笔记'):
                in_related_section = True
                new_lines.append(line)
                new_lines.append(f"*更新于 {datetime.now().strftime('%Y-%m-%d')}*\n")
                
                # 添加新的相关笔记
                for related_note, score in related_notes:
                    new_lines.append(f"- [[{related_note['title']}]]")
                
            elif in_related_section and line.startswith('##'):
                in_related_section = False
                new_lines.append(line)
            elif not in_related_section:
                new_lines.append(line)
        
        content = '\n'.join(new_lines)
    else:
        # 添加新的相关笔记部分
        content += f"\n\n---\n\n## 相关笔记\n"
        content += f"*自动链接于 {datetime.now().strftime('%Y-%m-%d')}*\n\n"
        
        for related_note, score in related_notes:
            content += f"- [[{related_note['title']}]]\n"
    
    # 保存文件
    try:
        with open(note['path'], 'w', encoding='utf-8') as f:
            f.write(content)
        return True
    except:
        return False

def main():
    """主函数"""
    # 整个obsidian笔记系统的根目录
    base_dir = Path(r"c:\Users\<USER>\OneDrive\obsidian笔记系统")
    
    print("全系统孤立笔记链接器")
    print("=" * 40)
    
    # 收集所有笔记
    all_notes, isolated_notes = collect_all_notes(base_dir)
    
    print(f"总笔记数: {len(all_notes)}")
    print(f"孤立笔记数 (0-3个链接): {len(isolated_notes)}")
    
    if len(isolated_notes) == 0:
        print("🎉 没有孤立笔记！")
        return
    
    # 按链接数分组显示
    link_groups = {0: [], 1: [], 2: [], 3: []}
    for note in isolated_notes:
        if note['link_count'] in link_groups:
            link_groups[note['link_count']].append(note)
    
    for link_count, notes in link_groups.items():
        if notes:
            print(f"  {link_count}个链接: {len(notes)}个笔记")
    
    # 处理孤立笔记
    print(f"\n开始处理孤立笔记...")
    success_count = 0
    
    for i, note in enumerate(isolated_notes):
        if i % 50 == 0:  # 每50个显示一次进度
            print(f"进度: {i+1}/{len(isolated_notes)}")
        
        # 找到相关笔记
        related_notes = find_related_notes(note, all_notes)
        
        if related_notes:
            if add_links_to_note(note, related_notes):
                success_count += 1
    
    print(f"\n🎯 处理完成！")
    print(f"📊 处理了 {len(isolated_notes)} 个孤立笔记")
    print(f"🔗 成功为 {success_count} 个笔记添加了链接")
    print(f"📈 成功率: {success_count/len(isolated_notes)*100:.1f}%")

if __name__ == "__main__":
    main()
