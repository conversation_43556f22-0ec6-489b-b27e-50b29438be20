import { App, Plugin, PluginSettingTab, Setting, WorkspaceLeaf, ItemView, Notice, Modal, TextComponent, TextAreaComponent } from 'obsidian';

// 常量定义
const PROMPT_LIBRARY_VIEW_TYPE = 'prompt-library-view';

// 数据类型定义
interface PromptItem {
    id: string;
    title: string;
    description: string;
    content: string;
    category: string;
    tags: string[];
    createdAt: Date;
    updatedAt: Date;
}

interface DeletedPrompt {
    prompt: PromptItem;
    deletedAt: Date;
}

interface PromptCategory {
    id: string;
    name: string;
    icon?: string;
    color?: string;
}

interface PromptLibrarySettings {
    prompts: PromptItem[];
    categories: PromptCategory[];
    defaultCategory: string;
    deletedPrompts: DeletedPrompt[];
    selectedTags: string[];
    aiApiUrl: string;
    aiApiKey: string;
    aiModel: string;
}

// 默认设置
const DEFAULT_SETTINGS: PromptLibrarySettings = {
    prompts: [
        {
            id: 'demo1',
            title: '中英翻译',
            description: '你是一个中英文翻译专家，将用户输入的中文翻译成英文，或将英文翻译成中文',
            content: '你是一个中英文翻译专家，将用户输入的中文翻译成英文，或将英文翻译成中文。对于非中文内容，它将提供中文翻译结果。用户只需要输入文本内容，无需额外说明。',
            category: 'translation',
            tags: ['翻译', '中英文'],
            createdAt: new Date(),
            updatedAt: new Date()
        },
        {
            id: 'demo2',
            title: '代码优化',
            description: '帮助优化和改进代码质量，提供最佳实践建议',
            content: '你是一个资深的软件工程师，专门帮助优化代码。请分析用户提供的代码，从性能、可读性、安全性等方面提出改进建议，并提供优化后的代码示例。',
            category: 'programming',
            tags: ['代码改写', '优化'],
            createdAt: new Date(),
            updatedAt: new Date()
        },
        {
            id: 'demo3',
            title: '文章写作助手',
            description: '专业的文章写作指导，帮助构思和完善文章结构',
            content: '你是一个专业的写作导师，擅长各种文体的写作指导。请根据用户提供的主题和要求，帮助构思文章大纲，提供写作建议，并协助完善文章的逻辑结构和表达方式。',
            category: 'writing',
            tags: ['写作', '文章', '结构'],
            createdAt: new Date(),
            updatedAt: new Date()
        },
        {
            id: 'demo4',
            title: '数据分析专家',
            description: '专业的数据分析和解读，提供深入洞察',
            content: '你是一个资深的数据分析师，擅长从复杂数据中提取有价值的信息。请分析用户提供的数据，识别关键趋势和模式，并提供清晰的解读和建议。',
            category: 'analysis',
            tags: ['数据分析', '趋势', '洞察'],
            createdAt: new Date(),
            updatedAt: new Date()
        },
        {
            id: 'demo5',
            title: '创意头脑风暴',
            description: '激发创意思维，提供多角度的创新想法',
            content: '你是一个富有创意的思维导师，擅长从不同角度思考问题。请针对用户的需求，提供多个创新性的解决方案和想法，鼓励跳出常规思维模式。',
            category: 'creative',
            tags: ['创意', '头脑风暴', '创新'],
            createdAt: new Date(),
            updatedAt: new Date()
        },
        {
            id: 'demo6',
            title: 'Python编程助手',
            description: '专业的Python编程指导和问题解决',
            content: '你是一个Python编程专家，熟悉各种Python库和最佳实践。请帮助用户解决Python编程问题，提供清晰的代码示例和详细的解释。',
            category: 'programming',
            tags: ['Python', '编程', '调试'],
            createdAt: new Date(),
            updatedAt: new Date()
        },
        {
            id: 'demo7',
            title: '英语学习导师',
            description: '专业的英语学习指导，提高口语和写作能力',
            content: '你是一位经验丰富的英语教师，擅长帮助学生提高英语水平。请根据用户的英语水平和学习目标，提供个性化的学习建议和练习方法。',
            category: 'translation',
            tags: ['英语', '学习', '教育'],
            createdAt: new Date(),
            updatedAt: new Date()
        },
        {
            id: 'demo8',
            title: '产品经理助手',
            description: '产品规划和需求分析专家',
            content: '你是一个资深的产品经理，擅长产品规划、需求分析和用户体验设计。请帮助分析产品需求，制定产品策略，并提供专业的产品建议。',
            category: 'analysis',
            tags: ['产品', '需求', '策略'],
            createdAt: new Date(),
            updatedAt: new Date()
        },
        {
            id: 'demo9',
            title: '营销文案专家',
            description: '创作吸引人的营销文案和广告语',
            content: '你是一个创意营销专家，擅长撰写各种营销文案。请根据产品特点和目标受众，创作有吸引力的广告语、产品描述和营销内容。',
            category: 'writing',
            tags: ['营销', '文案', '广告'],
            createdAt: new Date(),
            updatedAt: new Date()
        },
        {
            id: 'demo10',
            title: 'UI/UX设计顾问',
            description: '用户界面和用户体验设计指导',
            content: '你是一个专业的UI/UX设计师，具有丰富的设计经验。请分析用户界面设计，提供用户体验优化建议，并指导设计最佳实践。',
            category: 'creative',
            tags: ['设计', 'UI', 'UX'],
            createdAt: new Date(),
            updatedAt: new Date()
        },
        {
            id: 'demo11',
            title: '财务分析师',
            description: '专业的财务数据分析和投资建议',
            content: '你是一个专业的财务分析师，擅长分析财务数据和市场趋势。请帮助分析财务报表，评估投资机会，并提供理财建议。',
            category: 'analysis',
            tags: ['财务', '投资', '分析'],
            createdAt: new Date(),
            updatedAt: new Date()
        },
        {
            id: 'demo12',
            title: 'JavaScript开发专家',
            description: '前端JavaScript开发和框架应用',
            content: '你是一个JavaScript开发专家，熟悉现代前端框架和工具。请帮助解决JavaScript编程问题，提供最佳实践和性能优化建议。',
            category: 'programming',
            tags: ['JavaScript', '前端', '框架'],
            createdAt: new Date(),
            updatedAt: new Date()
        },
        {
            id: 'demo13',
            title: '学术论文助手',
            description: '专业的学术写作指导和论文结构优化',
            content: '你是一个资深的学术写作专家，擅长各类学术论文的写作指导。请帮助用户完善论文结构、改进学术表达、规范引用格式，并提供专业的学术建议。',
            category: 'writing',
            tags: ['学术', '论文', '写作'],
            createdAt: new Date(),
            updatedAt: new Date()
        },
        {
            id: 'demo14',
            title: '市场调研分析师',
            description: '深入的市场分析和商业洞察',
            content: '你是一个专业的市场调研分析师，擅长分析市场趋势、竞争格局和消费者行为。请提供详细的市场分析报告和商业建议。',
            category: 'analysis',
            tags: ['市场', '调研', '商业'],
            createdAt: new Date(),
            updatedAt: new Date()
        },
        {
            id: 'demo15',
            title: '品牌策划专家',
            description: '创意品牌策划和营销方案设计',
            content: '你是一个富有创意的品牌策划专家，擅长品牌定位、视觉设计和营销策略。请帮助制定完整的品牌策划方案和创意营销活动。',
            category: 'creative',
            tags: ['品牌', '策划', '营销'],
            createdAt: new Date(),
            updatedAt: new Date()
        },
        {
            id: 'demo16',
            title: 'SQL数据库专家',
            description: '数据库设计和SQL查询优化',
            content: '你是一个数据库专家，精通SQL语言和数据库设计。请帮助优化数据库结构、编写高效的SQL查询，并提供数据库性能调优建议。',
            category: 'programming',
            tags: ['SQL', '数据库', '优化'],
            createdAt: new Date(),
            updatedAt: new Date()
        },
        {
            id: 'demo17',
            title: '心理咨询顾问',
            description: '专业的心理分析和情感支持',
            content: '你是一个专业的心理咨询师，擅长情感分析和心理疏导。请以专业、温暖的态度提供心理支持和建议，帮助用户解决心理困扰。',
            category: 'analysis',
            tags: ['心理', '咨询', '情感'],
            createdAt: new Date(),
            updatedAt: new Date()
        },
        {
            id: 'demo18',
            title: '法语翻译专家',
            description: '专业的中法双语翻译服务',
            content: '你是一个专业的法语翻译专家，精通中法双语转换。请提供准确、地道的翻译服务，并解释语言文化背景和表达习惯。',
            category: 'translation',
            tags: ['法语', '翻译', '双语'],
            createdAt: new Date(),
            updatedAt: new Date()
        },
        {
            id: 'demo19',
            title: '摄影构图导师',
            description: '专业的摄影技巧和构图指导',
            content: '你是一个专业的摄影师，擅长各种摄影技巧和构图方法。请分析照片构图、提供拍摄建议，并指导摄影技术的提升。',
            category: 'creative',
            tags: ['摄影', '构图', '艺术'],
            createdAt: new Date(),
            updatedAt: new Date()
        },
        {
            id: 'demo20',
            title: '项目管理专家',
            description: '专业的项目规划和团队管理',
            content: '你是一个资深的项目管理专家，熟悉各种项目管理方法论。请帮助制定项目计划、优化团队协作，并提供项目风险管理建议。',
            category: 'analysis',
            tags: ['项目', '管理', '团队'],
            createdAt: new Date(),
            updatedAt: new Date()
        }
    ],
    categories: [
        { id: 'all', name: '全部' },
        { id: 'translation', name: '翻译' },
        { id: 'writing', name: '写作' },
        { id: 'programming', name: '编程' },
        { id: 'analysis', name: '分析' },
        { id: 'creative', name: '创意' }
    ],
    defaultCategory: 'writing',
    deletedPrompts: [],
    selectedTags: [],
    aiApiUrl: 'https://api.deepseek.com/v1/chat/completions',
    aiApiKey: 'sk-b37b6de26d854175a1db8d6dfe1ea0ec',
    aiModel: 'deepseek-chat'
};

// 主插件类
export default class PromptLibraryPlugin extends Plugin {
    settings: PromptLibrarySettings;

    async onload() {
        await this.loadSettings();

        // 注册视图
        this.registerView(
            PROMPT_LIBRARY_VIEW_TYPE,
            (leaf) => new PromptLibraryView(leaf, this)
        );

        // 添加命令
        this.addCommand({
            id: 'open-prompt-library',
            name: '打开提示词库',
            callback: () => this.activateView()
        });

        // 添加设置页面
        this.addSettingTab(new PromptLibrarySettingTab(this.app, this));

        // 添加侧边栏图标
        this.addRibbonIcon('library', '提示词库', (evt: MouseEvent) => {
            this.activateView();
        });
    }

    onunload() {
        this.app.workspace.detachLeavesOfType(PROMPT_LIBRARY_VIEW_TYPE);
    }

    async activateView() {
        const { workspace } = this.app;
        
        let leaf: WorkspaceLeaf | null = null;
        const leaves = workspace.getLeavesOfType(PROMPT_LIBRARY_VIEW_TYPE);

        if (leaves.length > 0) {
            leaf = leaves[0];
        } else {
            // 在主工作区创建新的标签页，而不是右侧边栏
            leaf = workspace.getLeaf('tab');
            if (leaf) {
                await leaf.setViewState({ type: PROMPT_LIBRARY_VIEW_TYPE, active: true });
            }
        }

        if (leaf) {
            workspace.revealLeaf(leaf);
        }
    }

    async loadSettings() {
        this.settings = Object.assign({}, DEFAULT_SETTINGS, await this.loadData());
    }

    async saveSettings() {
        await this.saveData(this.settings);
    }
}

// 主视图类
class PromptLibraryView extends ItemView {
    plugin: PromptLibraryPlugin;
    currentCategory: string = 'all';
    searchQuery: string = '';
    showTagFilter: boolean = false;
    showUndoButtons: boolean = false;

    constructor(leaf: WorkspaceLeaf, plugin: PromptLibraryPlugin) {
        super(leaf);
        this.plugin = plugin;
    }

    getViewType(): string {
        return PROMPT_LIBRARY_VIEW_TYPE;
    }

    getDisplayText(): string {
        return '提示词库';
    }

    getIcon(): string {
        return 'library';
    }

    async onOpen() {
        this.render();
    }

    async onClose() {
        // 清理工作
    }

    render() {
        const container = this.containerEl.children[1];
        container.empty();
        container.addClass('prompt-library-container');

        // 创建头部
        this.createHeader(container);

        // 创建搜索栏
        this.createSearchBar(container);

        // 创建标签筛选按钮
        this.createTagFilterButton(container);

        // 创建标签筛选
        this.createTagFilter(container);

        // 创建分类标签
        this.createCategoryTabs(container);

        // 创建撤销删除按钮
        this.createUndoDeleteButton(container);

        // 创建提示词网格
        this.createPromptGrid(container);
    }

    private createHeader(container: Element) {
        const header = container.createDiv('prompt-library-header');
        
        const title = header.createEl('h2', { text: '提示词库' });
        
        const buttonContainer = header.createDiv('prompt-header-buttons');
        
        const addButton = buttonContainer.createEl('button', {
            text: '+ 添加提示词',
            cls: 'prompt-add-btn'
        });
        
        addButton.addEventListener('click', () => {
            new PromptEditModal(this.app, this.plugin, null, () => {
                this.render();
            }).open();
        });
        
        const aiGenerateButton = buttonContainer.createEl('button', {
            text: '🤖 AI生成',
            cls: 'prompt-ai-generate-btn'
        });
        
        aiGenerateButton.addEventListener('click', () => {
            this.openAiGenerateModal();
        });
    }

    private createSearchBar(container: Element) {
        const searchContainer = container.createDiv('prompt-search-container');
        
        const searchInput = searchContainer.createEl('input', {
            type: 'text',
            placeholder: '搜索提示词...',
            cls: 'prompt-search-input'
        });
        
        searchInput.value = this.searchQuery;
        
        searchInput.addEventListener('input', (e) => {
            this.searchQuery = (e.target as HTMLInputElement).value;
            this.updatePromptGrid();
        });
    }

    private createTagFilterButton(container: Element) {
        const tagFilterMainContainer = container.createDiv('prompt-tag-filter-main-container');
        
        // 创建切换按钮
        const toggleContainer = tagFilterMainContainer.createDiv('prompt-tag-filter-toggle-container');
        const toggleBtn = toggleContainer.createEl('button', {
            text: '🏷️',
            cls: 'prompt-tag-filter-toggle-btn'
        });
        toggleBtn.setAttribute('title', '显示/隐藏标签筛选');
        toggleBtn.addEventListener('click', () => {
            this.showTagFilter = !this.showTagFilter;
            this.render();
        });
    }

    private createTagFilter(container: Element) {
        if (!this.showTagFilter) return;
        
        const tagFilterContainer = container.createDiv('prompt-tag-filter');
        
        const tagFilterTitle = tagFilterContainer.createEl('h4', { text: '标签筛选' });
        
        // 获取所有可用标签
        const allTags = new Set<string>();
        this.plugin.settings.prompts.forEach(prompt => {
            prompt.tags.forEach(tag => allTags.add(tag));
        });
        
        if (allTags.size === 0) {
            tagFilterContainer.createEl('p', { text: '暂无可用标签' });
            return;
        }
        
        const tagContainer = tagFilterContainer.createDiv('tag-filter-options');
        
        // 清除所有筛选按钮
        const clearAllBtn = tagContainer.createEl('button', {
            text: '清除筛选',
            cls: 'tag-filter-clear'
        });
        clearAllBtn.addEventListener('click', () => {
            this.plugin.settings.selectedTags = [];
            this.plugin.saveSettings();
            this.render();
        });
        
        // 为每个标签创建筛选按钮
        Array.from(allTags).sort().forEach(tag => {
            const tagBtn = tagContainer.createEl('button', {
                text: tag,
                cls: 'tag-filter-btn'
            });
            
            if (this.plugin.settings.selectedTags.includes(tag)) {
                tagBtn.addClass('active');
            }
            
            tagBtn.addEventListener('click', () => {
                const index = this.plugin.settings.selectedTags.indexOf(tag);
                if (index > -1) {
                    this.plugin.settings.selectedTags.splice(index, 1);
                } else {
                    this.plugin.settings.selectedTags.push(tag);
                }
                this.plugin.saveSettings();
                this.render();
            });
        });
    }

    private createCategoryTabs(container: Element) {
        const tabContainer = container.createDiv('prompt-category-tabs');
        
        // 添加"全部"标签
        const allTab = tabContainer.createDiv('prompt-tab');
        allTab.textContent = '全部';
        if (this.currentCategory === 'all') {
            allTab.addClass('active');
        }
        allTab.addEventListener('click', () => {
            this.setActiveCategory('all', allTab);
        });
        
        // 添加其他分类标签
        this.plugin.settings.categories.forEach(category => {
            if (category.id !== 'all') {
                const tab = tabContainer.createDiv('prompt-tab');
                tab.textContent = category.name;
                if (this.currentCategory === category.id) {
                    tab.addClass('active');
                }
                tab.addEventListener('click', () => {
                    this.setActiveCategory(category.id, tab);
                });
            }
        });
        
        // 添加分类管理按钮
        const addCategoryBtn = tabContainer.createDiv('prompt-tab add-category');
        addCategoryBtn.textContent = '+ 添加分类';
        addCategoryBtn.addEventListener('click', () => {
            new CategoryEditModal(this.plugin.app, this.plugin, null, () => {
                this.render();
            }).open();
        });
        
        // 编辑分类按钮
        const editCategoryBtn = tabContainer.createDiv('prompt-tab edit-category');
        editCategoryBtn.textContent = '✏️ 编辑';
        editCategoryBtn.addEventListener('click', () => {
            // 显示分类管理界面
            const currentCategory = this.plugin.settings.categories.find(c => c.id === this.currentCategory);
            if (currentCategory) {
                new CategoryEditModal(this.plugin.app, this.plugin, currentCategory, () => {
                    this.render();
                }).open();
            } else {
                new Notice('请先选择一个分类进行编辑');
            }
        });
    }

    private setActiveCategory(categoryId: string, tabElement: Element) {
        this.currentCategory = categoryId;
        
        // 更新标签样式
        const tabs = tabElement.parentElement?.querySelectorAll('.prompt-tab');
        tabs?.forEach(tab => tab.removeClass('active'));
        tabElement.addClass('active');
        
        this.updatePromptGrid();
    }

    private createUndoDeleteButton(container: Element) {
        const undoMainContainer = container.createDiv('prompt-undo-main-container');
        
        // 创建切换按钮
        const toggleContainer = undoMainContainer.createDiv('prompt-undo-toggle-container');
        const toggleBtn = toggleContainer.createEl('button', {
            text: '🗑️',
            cls: 'prompt-undo-toggle-btn'
        });
        toggleBtn.setAttribute('title', '显示/隐藏回收站功能');
        
        toggleBtn.addEventListener('click', () => {
            this.showUndoButtons = !this.showUndoButtons;
            this.render();
        });
        
        // 只有在显示状态下才创建撤销删除按钮
        if (this.showUndoButtons) {
            const undoContainer = undoMainContainer.createDiv('prompt-undo-container');
            
            const hasDeletedItems = this.plugin.settings.deletedPrompts.length > 0;
            
            const undoBtn = undoContainer.createEl('button', {
                text: hasDeletedItems ? `撤销删除 (${this.plugin.settings.deletedPrompts.length})` : '撤销删除 (0)',
                cls: 'prompt-undo-btn mod-warning'
            });
            
            if (!hasDeletedItems) {
                undoBtn.disabled = true;
                undoBtn.addClass('disabled');
            }
            
            undoBtn.addEventListener('click', () => {
                if (this.plugin.settings.deletedPrompts.length > 0) {
                    const lastDeleted = this.plugin.settings.deletedPrompts.pop();
                    if (lastDeleted) {
                        this.plugin.settings.prompts.push(lastDeleted.prompt);
                        this.plugin.saveSettings();
                        this.render();
                        new Notice('已恢复提示词: ' + lastDeleted.prompt.title);
                    }
                }
            });
            
            // 清空回收站按钮
            const clearBtn = undoContainer.createEl('button', {
                text: '清空回收站',
                cls: 'prompt-clear-deleted-btn'
            });
            
            if (!hasDeletedItems) {
                clearBtn.disabled = true;
                clearBtn.addClass('disabled');
            }
            
            clearBtn.addEventListener('click', () => {
                if (this.plugin.settings.deletedPrompts.length > 0) {
                    this.plugin.settings.deletedPrompts = [];
                    this.plugin.saveSettings();
                    this.render();
                    new Notice('回收站已清空');
                }
            });
        }
    }

    private createPromptGrid(container: Element) {
        const gridContainer = container.createDiv('prompt-grid');
        this.updatePromptGrid(gridContainer);
    }

    private updatePromptGrid(gridContainer?: Element) {
        if (!gridContainer) {
            gridContainer = this.containerEl.querySelector('.prompt-grid') as Element;
        }
        
        if (!gridContainer) return;
        
        gridContainer.empty();
        
        const filteredPrompts = this.getFilteredPrompts();
        
        if (filteredPrompts.length === 0) {
            const emptyState = gridContainer.createDiv('prompt-empty-state');
            emptyState.createEl('p', { text: '没有找到匹配的提示词' });
            return;
        }
        
        filteredPrompts.forEach(prompt => {
            if (gridContainer) {
                this.createPromptCard(gridContainer, prompt);
            }
        });
    }

    private getFilteredPrompts(): PromptItem[] {
        let prompts = this.plugin.settings.prompts;
        
        // 按分类过滤
        if (this.currentCategory !== 'all') {
            prompts = prompts.filter(prompt => prompt.category === this.currentCategory);
        }
        
        // 按搜索词过滤
        if (this.searchQuery) {
            const query = this.searchQuery.toLowerCase();
            prompts = prompts.filter(prompt => 
                prompt.title.toLowerCase().includes(query) ||
                prompt.description.toLowerCase().includes(query) ||
                prompt.tags.some(tag => tag.toLowerCase().includes(query))
            );
        }
        
        // 按选中的标签过滤
        if (this.plugin.settings.selectedTags.length > 0) {
            prompts = prompts.filter(prompt => 
                this.plugin.settings.selectedTags.some(selectedTag => 
                    prompt.tags.includes(selectedTag)
                )
            );
        }
        
        return prompts;
    }

    private createPromptCard(container: Element, prompt: PromptItem) {
        const card = container.createDiv('prompt-card');
        
        // 标题
        const title = card.createDiv('prompt-card-title');
        title.textContent = prompt.title;
        
        // 描述
        const description = card.createDiv('prompt-card-description');
        description.textContent = prompt.description;
        
        // 标签
        if (prompt.tags.length > 0) {
            const tagsContainer = card.createDiv('prompt-card-tags');
            prompt.tags.forEach(tag => {
                const tagEl = tagsContainer.createSpan('prompt-card-tag');
                tagEl.textContent = tag;
            });
        }
        
        // 操作按钮
        const actionsContainer = card.createDiv('prompt-card-actions');
        
        // 复制按钮
        const copyBtn = actionsContainer.createEl('button', {
            text: '复制',
            cls: 'prompt-action-btn'
        });
        copyBtn.addEventListener('click', () => {
            navigator.clipboard.writeText(prompt.content);
            new Notice('提示词已复制到剪贴板');
        });
        
        // 编辑按钮
        const editBtn = actionsContainer.createEl('button', {
            text: '编辑',
            cls: 'prompt-action-btn edit'
        });
        editBtn.addEventListener('click', () => {
            new PromptEditModal(this.app, this.plugin, prompt, () => {
                this.render();
            }).open();
        });
        
        // AI改写按钮
        const aiRewriteBtn = actionsContainer.createEl('button', {
            text: 'AI改写',
            cls: 'prompt-action-btn ai-rewrite'
        });
        aiRewriteBtn.addEventListener('click', () => {
            this.aiRewritePrompt(prompt);
        });
        
        // 删除按钮
        const deleteBtn = actionsContainer.createEl('button', {
            text: '删除',
            cls: 'prompt-action-btn danger'
        });
        deleteBtn.addEventListener('click', () => {
            this.deletePrompt(prompt.id);
        });
    }

    private async deletePrompt(id: string) {
        const promptIndex = this.plugin.settings.prompts.findIndex(p => p.id === id);
        if (promptIndex > -1) {
            const deletedPrompt = this.plugin.settings.prompts.splice(promptIndex, 1)[0];
            
            // 将删除的提示词添加到回收站
            this.plugin.settings.deletedPrompts.push({
                prompt: deletedPrompt,
                deletedAt: new Date()
            });
            
            // 限制回收站大小，只保留最近10个删除的提示词
            if (this.plugin.settings.deletedPrompts.length > 10) {
                this.plugin.settings.deletedPrompts.shift();
            }
            
            await this.plugin.saveSettings();
            this.render();
            new Notice('提示词已删除，可通过撤销按钮恢复');
        }
    }

    private async aiRewritePrompt(prompt: PromptItem) {
        new AiRewriteModal(this.app, this.plugin, prompt, () => {
            this.render();
        }).open();
    }

    private openAiGenerateModal() {
        console.log('Opening AI Generate Modal...');
        try {
            const modal = new AiGenerateModal(this.app, this.plugin, (newPrompt) => {
                this.plugin.settings.prompts.push(newPrompt);
                this.plugin.saveSettings();
                this.render();
            });
            console.log('Modal created, opening...');
            modal.open();
            console.log('Modal opened successfully');
        } catch (error) {
            console.error('Error opening AI Generate Modal:', error);
            new Notice('打开AI生成对话框时出错: ' + error.message);
        }
    }
}

// 编辑模态框
class PromptEditModal extends Modal {
    plugin: PromptLibraryPlugin;
    prompt: PromptItem | null;
    onSave: () => void;
    
    titleInput: TextComponent;
    descriptionInput: TextAreaComponent;
    contentInput: TextAreaComponent;
    categorySelect: HTMLSelectElement;
    tagsInput: TextAreaComponent;

    constructor(app: App, plugin: PromptLibraryPlugin, prompt: PromptItem | null, onSave: () => void) {
        super(app);
        this.plugin = plugin;
        this.prompt = prompt;
        this.onSave = onSave;
    }

    onOpen() {
        const { contentEl } = this;
        contentEl.empty();
        
        contentEl.createEl('h2', { text: this.prompt ? '编辑提示词' : '添加新提示词' });
        
        // 标题输入
        new Setting(contentEl)
            .setName('标题')
            .addText(text => {
                this.titleInput = text;
                text.setPlaceholder('输入提示词标题');
                if (this.prompt) {
                    text.setValue(this.prompt.title);
                }
            });
        
        // 描述输入
        new Setting(contentEl)
            .setName('描述')
            .addTextArea(text => {
                this.descriptionInput = text;
                text.setPlaceholder('输入提示词描述');
                if (this.prompt) {
                    text.setValue(this.prompt.description);
                }
                text.inputEl.rows = 3;
                text.inputEl.style.minHeight = '80px';
            });
        
        // 分类选择
        new Setting(contentEl)
            .setName('分类')
            .addDropdown(dropdown => {
                this.plugin.settings.categories.forEach(category => {
                    dropdown.addOption(category.id, category.name);
                });
                if (this.prompt) {
                    dropdown.setValue(this.prompt.category);
                } else {
                    dropdown.setValue(this.plugin.settings.defaultCategory);
                }
                this.categorySelect = dropdown.selectEl;
            });
        
        // 标签输入
        new Setting(contentEl)
            .setName('标签')
            .setDesc('每行输入一个标签，或用逗号分隔')
            .addTextArea(text => {
                this.tagsInput = text;
                text.setPlaceholder('标签1\n标签2\n标签3\n或者: 标签1, 标签2, 标签3');
                if (this.prompt) {
                    text.setValue(this.prompt.tags.join('\n'));
                }
                text.inputEl.rows = 4;
                text.inputEl.style.minHeight = '80px';
            });
        
        // 内容输入
        new Setting(contentEl)
            .setName('提示词内容')
            .addTextArea(text => {
                this.contentInput = text;
                text.setPlaceholder('输入完整的提示词内容...');
                if (this.prompt) {
                    text.setValue(this.prompt.content);
                }
                text.inputEl.rows = 12;
                text.inputEl.style.minHeight = '200px';
                text.inputEl.style.resize = 'vertical';
            });
        
        // 按钮
        const buttonContainer = contentEl.createDiv('prompt-modal-buttons');
        
        const saveButton = buttonContainer.createEl('button', {
            text: '保存',
            cls: 'mod-cta'
        });
        saveButton.addEventListener('click', () => this.save());
        
        const cancelButton = buttonContainer.createEl('button', {
            text: '取消'
        });
        cancelButton.addEventListener('click', () => this.close());
    }

    async save() {
        const title = this.titleInput.getValue().trim();
        const description = this.descriptionInput.getValue().trim();
        const content = this.contentInput.getValue().trim();
        const category = this.categorySelect.value;
        const tagsString = this.tagsInput.getValue().trim();
        
        // 支持换行分隔和逗号分隔
        let tags: string[] = [];
        if (tagsString) {
            // 先按换行分割，然后对每行再按逗号分割
            const lines = tagsString.split('\n');
            lines.forEach(line => {
                const lineTags = line.split(',').map(tag => tag.trim()).filter(tag => tag);
                tags.push(...lineTags);
            });
            // 去重
            tags = [...new Set(tags)];
        }
        
        if (!title || !description || !content) {
            new Notice('请填写所有必填字段');
            return;
        }
        
        if (this.prompt) {
            // 更新现有提示词
            this.prompt.title = title;
            this.prompt.description = description;
            this.prompt.content = content;
            this.prompt.category = category;
            this.prompt.tags = tags;
            this.prompt.updatedAt = new Date();
        } else {
            // 创建新提示词
            const newPrompt: PromptItem = {
                id: Date.now().toString(36) + Math.random().toString(36).substr(2),
                title,
                description,
                content,
                category,
                tags,
                createdAt: new Date(),
                updatedAt: new Date()
            };
            this.plugin.settings.prompts.push(newPrompt);
        }
        
        await this.plugin.saveSettings();
        this.onSave();
        this.close();
        new Notice(this.prompt ? '提示词已更新' : '提示词已添加');
    }
}

// 设置页面
class PromptLibrarySettingTab extends PluginSettingTab {
    plugin: PromptLibraryPlugin;

    constructor(app: App, plugin: PromptLibraryPlugin) {
        super(app, plugin);
        this.plugin = plugin;
    }

    display(): void {
        const { containerEl } = this;
        containerEl.empty();
        
        containerEl.createEl('h2', { text: '提示词库设置' });
        
        // AI API 配置
        containerEl.createEl('h3', { text: 'AI改写配置' });
        
        new Setting(containerEl)
            .setName('API URL')
            .setDesc('AI服务的API地址（如：https://api.openai.com/v1/chat/completions）')
            .addText(text => text
                .setPlaceholder('输入API URL')
                .setValue(this.plugin.settings.aiApiUrl)
                .onChange(async (value) => {
                    this.plugin.settings.aiApiUrl = value;
                    await this.plugin.saveSettings();
                }));
        
        new Setting(containerEl)
            .setName('API Key')
            .setDesc('AI服务的API密钥')
            .addText(text => {
                text.setPlaceholder('输入API Key')
                    .setValue(this.plugin.settings.aiApiKey)
                    .onChange(async (value) => {
                        this.plugin.settings.aiApiKey = value;
                        await this.plugin.saveSettings();
                    });
                text.inputEl.type = 'password';
            });
        
        new Setting(containerEl)
            .setName('AI模型')
            .setDesc('使用的AI模型名称')
            .addText(text => text
                .setPlaceholder('如：gpt-3.5-turbo')
                .setValue(this.plugin.settings.aiModel)
                .onChange(async (value) => {
                    this.plugin.settings.aiModel = value;
                    await this.plugin.saveSettings();
                }));
        
        // 默认分类设置
        containerEl.createEl('h3', { text: '基本设置' });
        
        new Setting(containerEl)
            .setName('默认分类')
            .setDesc('新建提示词时的默认分类')
            .addDropdown(dropdown => {
                this.plugin.settings.categories.forEach(category => {
                    dropdown.addOption(category.id, category.name);
                });
                dropdown.setValue(this.plugin.settings.defaultCategory);
                dropdown.onChange(async (value) => {
                    this.plugin.settings.defaultCategory = value;
                    await this.plugin.saveSettings();
                });
            });
        
        // 分类管理
        containerEl.createEl('h3', { text: '分类管理' });
        
        const categoryContainer = containerEl.createDiv('category-management');
        this.displayCategories(categoryContainer);
        
        // 添加新分类按钮
        new Setting(containerEl)
            .setName('添加新分类')
            .addButton(button => {
                button.setButtonText('添加分类');
                button.onClick(() => {
                    new CategoryEditModal(this.app, this.plugin, null, () => {
                        this.display();
                    }).open();
                });
            });
    }
    
    displayCategories(container: Element) {
        container.empty();
        
        this.plugin.settings.categories.forEach(category => {
            new Setting(container as HTMLElement)
                .setName(category.name)
                .addButton(button => {
                    button.setButtonText('编辑');
                    button.onClick(() => {
                        new CategoryEditModal(this.app, this.plugin, category, () => {
                            this.display();
                        }).open();
                    });
                })
                .addButton(button => {
                    button.setButtonText('删除');
                    button.setWarning();
                    button.onClick(async () => {
                        this.plugin.settings.categories = this.plugin.settings.categories.filter(c => c.id !== category.id);
                        await this.plugin.saveSettings();
                        this.display();
                        new Notice('分类已删除');
                    });
                });
        });
    }
}

// AI改写模态框
class AiRewriteModal extends Modal {
    plugin: PromptLibraryPlugin;
    prompt: PromptItem;
    onSave: () => void;
    requirementInput: TextAreaComponent;
    resultContainer: HTMLElement;
    isGenerating: boolean = false;

    constructor(app: App, plugin: PromptLibraryPlugin, prompt: PromptItem, onSave: () => void) {
        super(app);
        this.plugin = plugin;
        this.prompt = prompt;
        this.onSave = onSave;
    }

    onOpen() {
        const { contentEl } = this;
        contentEl.empty();
        contentEl.addClass('ai-rewrite-modal');
        
        contentEl.createEl('h2', { text: 'AI改写提示词' });
        
        // 显示原始提示词信息
        const originalSection = contentEl.createDiv('original-prompt-section');
        originalSection.createEl('h3', { text: '原始提示词' });
        
        const originalInfo = originalSection.createDiv('original-prompt-info');
        originalInfo.createEl('div', { text: `标题：${this.prompt.title}`, cls: 'prompt-info-item' });
        originalInfo.createEl('div', { text: `描述：${this.prompt.description}`, cls: 'prompt-info-item' });
        
        const contentPreview = originalInfo.createDiv('prompt-content-preview');
        contentPreview.createEl('div', { text: '内容预览：', cls: 'prompt-info-label' });
        const contentText = contentPreview.createEl('div', { cls: 'prompt-content-text' });
        contentText.textContent = this.prompt.content.length > 200 ? 
            this.prompt.content.substring(0, 200) + '...' : this.prompt.content;
        
        // 改写要求输入
        const requirementSection = contentEl.createDiv('requirement-section');
        requirementSection.createEl('h3', { text: '改写要求' });
        
        new Setting(requirementSection)
            .setName('请描述您的改写要求')
            .setDesc('例如：使其更加专业、增加具体示例、简化表达等')
            .addTextArea(text => {
                this.requirementInput = text;
                text.setPlaceholder('请输入您希望如何改写这个提示词...');
                text.inputEl.rows = 6;
                text.inputEl.style.minHeight = '150px';
            });
        
        // 生成按钮
        const generateButton = requirementSection.createEl('button', {
            text: '🤖 开始改写',
            cls: 'mod-cta ai-generate-btn'
        });
        generateButton.addEventListener('click', () => this.generateRewrite());
        
        // 结果显示区域
        this.resultContainer = contentEl.createDiv('ai-result-container');
        
        // 底部按钮
        const buttonContainer = contentEl.createDiv('modal-button-container');
        
        const closeButton = buttonContainer.createEl('button', {
            text: '关闭',
            cls: 'modal-button'
        });
        closeButton.addEventListener('click', () => this.close());
    }

    async generateRewrite() {
        const requirement = this.requirementInput.getValue().trim();
        
        if (!requirement) {
            new Notice('请输入改写要求');
            return;
        }
        
        if (this.isGenerating) {
            return;
        }
        
        const settings = this.plugin.settings;
        if (!settings.aiApiUrl || !settings.aiApiKey) {
            new Notice('请先在设置中配置AI API信息');
            return;
        }
        
        this.isGenerating = true;
        this.resultContainer.empty();
        
        const loadingDiv = this.resultContainer.createDiv('loading-container');
        loadingDiv.createEl('div', { text: '🤖 AI正在改写中...', cls: 'loading-text' });
        
        try {
            const response = await fetch(settings.aiApiUrl, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${settings.aiApiKey}`
                },
                body: JSON.stringify({
                    model: settings.aiModel,
                    messages: [
                        {
                            role: 'system',
                            content: '你是一个专业的提示词优化专家。请根据用户的要求改写和优化提示词，使其更加符合用户的需求。请严格按照指定格式返回结果。'
                        },
                        {
                            role: 'user',
                            content: `请根据以下要求改写提示词：\n\n原始提示词：\n标题：${this.prompt.title}\n描述：${this.prompt.description}\n内容：${this.prompt.content}\n标签：${this.prompt.tags.join(', ')}\n\n改写要求：${requirement}\n\n请返回改写后的结果，严格按照以下格式：\n标题：[改写后的标题]\n描述：[改写后的描述]\n内容：[改写后的内容]\n标签：[相关标签，用逗号分隔]`
                        }
                    ],
                    temperature: 0.7,
                    max_tokens: 1500
                })
            });
            
            if (!response.ok) {
                throw new Error(`API请求失败: ${response.status}`);
            }
            
            const data = await response.json();
            const aiResponse = data.choices[0].message.content;
            
            this.displayResult(aiResponse);
            
        } catch (error) {
            console.error('AI改写失败:', error);
            this.resultContainer.empty();
            this.resultContainer.createEl('div', { 
                text: 'AI改写失败，请检查网络连接和API配置', 
                cls: 'error-message' 
            });
        } finally {
            this.isGenerating = false;
        }
    }

    displayResult(aiResponse: string) {
        this.resultContainer.empty();
        
        const resultSection = this.resultContainer.createDiv('result-section');
        resultSection.createEl('h3', { text: 'AI改写结果' });
        
        // 解析AI返回的内容
        const titleMatch = aiResponse.match(/标题：(.+?)(?=\n|$)/);
        const descMatch = aiResponse.match(/描述：(.+?)(?=\n|$)/);
        const contentMatch = aiResponse.match(/内容：([\s\S]+?)(?=\n标签：|\n\n|$)/);
        const tagsMatch = aiResponse.match(/标签：(.+?)(?=\n|$)/);
        
        if (titleMatch && descMatch && contentMatch) {
            const tags = tagsMatch ? 
                tagsMatch[1].split(',').map(tag => tag.trim()).filter(tag => tag) : 
                this.prompt.tags; // 如果没有新标签，保留原有标签
            
            const rewrittenPrompt = {
                ...this.prompt,
                title: titleMatch[1].trim(),
                description: descMatch[1].trim(),
                content: contentMatch[1].trim(),
                tags: tags,
                updatedAt: new Date()
            };
            
            // 显示改写结果
            const resultDisplay = resultSection.createDiv('rewritten-prompt-display');
            
            resultDisplay.createEl('div', { text: `标题：${rewrittenPrompt.title}`, cls: 'result-item' });
            resultDisplay.createEl('div', { text: `描述：${rewrittenPrompt.description}`, cls: 'result-item' });
            
            if (rewrittenPrompt.tags.length > 0) {
                resultDisplay.createEl('div', { text: `标签：${rewrittenPrompt.tags.join(', ')}`, cls: 'result-item' });
            }
            
            const contentDiv = resultDisplay.createDiv('result-content');
            contentDiv.createEl('div', { text: '内容：', cls: 'result-label' });
            const contentText = contentDiv.createEl('div', { cls: 'result-content-text' });
            contentText.textContent = rewrittenPrompt.content;
            
            // 操作按钮
            const actionButtons = resultSection.createDiv('result-actions');
            
            const acceptButton = actionButtons.createEl('button', {
                text: '✅ 接受并保存',
                cls: 'mod-cta'
            });
            acceptButton.addEventListener('click', () => {
                // 更新原始提示词
                const index = this.plugin.settings.prompts.findIndex(p => p.id === this.prompt.id);
                if (index !== -1) {
                    this.plugin.settings.prompts[index] = rewrittenPrompt;
                    this.plugin.saveSettings();
                    this.onSave();
                    this.close();
                    new Notice('提示词已更新');
                }
            });
            
            const editButton = actionButtons.createEl('button', {
                text: '✏️ 编辑后保存',
                cls: 'modal-button'
            });
            editButton.addEventListener('click', () => {
                this.close();
                new PromptEditModal(this.app, this.plugin, rewrittenPrompt, () => {
                    this.onSave();
                }).open();
            });
            
            const regenerateButton = actionButtons.createEl('button', {
                text: '🔄 重新生成',
                cls: 'modal-button'
            });
            regenerateButton.addEventListener('click', () => {
                this.generateRewrite();
            });
            
        } else {
            resultSection.createEl('div', { 
                text: 'AI返回格式异常，请重试', 
                cls: 'error-message' 
            });
        }
    }
}

// AI生成模态框
class AiGenerateModal extends Modal {
    plugin: PromptLibraryPlugin;
    onSave: (prompt: PromptItem) => void;
    requirementInput: TextAreaComponent;
    categorySelect: HTMLSelectElement;
    resultContainer: HTMLElement;
    isGenerating: boolean = false;

    constructor(app: App, plugin: PromptLibraryPlugin, onSave: (prompt: PromptItem) => void) {
        super(app);
        this.plugin = plugin;
        this.onSave = onSave;
    }

    onOpen() {
        console.log('AiGenerateModal onOpen called');
        const { contentEl } = this;
        contentEl.empty();
        contentEl.addClass('ai-generate-modal');
        
        console.log('Modal content element:', contentEl);
        console.log('Modal element classes:', contentEl.className);
        
        contentEl.createEl('h2', { text: 'AI生成提示词' });
        
        // 需求描述输入
        const requirementSection = contentEl.createDiv('requirement-section');
        requirementSection.createEl('h3', { text: '描述您的需求' });
        
        new Setting(requirementSection)
            .setName('请详细描述您需要的提示词')
            .setDesc('例如：我需要一个帮助写作的提示词，能够指导用户写出高质量的技术文档')
            .addTextArea(text => {
                this.requirementInput = text;
                text.setPlaceholder('请详细描述您需要什么样的提示词，它应该具备什么功能...');
                text.inputEl.rows = 6;
                text.inputEl.style.minHeight = '200px';
            });
        
        // 分类选择
        new Setting(requirementSection)
            .setName('选择分类')
            .addDropdown(dropdown => {
                this.plugin.settings.categories.forEach(category => {
                    if (category.id !== 'all') {
                        dropdown.addOption(category.id, category.name);
                    }
                });
                dropdown.setValue(this.plugin.settings.defaultCategory);
                this.categorySelect = dropdown.selectEl;
            });
        
        // 生成按钮
        const generateButton = requirementSection.createEl('button', {
            text: '🤖 生成提示词',
            cls: 'mod-cta ai-generate-btn'
        });
        generateButton.addEventListener('click', () => this.generatePrompt());
        
        // 结果显示区域
        this.resultContainer = contentEl.createDiv('ai-result-container');
        
        // 底部按钮
        const buttonContainer = contentEl.createDiv('modal-button-container');
        
        const closeButton = buttonContainer.createEl('button', {
            text: '关闭',
            cls: 'modal-button'
        });
        closeButton.addEventListener('click', () => this.close());
    }

    async generatePrompt() {
        const requirement = this.requirementInput.getValue().trim();
        
        if (!requirement) {
            new Notice('请输入需求描述');
            return;
        }
        
        if (this.isGenerating) {
            return;
        }
        
        const settings = this.plugin.settings;
        if (!settings.aiApiUrl || !settings.aiApiKey) {
            new Notice('请先在设置中配置AI API信息');
            return;
        }
        
        this.isGenerating = true;
        this.resultContainer.empty();
        
        const loadingDiv = this.resultContainer.createDiv('loading-container');
        loadingDiv.createEl('div', { text: '🤖 AI正在生成提示词...', cls: 'loading-text' });
        
        try {
            const response = await fetch(settings.aiApiUrl, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${settings.aiApiKey}`
                },
                body: JSON.stringify({
                    model: settings.aiModel,
                    messages: [
                        {
                            role: 'system',
                            content: '你是一个专业的提示词创作专家。请根据用户的需求，创作出高质量、实用的提示词。提示词应该清晰、具体、易于理解和使用。请严格按照指定格式返回结果。'
                        },
                        {
                            role: 'user',
                            content: `请根据以下需求创作一个提示词：\n\n需求描述：${requirement}\n\n请创作一个符合需求的提示词，并严格按照以下格式返回：\n标题：[提示词标题]\n描述：[提示词的简短描述]\n内容：[完整的提示词内容]\n标签：[相关标签，用逗号分隔]`
                        }
                    ],
                    temperature: 0.8,
                    max_tokens: 1500
                })
            });
            
            if (!response.ok) {
                throw new Error(`API请求失败: ${response.status}`);
            }
            
            const data = await response.json();
            const aiResponse = data.choices[0].message.content;
            
            this.displayResult(aiResponse);
            
        } catch (error) {
            console.error('AI生成失败:', error);
            this.resultContainer.empty();
            this.resultContainer.createEl('div', { 
                text: 'AI生成失败，请检查网络连接和API配置', 
                cls: 'error-message' 
            });
        } finally {
            this.isGenerating = false;
        }
    }

    displayResult(aiResponse: string) {
        this.resultContainer.empty();
        
        const resultSection = this.resultContainer.createDiv('result-section');
        resultSection.createEl('h3', { text: 'AI生成结果' });
        
        // 解析AI返回的内容
        const titleMatch = aiResponse.match(/标题：(.+?)(?=\n|$)/);
        const descMatch = aiResponse.match(/描述：(.+?)(?=\n|$)/);
        const contentMatch = aiResponse.match(/内容：([\s\S]+?)(?=\n标签：|$)/);
        const tagsMatch = aiResponse.match(/标签：(.+?)(?=\n|$)/);
        
        if (titleMatch && descMatch && contentMatch) {
            const tags = tagsMatch ? 
                tagsMatch[1].split(',').map(tag => tag.trim()).filter(tag => tag) : [];
            
            const newPrompt: PromptItem = {
                id: Date.now().toString(36) + Math.random().toString(36).substr(2),
                title: titleMatch[1].trim(),
                description: descMatch[1].trim(),
                content: contentMatch[1].trim(),
                category: this.categorySelect.value,
                tags: tags,
                createdAt: new Date(),
                updatedAt: new Date()
            };
            
            // 显示生成结果
            const resultDisplay = resultSection.createDiv('generated-prompt-display');
            
            resultDisplay.createEl('div', { text: `标题：${newPrompt.title}`, cls: 'result-item' });
            resultDisplay.createEl('div', { text: `描述：${newPrompt.description}`, cls: 'result-item' });
            
            if (newPrompt.tags.length > 0) {
                resultDisplay.createEl('div', { text: `标签：${newPrompt.tags.join(', ')}`, cls: 'result-item' });
            }
            
            const contentDiv = resultDisplay.createDiv('result-content');
            contentDiv.createEl('div', { text: '内容：', cls: 'result-label' });
            const contentText = contentDiv.createEl('div', { cls: 'result-content-text' });
            contentText.textContent = newPrompt.content;
            
            // 操作按钮
            const actionButtons = resultSection.createDiv('result-actions');
            
            const acceptButton = actionButtons.createEl('button', {
                text: '✅ 接受并保存',
                cls: 'mod-cta'
            });
            acceptButton.addEventListener('click', () => {
                this.onSave(newPrompt);
                this.close();
                new Notice('提示词已添加');
            });
            
            const editButton = actionButtons.createEl('button', {
                text: '✏️ 编辑后保存',
                cls: 'modal-button'
            });
            editButton.addEventListener('click', () => {
                this.close();
                new PromptEditModal(this.app, this.plugin, newPrompt, () => {
                    // 编辑模态框会处理保存
                }).open();
            });
            
            const regenerateButton = actionButtons.createEl('button', {
                text: '🔄 重新生成',
                cls: 'modal-button'
            });
            regenerateButton.addEventListener('click', () => {
                this.generatePrompt();
            });
            
        } else {
            resultSection.createEl('div', { 
                text: 'AI返回格式异常，请重试', 
                cls: 'error-message' 
            });
        }
    }
}

// 分类编辑模态框
class CategoryEditModal extends Modal {
    plugin: PromptLibraryPlugin;
    category: PromptCategory | null;
    onSave: () => void;
    nameInput: TextComponent;

    constructor(app: App, plugin: PromptLibraryPlugin, category: PromptCategory | null, onSave: () => void) {
        super(app);
        this.plugin = plugin;
        this.category = category;
        this.onSave = onSave;
    }

    onOpen() {
        const { contentEl } = this;
        contentEl.empty();
        
        contentEl.createEl('h2', { text: this.category ? '编辑分类' : '添加新分类' });
        
        new Setting(contentEl)
            .setName('分类名称')
            .addText(text => {
                this.nameInput = text;
                text.setPlaceholder('输入分类名称');
                if (this.category) {
                    text.setValue(this.category.name);
                }
            });
        
        const buttonContainer = contentEl.createDiv('category-modal-buttons');
        
        const saveButton = buttonContainer.createEl('button', {
            text: '保存',
            cls: 'mod-cta'
        });
        saveButton.addEventListener('click', () => this.save());
        
        const cancelButton = buttonContainer.createEl('button', {
            text: '取消'
        });
        cancelButton.addEventListener('click', () => this.close());
    }

    async save() {
        const name = this.nameInput.getValue().trim();
        
        if (!name) {
            new Notice('请输入分类名称');
            return;
        }
        
        if (this.category) {
            this.category.name = name;
        } else {
            const newCategory: PromptCategory = {
                id: Date.now().toString(36) + Math.random().toString(36).substr(2),
                name
            };
            this.plugin.settings.categories.push(newCategory);
        }
        
        await this.plugin.saveSettings();
        this.onSave();
        this.close();
        new Notice(this.category ? '分类已更新' : '分类已添加');
    }
}