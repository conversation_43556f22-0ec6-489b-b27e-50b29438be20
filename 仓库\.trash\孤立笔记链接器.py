#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
孤立笔记链接器
找到孤立笔记（只有1个或没有链接的笔记）并为它们建立与其他笔记的链接
"""

import os
import re
from pathlib import Path
from datetime import datetime
import hashlib

class IsolatedNoteLinkGenerator:
    def __init__(self, base_dir):
        self.base_dir = Path(base_dir)
        self.all_notes = {}  # 存储所有笔记信息
        self.isolated_notes = []  # 孤立笔记列表
        self.processed_count = 0
        
    def get_core_keywords(self):
        """核心关键词库"""
        return {
            '股权融资': ['股权', '股份', '股东', '控制权', '投票权', '分红权', '优先股', '普通股', 'AB股', 
                      '期权池', '融资', '投资', '估值', '对赌协议', '清算优先权', '领售权', '反稀释', '一票否决权'],
            '法律合规': ['合同', '协议', '法律风险', '合规', '知识产权', '竞业禁止', '保密协议', '劳动合同',
                      '法律', '律师', '诉讼', '仲裁', '违约', '责任', '义务', '权利'],
            '财务管理': ['财务报表', '现金流', '利润', '成本', '税务', '审计', '会计', '预算', '资产负债',
                      '收入', '支出', '财务', '资金', '资本', '负债', '资产'],
            '战略管理': ['战略', '策略', '商业模式', '竞争', '市场', '客户', '产品', '运营', '管理',
                      '规划', '目标', '愿景', '使命', '价值观', '文化'],
            '组织人力': ['领导力', '团队', '人才', '招聘', '培训', '绩效', '激励', '文化', '组织',
                      '员工', '人力资源', '管理', '沟通', '协作', '领导'],
            '风险控制': ['风险', '危机', '应急', '预警', '防范', '控制', '监控', '评估', '识别',
                      '安全', '保险', '担保', '抵押', '质押'],
            '市场营销': ['营销', '推广', '品牌', '广告', '客户', '用户', '市场', '销售', '渠道',
                      '定位', '传播', '公关', '媒体', '社交']
        }
    
    def extract_existing_links(self, content):
        """提取现有的双链"""
        # 提取 [[链接]] 格式的内部链接
        internal_links = re.findall(r'\[\[([^\]]+)\]\]', content)
        return internal_links
    
    def extract_keywords_from_content(self, content):
        """从内容中提取关键词"""
        found_keywords = []
        all_keywords = self.get_core_keywords()
        
        for category, keywords in all_keywords.items():
            for keyword in keywords:
                if keyword in content:
                    found_keywords.append((keyword, category))
        
        return found_keywords
    
    def should_exclude_file(self, file_path):
        """判断是否应该排除文件"""
        path_str = str(file_path).lower()
        
        # 排除特定目录
        exclude_dirs = ['默写本', '.obsidian', '.trash', '原子笔记', '知识卡片', 'templates', '模板']
        if any(exclude_dir in path_str for exclude_dir in exclude_dirs):
            return True
            
        # 排除临时文件
        if any(temp in path_str for temp in ['temp', '临时', 'untitled', '未命名']):
            return True
            
        return False
    
    def analyze_note(self, file_path):
        """分析单个笔记"""
        if self.should_exclude_file(file_path):
            return None
            
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
        except:
            return None
        
        # 提取信息
        title = file_path.stem
        keywords = self.extract_keywords_from_content(content)
        existing_links = self.extract_existing_links(content)
        
        note_info = {
            'path': file_path,
            'title': title,
            'content': content,
            'keywords': keywords,
            'existing_links': existing_links,
            'link_count': len(existing_links),
            'word_count': len(content)
        }
        
        return note_info
    
    def find_related_notes(self, target_note, max_suggestions=5):
        """找到与目标笔记相关的其他笔记"""
        related_notes = []
        target_keywords = set([kw[0] for kw in target_note['keywords']])
        target_title_words = set(target_note['title'].split())
        
        for other_title, other_note in self.all_notes.items():
            if other_title == target_note['title']:
                continue
                
            # 如果已经有链接，跳过
            if other_title in target_note['existing_links']:
                continue
            
            other_keywords = set([kw[0] for kw in other_note['keywords']])
            other_title_words = set(other_note['title'].split())
            
            # 计算相关度分数
            score = 0
            
            # 关键词重叠
            keyword_overlap = len(target_keywords & other_keywords)
            score += keyword_overlap * 10
            
            # 标题词汇重叠
            title_overlap = len(target_title_words & other_title_words)
            score += title_overlap * 15
            
            # 标题包含关系
            for word in target_title_words:
                if len(word) > 2 and word in other_note['title']:
                    score += 8
            
            # 内容相似度（简单检查）
            target_content_words = set(target_note['content'][:500].split())
            other_content_words = set(other_note['content'][:500].split())
            content_overlap = len(target_content_words & other_content_words)
            if content_overlap > 10:
                score += content_overlap * 2
            
            if score >= 15:  # 相关度阈值
                related_notes.append((other_note, score, keyword_overlap))
        
        # 按分数排序，返回前N个
        related_notes.sort(key=lambda x: x[1], reverse=True)
        return related_notes[:max_suggestions]
    
    def add_links_to_note(self, note, related_notes):
        """为笔记添加链接"""
        if not related_notes:
            return False
            
        content = note['content']
        
        # 检查是否已有相关链接部分
        if '## 相关笔记' in content:
            # 更新现有的相关链接部分
            lines = content.split('\n')
            new_lines = []
            in_related_section = False
            
            for line in lines:
                if line.startswith('## 相关笔记'):
                    in_related_section = True
                    new_lines.append(line)
                    new_lines.append(f"*更新于 {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}*\n")
                    
                    # 添加新的相关笔记
                    for related_note, score, overlap in related_notes:
                        new_lines.append(f"- [[{related_note['title']}]] - 相关度: {score} (共同关键词: {overlap}个)")
                    
                elif in_related_section and line.startswith('##'):
                    # 遇到下一个章节，结束相关笔记部分
                    in_related_section = False
                    new_lines.append(line)
                elif not in_related_section:
                    new_lines.append(line)
            
            content = '\n'.join(new_lines)
        else:
            # 添加新的相关链接部分
            content += f"\n\n---\n\n## 相关笔记\n"
            content += f"*由系统自动生成于 {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}*\n\n"
            
            for related_note, score, overlap in related_notes:
                content += f"- [[{related_note['title']}]] - 相关度: {score} (共同关键词: {overlap}个)\n"
        
        # 保存修改后的内容
        try:
            with open(note['path'], 'w', encoding='utf-8') as f:
                f.write(content)
            return True
        except:
            return False
    
    def create_link_report(self):
        """创建链接报告"""
        report_path = self.base_dir / "孤立笔记链接报告.md"
        
        report_content = f"""# 孤立笔记链接报告

生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
分析笔记数量: {len(self.all_notes)}
孤立笔记数量: {len(self.isolated_notes)}
已处理笔记数量: {self.processed_count}

## 统计信息

### 链接分布
"""
        
        # 统计链接分布
        link_stats = {'0个链接': 0, '1个链接': 0, '2-5个链接': 0, '6+个链接': 0}
        
        for note in self.all_notes.values():
            count = note['link_count']
            if count == 0:
                link_stats['0个链接'] += 1
            elif count == 1:
                link_stats['1个链接'] += 1
            elif count <= 5:
                link_stats['2-5个链接'] += 1
            else:
                link_stats['6+个链接'] += 1
        
        for category, count in link_stats.items():
            report_content += f"- **{category}**: {count}个笔记\n"
        
        report_content += f"\n## 已处理的孤立笔记\n\n"
        
        # 列出已处理的笔记
        processed_notes = [note for note in self.isolated_notes if hasattr(note, 'processed') and note.processed]
        for note in processed_notes[:50]:  # 只显示前50个
            report_content += f"- **{note['title']}** - 原有链接: {note['link_count']}个\n"
        
        if len(processed_notes) > 50:
            report_content += f"\n... 还有 {len(processed_notes) - 50} 个笔记已处理\n"
        
        with open(report_path, 'w', encoding='utf-8') as f:
            f.write(report_content)
        
        return report_path
    
    def run(self, max_links_threshold=1, add_links=True):
        """运行孤立笔记链接器"""
        print(f"孤立笔记链接器")
        print(f"处理目录: {self.base_dir}")
        print(f"孤立阈值: 链接数 ≤ {max_links_threshold}")
        print("=" * 50)
        
        # 收集所有笔记
        print("收集笔记文件...")
        md_files = list(self.base_dir.rglob("*.md"))
        
        for file_path in md_files:
            note_info = self.analyze_note(file_path)
            if note_info:
                self.all_notes[note_info['title']] = note_info
        
        print(f"找到有效笔记: {len(self.all_notes)}个")
        
        # 找到孤立笔记
        print("识别孤立笔记...")
        for note in self.all_notes.values():
            if note['link_count'] <= max_links_threshold:
                self.isolated_notes.append(note)
        
        print(f"发现孤立笔记: {len(self.isolated_notes)}个")
        
        if add_links:
            print("\n开始为孤立笔记添加链接...")
            
            for note in self.isolated_notes:
                print(f"处理: {note['title']}")
                
                # 找到相关笔记
                related_notes = self.find_related_notes(note)
                
                if related_notes:
                    # 添加链接
                    if self.add_links_to_note(note, related_notes):
                        self.processed_count += 1
                        note.processed = True
                        print(f"  ✓ 添加了 {len(related_notes)} 个链接")
                    else:
                        print(f"  ✗ 添加链接失败")
                else:
                    print(f"  - 未找到相关笔记")
        
        # 创建报告
        report_path = self.create_link_report()
        
        print(f"\n✅ 完成！")
        print(f"📊 分析了 {len(self.all_notes)} 个笔记")
        print(f"🔍 发现 {len(self.isolated_notes)} 个孤立笔记")
        print(f"🔗 成功处理 {self.processed_count} 个笔记")
        print(f"📄 报告保存在: {report_path}")

if __name__ == "__main__":
    # 配置基础目录
    base_directory = Path(r"c:\Users\<USER>\OneDrive\obsidian笔记系统\战略与战术")
    
    print("孤立笔记链接器")
    print("=" * 50)
    print("选择处理模式:")
    print("1. 仅生成分析报告")
    print("2. 为孤立笔记添加链接")
    
    choice = input("请选择 (1/2): ").strip()
    add_links = (choice == "2")
    
    if add_links:
        print("\n选择孤立笔记阈值:")
        print("1. 0个链接的笔记")
        print("2. 1个或更少链接的笔记（推荐）")
        print("3. 2个或更少链接的笔记")
        
        threshold_choice = input("请选择 (1/2/3): ").strip()
        threshold_map = {"1": 0, "2": 1, "3": 2}
        max_links_threshold = threshold_map.get(threshold_choice, 1)
        
        confirm = input(f"确认要为链接数≤{max_links_threshold}的笔记添加链接吗？(y/N): ").strip().lower()
        add_links = (confirm == 'y')
    else:
        max_links_threshold = 1
    
    # 运行链接器
    generator = IsolatedNoteLinkGenerator(base_directory)
    generator.run(max_links_threshold=max_links_threshold, add_links=add_links)
