#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
有价值笔记链接器
只为有价值的笔记建立链接，排除股权财务等无用内容和日记类笔记
"""

import os
import re
from pathlib import Path
from datetime import datetime

def extract_links(content):
    """提取现有链接"""
    return re.findall(r'\[\[([^\]]+)\]\]', content)

def is_valuable_note(file_path, content):
    """判断是否是有价值的笔记"""
    filename = file_path.stem.lower()
    content_lower = content.lower()
    
    # 排除日记类
    if re.match(r'^\d{4}-\d{2}-\d{2}', filename):  # 日期格式
        return False
    if re.match(r'^\d{4}\d{2}\d{2}', filename):
        return False
    if any(word in filename for word in ['daily', 'journal', '日记', '日志']):
        return False
    
    # 排除股权财务等无用内容
    useless_keywords = [
        '股权', '股份', '股东', '融资', '投资', '对赌', '清算',
        '财务', '现金流', '利润', '成本', '税务', '会计',
        '法律', '合规', '合同', '协议', '知识产权',
        '董事会', '控制权', '投票权', 'ab股', '期权'
    ]
    
    # 如果标题或内容主要是这些无用关键词，排除
    useless_count = sum(1 for keyword in useless_keywords if keyword in filename or keyword in content_lower[:200])
    if useless_count >= 3:  # 如果包含3个或更多无用关键词，认为是无用笔记
        return False
    
    # 排除模板和系统文件
    if any(word in filename for word in ['template', '模板', 'untitled', '未命名', 'new ']):
        return False
    
    # 排除太短的笔记（可能是无意义的）
    if len(content.strip()) < 50:
        return False
    
    return True

def scan_valuable_notes():
    """扫描有价值的笔记"""
    handbook_dir = Path(r"c:\Users\<USER>\OneDrive\obsidian笔记系统\战略与战术\智库辅助手册")
    valuable_notes = []
    
    print(f"扫描有价值的笔记...")
    
    for file_path in handbook_dir.rglob("*.md"):
        # 排除脚本工具目录
        if '脚本工具' in str(file_path):
            continue
            
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
        except:
            continue
        
        # 只处理有价值的笔记
        if not is_valuable_note(file_path, content):
            continue
        
        title = file_path.stem
        links = extract_links(content)
        
        valuable_notes.append({
            'path': file_path,
            'title': title,
            'content': content,
            'links': links,
            'link_count': len(links),
            'relative_path': str(file_path.relative_to(handbook_dir))
        })
    
    return valuable_notes

def get_valuable_links():
    """获取有价值的链接"""
    return [
        "团队管理核心法则",
        "商业模式设计思路", 
        "市场营销核心策略",
        "人才招聘与培养",
        "企业文化建设指南",
        "产品开发管理流程",
        "客户关系管理系统",
        "竞争对手分析方法",
        "创新管理机制",
        "战略规划制定",
        "执行力提升方案",
        "绩效考核体系",
        "组织架构优化",
        "数字化转型路径",
        "供应链优化策略"
    ]

def select_relevant_links(note):
    """为笔记选择相关链接"""
    title = note['title'].lower()
    content = note['content'].lower()
    full_text = f"{title} {content}"
    
    all_links = get_valuable_links()
    selected = []
    
    # 基于关键词匹配
    keyword_mapping = {
        '管理': ["团队管理核心法则", "企业文化建设指南", "组织架构优化"],
        '团队': ["团队管理核心法则", "人才招聘与培养", "企业文化建设指南"],
        '营销': ["市场营销核心策略", "客户关系管理系统", "商业模式设计思路"],
        '市场': ["市场营销核心策略", "竞争对手分析方法", "商业模式设计思路"],
        '产品': ["产品开发管理流程", "创新管理机制", "商业模式设计思路"],
        '战略': ["战略规划制定", "竞争对手分析方法", "执行力提升方案"],
        '创新': ["创新管理机制", "数字化转型路径", "产品开发管理流程"],
        '客户': ["客户关系管理系统", "市场营销核心策略", "商业模式设计思路"],
        '绩效': ["绩效考核体系", "团队管理核心法则", "执行力提升方案"],
        '数字化': ["数字化转型路径", "创新管理机制", "供应链优化策略"]
    }
    
    # 根据关键词匹配选择链接
    for keyword, related_links in keyword_mapping.items():
        if keyword in full_text:
            for link in related_links:
                if link not in note['links'] and link not in selected:
                    selected.append(link)
                    if len(selected) >= 3:
                        break
            if len(selected) >= 3:
                break
    
    # 如果没有匹配到足够的链接，添加通用有价值链接
    if len(selected) < 3:
        default_links = [
            "团队管理核心法则",
            "商业模式设计思路", 
            "市场营销核心策略"
        ]
        for link in default_links:
            if link not in note['links'] and link not in selected:
                selected.append(link)
                if len(selected) >= 3:
                    break
    
    return selected

def add_links_to_note(note, new_links):
    """为笔记添加链接"""
    if not new_links:
        return False
    
    content = note['content']
    
    # 添加相关笔记部分
    if '## 相关笔记' not in content:
        content += f"\n\n---\n\n## 相关笔记\n"
        content += f"*有价值链接于 {datetime.now().strftime('%Y-%m-%d')}*\n\n"
        
        for link in new_links:
            content += f"- [[{link}]]\n"
        
        try:
            with open(note['path'], 'w', encoding='utf-8') as f:
                f.write(content)
            return True
        except:
            return False
    
    return False

def main():
    """主函数"""
    print("有价值笔记链接器")
    print("=" * 25)
    print("排除：日记类、股权财务类、模板类、过短笔记")
    
    # 扫描有价值的笔记
    valuable_notes = scan_valuable_notes()
    print(f"\n找到有价值笔记: {len(valuable_notes)}个")
    
    # 找到需要处理的孤立笔记
    isolated_notes = [note for note in valuable_notes if note['link_count'] < 3]
    
    print(f"其中孤立笔记(少于3个链接): {len(isolated_notes)}个")
    
    if len(isolated_notes) == 0:
        print("🎉 所有有价值的笔记都有足够链接！")
        return
    
    # 显示部分孤立笔记
    print(f"\n孤立笔记示例:")
    for i, note in enumerate(isolated_notes[:10]):
        print(f"  {i+1}. {note['title']} ({note['link_count']}个链接)")
    if len(isolated_notes) > 10:
        print(f"  ... 还有{len(isolated_notes)-10}个")
    
    # 处理孤立笔记
    print(f"\n开始处理有价值的孤立笔记...")
    success_count = 0
    
    for i, note in enumerate(isolated_notes):
        if i % 10 == 0:
            print(f"进度: {i+1}/{len(isolated_notes)}")
        
        # 选择相关链接
        new_links = select_relevant_links(note)
        
        if new_links and add_links_to_note(note, new_links):
            success_count += 1
    
    print(f"\n✅ 有价值笔记处理完成！")
    print(f"📊 处理了: {len(isolated_notes)}个孤立笔记")
    print(f"🔗 成功处理: {success_count}个")
    print(f"📈 成功率: {success_count/len(isolated_notes)*100:.1f}%")

if __name__ == "__main__":
    main()
