---
title: 端到端加密（E2EE）的优势
source: "[[为什么AES-256存储加密+HTTPS传输仍然不够安全？]]"
tags:
  - 端到端加密
  - 安全
  - 加密
  - 百度
keywords:
  - 端到端加密
  - 安全
  - 加密
  - 百度
  - 密钥
created: 2025-08-02
type: 原子笔记
aliases:
  - |-
    端到端加密（E2EE）的优势1. 加密发生在用户设备上，百度服务器只能存储乱码。
    2. 密钥仅存于用户设备，百度没有解密能力。
已学: true
---

# 端到端加密（E2EE）的优势1. 加密发生在用户设备上，百度服务器只能存储乱码。2. 密钥仅存于用户设备，百度没有解密能力。

端到端加密（E2EE）的优势：
1. 加密发生在用户设备上，百度服务器只能存储乱码。
2. 密钥仅存于用户设备，百度没有解密能力。

---

## 元信息
- **来源笔记**: [[为什么AES-256存储加密+HTTPS传输仍然不够安全？]]
- **创建时间**: 2025/8/2 21:03:07
- **标签**: #端到端加密 #安全 #加密 #百度
- **关键词**: 端到端加密, 安全, 加密, 百度, 密钥

## 相关链接
- 返回原笔记: [[为什么AES-256存储加密+HTTPS传输仍然不够安全？]]
