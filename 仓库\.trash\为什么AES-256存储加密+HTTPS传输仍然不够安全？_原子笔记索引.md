# 为什么AES-256存储加密+HTTPS传输仍然不够安全？ - 原子笔记索引

> 本索引由原子笔记切分工具自动生成
> 生成时间: 2025/8/2 21:03:10
> 原始笔记: [[为什么AES-256存储加密+HTTPS传输仍然不够安全？]]

## 统计信息
- 原始笔记: [[为什么AES-256存储加密+HTTPS传输仍然不够安全？]]
- 切分出的原子笔记数量: 5
- 生成时间: 2025/8/2 21:03:10

## 原子笔记列表

1. [[AES-256存储加密与HTTPS传输的不足1. 百度公司持有AES-256存储加密的主密钥，员工可以解密用户文件。2. HTTPS传输加密使用临时会话密钥，但无法防止WiFi黑客截获。3. 端到端加密（E2EE）才是彻底防止第三方获取数据的方法。]] - AES-256存储加密与HTTPS传输的不足
2. [[百度访问用户文件的原因-上传时，百度服务器用其控制的AES-256密钥重新加密存储。2. 访问时，百度用其密钥解密文件，然后通过HTTPS传回给用户。3. 解密过程发生在百度服务器上，百度可以看到原始内容。]] - 百度访问用户文件的原因
3. [[黑客的攻击方式-1. 入侵百度服务器，窃取存储的AES-256密钥，解密所有用户文件。2. 钓鱼攻击用户账号，获取密码，用用户身份查看文件。]] - 黑客的攻击方式
4. [[端到端加密（E2EE）的优势1. 加密发生在用户设备上，百度服务器只能存储乱码。2. 密钥仅存于用户设备，百度没有解密能力。]] - 端到端加密（E2EE）的优势
5. [[如何保护数据-1. 在必须使用E2EE的场景下使用。2. 选择合适的工具，如Obsidian官方同步、Veracrypt+Cryptomator、LiveSync+CouchDB。3. 不要使用百度网盘存储敏感数据。4. 检查服务条款，确保使用端到端加密的服务。]] - 如何保护数据

## 标签分类

### #加密
- [[AES-256存储加密与HTTPS传输的不足1. 百度公司持有AES-256存储加密的主密钥，员工可以解密用户文件。2. HTTPS传输加密使用临时会话密钥，但无法防止WiFi黑客截获。3. 端到端加密（E2EE）才是彻底防止第三方获取数据的方法。]]
- [[百度访问用户文件的原因-上传时，百度服务器用其控制的AES-256密钥重新加密存储。2. 访问时，百度用其密钥解密文件，然后通过HTTPS传回给用户。3. 解密过程发生在百度服务器上，百度可以看到原始内容。]]
- [[黑客的攻击方式-1. 入侵百度服务器，窃取存储的AES-256密钥，解密所有用户文件。2. 钓鱼攻击用户账号，获取密码，用用户身份查看文件。]]
- [[端到端加密（E2EE）的优势1. 加密发生在用户设备上，百度服务器只能存储乱码。2. 密钥仅存于用户设备，百度没有解密能力。]]
- [[如何保护数据-1. 在必须使用E2EE的场景下使用。2. 选择合适的工具，如Obsidian官方同步、Veracrypt+Cryptomator、LiveSync+CouchDB。3. 不要使用百度网盘存储敏感数据。4. 检查服务条款，确保使用端到端加密的服务。]]

### #安全
- [[AES-256存储加密与HTTPS传输的不足1. 百度公司持有AES-256存储加密的主密钥，员工可以解密用户文件。2. HTTPS传输加密使用临时会话密钥，但无法防止WiFi黑客截获。3. 端到端加密（E2EE）才是彻底防止第三方获取数据的方法。]]
- [[端到端加密（E2EE）的优势1. 加密发生在用户设备上，百度服务器只能存储乱码。2. 密钥仅存于用户设备，百度没有解密能力。]]
- [[如何保护数据-1. 在必须使用E2EE的场景下使用。2. 选择合适的工具，如Obsidian官方同步、Veracrypt+Cryptomator、LiveSync+CouchDB。3. 不要使用百度网盘存储敏感数据。4. 检查服务条款，确保使用端到端加密的服务。]]

### #AES-256
- [[AES-256存储加密与HTTPS传输的不足1. 百度公司持有AES-256存储加密的主密钥，员工可以解密用户文件。2. HTTPS传输加密使用临时会话密钥，但无法防止WiFi黑客截获。3. 端到端加密（E2EE）才是彻底防止第三方获取数据的方法。]]

### #HTTPS
- [[AES-256存储加密与HTTPS传输的不足1. 百度公司持有AES-256存储加密的主密钥，员工可以解密用户文件。2. HTTPS传输加密使用临时会话密钥，但无法防止WiFi黑客截获。3. 端到端加密（E2EE）才是彻底防止第三方获取数据的方法。]]
- [[百度访问用户文件的原因-上传时，百度服务器用其控制的AES-256密钥重新加密存储。2. 访问时，百度用其密钥解密文件，然后通过HTTPS传回给用户。3. 解密过程发生在百度服务器上，百度可以看到原始内容。]]

### #百度
- [[百度访问用户文件的原因-上传时，百度服务器用其控制的AES-256密钥重新加密存储。2. 访问时，百度用其密钥解密文件，然后通过HTTPS传回给用户。3. 解密过程发生在百度服务器上，百度可以看到原始内容。]]
- [[黑客的攻击方式-1. 入侵百度服务器，窃取存储的AES-256密钥，解密所有用户文件。2. 钓鱼攻击用户账号，获取密码，用用户身份查看文件。]]
- [[端到端加密（E2EE）的优势1. 加密发生在用户设备上，百度服务器只能存储乱码。2. 密钥仅存于用户设备，百度没有解密能力。]]

### #文件访问
- [[百度访问用户文件的原因-上传时，百度服务器用其控制的AES-256密钥重新加密存储。2. 访问时，百度用其密钥解密文件，然后通过HTTPS传回给用户。3. 解密过程发生在百度服务器上，百度可以看到原始内容。]]

### #黑客
- [[黑客的攻击方式-1. 入侵百度服务器，窃取存储的AES-256密钥，解密所有用户文件。2. 钓鱼攻击用户账号，获取密码，用用户身份查看文件。]]

### #攻击
- [[黑客的攻击方式-1. 入侵百度服务器，窃取存储的AES-256密钥，解密所有用户文件。2. 钓鱼攻击用户账号，获取密码，用用户身份查看文件。]]

### #端到端加密
- [[端到端加密（E2EE）的优势1. 加密发生在用户设备上，百度服务器只能存储乱码。2. 密钥仅存于用户设备，百度没有解密能力。]]

### #数据保护
- [[如何保护数据-1. 在必须使用E2EE的场景下使用。2. 选择合适的工具，如Obsidian官方同步、Veracrypt+Cryptomator、LiveSync+CouchDB。3. 不要使用百度网盘存储敏感数据。4. 检查服务条款，确保使用端到端加密的服务。]]

### #工具
- [[如何保护数据-1. 在必须使用E2EE的场景下使用。2. 选择合适的工具，如Obsidian官方同步、Veracrypt+Cryptomator、LiveSync+CouchDB。3. 不要使用百度网盘存储敏感数据。4. 检查服务条款，确保使用端到端加密的服务。]]

---
*此索引文件由原子笔记切分工具生成*
