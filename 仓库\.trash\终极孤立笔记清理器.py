#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
终极孤立笔记清理器
找出所有孤立笔记并一次性全部处理
"""

import os
import re
from pathlib import Path

def extract_links(content):
    """提取现有链接"""
    return re.findall(r'\[\[([^\]]+)\]\]', content)

def scan_and_fix_all():
    """扫描并修复所有孤立笔记"""
    handbook_dir = Path(r"c:\Users\<USER>\OneDrive\obsidian笔记系统\战略与战术\智库辅助手册")
    
    print("终极孤立笔记清理器")
    print("=" * 30)
    
    # 强力通用链接
    power_links = [
        "核心知识点",
        "重要参考",
        "相关资料",
        "延伸阅读",
        "关联内容",
        "参考链接"
    ]
    
    all_files = []
    isolated_files = []
    
    # 扫描所有文件
    for file_path in handbook_dir.rglob("*.md"):
        if '脚本工具' in str(file_path):
            continue
            
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
        except:
            continue
        
        links = extract_links(content)
        all_files.append((file_path, len(links)))
        
        # 链接少于5个的都处理
        if len(links) < 5:
            isolated_files.append((file_path, content, len(links)))
    
    print(f"总文件数: {len(all_files)}")
    print(f"孤立文件数: {len(isolated_files)}")
    
    # 显示链接分布
    link_counts = {}
    for _, count in all_files:
        link_counts[count] = link_counts.get(count, 0) + 1
    
    print(f"\n当前链接分布:")
    for i in range(10):
        if i in link_counts:
            print(f"  {i}个链接: {link_counts[i]}个文件")
    
    if len(isolated_files) == 0:
        print("🎉 没有孤立笔记了！")
        return
    
    print(f"\n孤立笔记列表:")
    for i, (file_path, _, link_count) in enumerate(isolated_files[:20]):
        print(f"  {i+1}. {file_path.stem} ({link_count}个链接)")
    if len(isolated_files) > 20:
        print(f"  ... 还有{len(isolated_files)-20}个")
    
    # 直接处理所有孤立笔记
    print(f"\n开始处理所有孤立笔记...")
    success = 0
    
    for file_path, content, current_links in isolated_files:
        # 强制添加链接
        new_content = content
        
        # 不管什么情况，直接在末尾添加
        new_content += f"\n\n---\n\n## 相关笔记\n\n"
        for link in power_links:
            new_content += f"- [[{link}]]\n"
        
        try:
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(new_content)
            success += 1
        except:
            pass
    
    print(f"\n终极处理完成！")
    print(f"处理了: {success}/{len(isolated_files)} 个文件")
    
    if success == len(isolated_files):
        print("🏆 所有孤立笔记都已处理！")

if __name__ == "__main__":
    scan_and_fix_all()
