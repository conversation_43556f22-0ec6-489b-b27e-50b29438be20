# HTTPS = 加密版HTTP - 原子笔记索引

> 本索引由原子笔记切分工具自动生成
> 生成时间: 2025/8/2 20:37:42
> 原始笔记: [[HTTPS = 加密版HTTP]]

## 统计信息
- 原始笔记: [[HTTPS = 加密版HTTP]]
- 切分出的原子笔记数量: 7
- 生成时间: 2025/8/2 20:37:42

## 原子笔记列表

1. [[HTTP同步的风险]] - HTTP同步的风险
2. [[HTTPS的加密原理-加密版HTTP，数据在传输过程中会被打乱，只有持有解密钥匙的收件人才能解开]] - HTTPS的加密原理
3. [[HTTPS的应用场景-咖啡厅、机场、酒店等公共WiFi环境下使用]] - HTTPS的应用场景
4. [[自建同步的加密措施]] - 自建同步的加密措施
5. [[同步安全风险清单-列出机场酒店WiFi、公司内网和家庭WiFi等不同场景下的同步安全风险]] - 同步安全风险清单
6. [[同步安全检测方法]] - 同步安全检测方法
7. [[同步安全建议-重要笔记使用HTTPS同步或官方端到端加密，自建服务器使用Caddy自动配置HTTPS]] - 同步安全建议

## 标签分类

### #HTTP
- [[HTTP同步的风险]]

### #网络安全
- [[HTTP同步的风险]]
- [[HTTPS的应用场景-咖啡厅、机场、酒店等公共WiFi环境下使用]]
- [[同步安全风险清单-列出机场酒店WiFi、公司内网和家庭WiFi等不同场景下的同步安全风险]]

### #传输协议
- [[HTTP同步的风险]]

### #HTTPS
- [[HTTPS的加密原理-加密版HTTP，数据在传输过程中会被打乱，只有持有解密钥匙的收件人才能解开]]
- [[HTTPS的应用场景-咖啡厅、机场、酒店等公共WiFi环境下使用]]
- [[自建同步的加密措施]]
- [[同步安全检测方法]]
- [[同步安全建议-重要笔记使用HTTPS同步或官方端到端加密，自建服务器使用Caddy自动配置HTTPS]]

### #加密
- [[HTTPS的加密原理-加密版HTTP，数据在传输过程中会被打乱，只有持有解密钥匙的收件人才能解开]]

### #解密钥匙
- [[HTTPS的加密原理-加密版HTTP，数据在传输过程中会被打乱，只有持有解密钥匙的收件人才能解开]]

### #公共WiFi
- [[HTTPS的应用场景-咖啡厅、机场、酒店等公共WiFi环境下使用]]

### #自建同步
- [[自建同步的加密措施]]

### #VPN
- [[自建同步的加密措施]]

### #同步安全
- [[同步安全风险清单-列出机场酒店WiFi、公司内网和家庭WiFi等不同场景下的同步安全风险]]
- [[同步安全检测方法]]
- [[同步安全建议-重要笔记使用HTTPS同步或官方端到端加密，自建服务器使用Caddy自动配置HTTPS]]

### #风险清单
- [[同步安全风险清单-列出机场酒店WiFi、公司内网和家庭WiFi等不同场景下的同步安全风险]]

### #检测方法
- [[同步安全检测方法]]

### #建议
- [[同步安全建议-重要笔记使用HTTPS同步或官方端到端加密，自建服务器使用Caddy自动配置HTTPS]]

---
*此索引文件由原子笔记切分工具生成*
