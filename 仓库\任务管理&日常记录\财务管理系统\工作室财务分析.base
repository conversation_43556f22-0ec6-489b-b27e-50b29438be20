filters:
  and:
    - file.inFolder("财务管理系统/工作室财务数据")
    - 'file.ext == "md"'

formulas:
  # 表1：项目利润分析表 - 核心计算公式
  project_profit_rate: 'if(contract_amount && human_cost && outsource_cost, ((contract_amount - human_cost - outsource_cost) / contract_amount * 100).toFixed(1) + "%", "未计算")'
  remaining_amount: 'if(contract_amount && received_amount, contract_amount - received_amount, 0)'
  overdue_status: 'if(is_overdue, "❌ 已逾期", "✅ 正常")'
  
  # 表2：人力效率监控表 - 核心计算公式  
  idle_rate: 'if(project_hours && idle_hours, ((idle_hours / (project_hours + idle_hours)) * 100).toFixed(1) + "%", "0%")'
  efficiency_ratio: 'if(revenue_generated && human_cost, (revenue_generated / human_cost).toFixed(1), "0")'
  efficiency_status: 'if(formula.efficiency_ratio >= 7, "✅ 健康", if(formula.efficiency_ratio >= 5, "⚠️ 注意", "❌ 偏低"))'
  
  # 表3：现金流预警表 - 核心计算公式
  cash_surplus: 'if(expected_income && expected_expense, expected_income - expected_expense, 0)'
  cash_status: 'if(cash_balance >= 50, "✅ 安全", if(cash_balance >= 25, "⚠️ 注意", "❗ 预警"))'
  action_priority: 'if(formula.cash_surplus < 0, "🔴 紧急", if(formula.cash_surplus < 10, "🟡 关注", "🟢 正常"))'

properties:
  # 通用字段
  table_type:
    displayName: "表格类型"
  date:
    displayName: "日期"
  month:
    displayName: "月份"
  
  # 表1：项目利润分析表字段
  project_name:
    displayName: "项目名称"
  client_type:
    displayName: "客户类型"
  contract_amount:
    displayName: "合同金额(万)"
  received_amount:
    displayName: "已收款(万)"
  formula.remaining_amount:
    displayName: "应收款(万)"
  human_cost:
    displayName: "人力成本(万)"
  outsource_cost:
    displayName: "外包成本(万)"
  formula.project_profit_rate:
    displayName: "项目毛利率"
  payment_cycle:
    displayName: "付款账期"
  is_overdue:
    displayName: "是否逾期"
  formula.overdue_status:
    displayName: "逾期状态"
  
  # 表2：人力效率监控表字段
  consultant_name:
    displayName: "顾问姓名"
  project_hours:
    displayName: "本月项目工时"
  idle_hours:
    displayName: "闲置工时"
  project_count:
    displayName: "参与项目数"
  revenue_generated:
    displayName: "创收金额(万)"
  human_cost_monthly:
    displayName: "人力成本(万)"
  formula.efficiency_ratio:
    displayName: "人效比(创收/成本)"
  formula.idle_rate:
    displayName: "闲置率"
  formula.efficiency_status:
    displayName: "效率状态"
  
  # 表3：现金流预警表字段
  expected_income:
    displayName: "预计收入(万)"
  expected_expense:
    displayName: "预计支出(万)"
  cash_balance:
    displayName: "现金结余(万)"
  formula.cash_surplus:
    displayName: "现金盈余(万)"
  formula.cash_status:
    displayName: "现金状态"
  key_actions:
    displayName: "关键动作"
  formula.action_priority:
    displayName: "行动优先级"
  
  # 备注字段
  notes:
    displayName: "备注"

views:
  - type: table
    name: "表1：项目利润分析表"
    filters:
      and:
        - 'table_type == "项目利润分析"'
    order:
      - project_name
      - client_type
      - contract_amount
      - received_amount
      - formula.remaining_amount
      - human_cost
      - outsource_cost
      - formula.project_profit_rate
      - payment_cycle
      - formula.overdue_status
    sort:
      - property: formula.project_profit_rate
        direction: DESC
    limit: 20

  - type: table
    name: "表2：人力效率监控表"
    filters:
      and:
        - 'table_type == "人力效率监控"'
    order:
      - consultant_name
      - project_hours
      - idle_hours
      - project_count
      - revenue_generated
      - human_cost_monthly
      - formula.efficiency_ratio
      - formula.idle_rate
      - formula.efficiency_status
    sort:
      - property: formula.efficiency_ratio
        direction: DESC
    limit: 15

  - type: table
    name: "表3：现金流预警表"
    filters:
      and:
        - 'table_type == "现金流预警"'
    order:
      - month
      - expected_income
      - expected_expense
      - cash_balance
      - formula.cash_surplus
      - formula.cash_status
      - key_actions
      - formula.action_priority
    sort:
      - property: month
        direction: ASC
    limit: 12

  - type: cards
    name: "逾期项目预警"
    filters:
      and:
        - 'table_type == "项目利润分析"'
        - 'is_overdue == true'
    order:
      - project_name
      - client_type
      - formula.remaining_amount
      - payment_cycle
      - notes
    sort:
      - property: formula.remaining_amount
        direction: DESC
    limit: 10

  - type: cards
    name: "低效顾问提醒"
    filters:
      and:
        - 'table_type == "人力效率监控"'
        - 'formula.efficiency_ratio < 5'
    order:
      - consultant_name
      - formula.efficiency_ratio
      - formula.idle_rate
      - project_count
      - notes
    sort:
      - property: formula.efficiency_ratio
        direction: ASC
    limit: 8

  - type: cards
    name: "现金流紧急预警"
    filters:
      and:
        - 'table_type == "现金流预警"'
        - 'formula.action_priority == "🔴 紧急"'
    order:
      - month
      - formula.cash_surplus
      - cash_balance
      - key_actions
    sort:
      - property: month
        direction: ASC
    limit: 6

  - type: table
    name: "财务总览仪表盘"
    filters:
      or:
        - 'table_type == "项目利润分析"'
        - 'table_type == "人力效率监控"'
        - 'table_type == "现金流预警"'
    order:
      - table_type
      - project_name
      - consultant_name
      - month
      - formula.project_profit_rate
      - formula.efficiency_ratio
      - formula.cash_status
    sort:
      - property: date
        direction: DESC
    limit: 30
