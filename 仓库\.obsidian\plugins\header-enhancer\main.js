/*
THIS IS A GENERATED/BUNDLED FILE BY ESBUILD
if you want to view the source, please visit the github repository of this plugin
*/

var __defProp = Object.defineProperty;
var __getOwnPropDesc = Object.getOwnPropertyDescriptor;
var __getOwnPropNames = Object.getOwnPropertyNames;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __export = (target, all) => {
  for (var name in all)
    __defProp(target, name, { get: all[name], enumerable: true });
};
var __copyProps = (to, from, except, desc) => {
  if (from && typeof from === "object" || typeof from === "function") {
    for (let key of __getOwnPropNames(from))
      if (!__hasOwnProp.call(to, key) && key !== except)
        __defProp(to, key, { get: () => from[key], enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable });
  }
  return to;
};
var __toCommonJS = (mod) => __copyProps(__defProp({}, "__esModule", { value: true }), mod);

// src/main.ts
var main_exports = {};
__export(main_exports, {
  default: () => HeaderEnhancerPlugin
});
module.exports = __toCommonJS(main_exports);
var import_obsidian3 = require("obsidian");
var import_view = require("@codemirror/view");
var import_state = require("@codemirror/state");

// src/core.ts
function getHeaderLevel(text, startHeaderLevel) {
  const match = text.match(/^#+/);
  if (!match)
    return [0, 0];
  let level = match ? match[0].length : 0;
  return [level - startHeaderLevel + 1, level];
}
function getNextNumber(cntNums, headerLevel) {
  let nextNums = [...cntNums];
  if (nextNums.length >= headerLevel) {
    nextNums = nextNums.slice(0, headerLevel);
    nextNums[nextNums.length - 1]++;
  } else {
    while (nextNums.length < headerLevel) {
      nextNums.push(1);
    }
  }
  return nextNums;
}
function isNeedInsertNumber(text, splitor) {
  if (splitor == " ")
    return text.split(splitor).length === 2;
  else
    return !text.contains(splitor);
}
function isNeedUpdateNumber(nextNumsStr, text, splitor) {
  let cntNumsStr;
  if (splitor == " ") {
    cntNumsStr = text.split(splitor)[1];
  } else {
    cntNumsStr = text.split(splitor)[0].split(" ")[0];
  }
  return nextNumsStr !== cntNumsStr;
}
function removeHeaderNumber(text, splitor) {
  let sharp, header;
  if (splitor == " ") {
    sharp = text.split(splitor)[0];
    header = text.split(splitor)[2];
  } else {
    if (!text.contains(splitor))
      return text;
    sharp = text.split(splitor)[0].split(" ")[0];
    header = text.split(splitor)[1];
  }
  return sharp + " " + header;
}
function isHeader(text) {
  return /^#{1,6} .*/.test(text.trim());
}

// src/utils.ts
var import_obsidian = require("obsidian");
var YAML_REGEX = /^---\n(?:((?:.|\n)*?)\n)?---(?=\n|$)/;
var DEFAULT_YAML_SETTING = [
  "state on",
  "start-level h2",
  "max-level h1",
  "start-number 1",
  "separator ."
];
function getYaml(editor) {
  var _a;
  const matchResult = editor.getValue().match(YAML_REGEX);
  return (_a = matchResult == null ? void 0 : matchResult[0]) != null ? _a : "";
}
function getAutoNumberingYaml(editor) {
  var _a;
  const yaml = getYaml(editor);
  const parsedYaml = (0, import_obsidian.parseYaml)(yaml.slice(4, -4));
  return (_a = parsedYaml == null ? void 0 : parsedYaml["header-auto-numbering"]) != null ? _a : "";
}
function setAutoNumberingYaml(editor, value = DEFAULT_YAML_SETTING) {
  const yaml = getYaml(editor);
  const parsedYaml = (0, import_obsidian.parseYaml)(yaml.slice(4, -4));
  if (parsedYaml) {
    parsedYaml["header-auto-numbering"] = value;
  }
  const newContent = `---
${(0, import_obsidian.stringifyYaml)(parsedYaml)}---`;
  const startPosition = { line: 0, ch: 0 };
  const endPosition = editor.offsetToPos(yaml.length);
  editor.replaceRange(newContent, startPosition, endPosition);
}

// src/setting.ts
var import_obsidian2 = require("obsidian");

// src/i18n/en.ts
var en_default = {
  settings: {
    title: "Header Enhancer Settings",
    general: "General",
    language: {
      name: "Language",
      desc: "Language for automatic numbering"
    },
    statusBar: {
      name: "Show on Status Bar",
      desc: "Show current header level on status bar"
    },
    autoDetect: {
      name: "Auto Detect Header Level",
      desc: "Automatically detect header level based on context"
    },
    headerLevel: {
      start: {
        name: "Start Header Level",
        desc: "The starting level for headers"
      },
      max: {
        name: "Max Header Level",
        desc: "Maximum level for headers"
      }
    },
    autoNumbering: {
      title: "Header Auto Numbering",
      enable: {
        name: "Enable Auto Numbering",
        desc: "Enable automatic header numbering",
        notice: "You can only change this option in side bar"
      },
      useYaml: {
        name: "Use YAML",
        desc: "Use YAML to control the format of header numbers"
      },
      headerLevel: {
        name: "Header Level Settings",
        desc: "Configure the header level settings"
      },
      startNumber: {
        name: "Start Number",
        desc: "Start numbering at this number",
        placeholder: "Enter a number"
      },
      separator: {
        name: "Number Separator",
        desc: "Separator between numbers (one of '. , / -')",
        placeholder: "Enter separator"
      },
      headerSeparator: {
        name: "Header Separator",
        desc: "Separator between header number and text",
        error: "You can't change header separator when auto numbering is enabled"
      },
      format: {
        name: "Your auto numbering format is",
        fromLevel: "from H",
        toLevel: "to H",
        autoDetect: "[Auto Detect]",
        manual: "[Manual]"
      }
    },
    font: {
      title: "Title Font Settings",
      separate: {
        name: "Separate Title Font",
        desc: "Use different font settings for titles",
        notice: "This feature is not available now, please wait for the next version"
      },
      family: {
        name: "Font Family",
        desc: "Title font family (inherit from global font by default)",
        placeholder: "global font"
      },
      size: {
        name: "Font Size",
        desc: "Title font size (inherit from global font size by default)",
        placeholder: "global font size"
      }
    },
    reset: {
      name: "Reset Settings",
      confirm: "Are you sure you want to reset settings to default?"
    },
    moreInfo: "More Information",
    author: "Author: ",
    license: "License: ",
    githubRepo: "Github Repository: ",
    anyQuestion: "Any questions? Send feedback on "
  }
};

// src/i18n/zh.ts
var zh_default = {
  settings: {
    title: "\u6807\u9898\u589E\u5F3A\u8BBE\u7F6E",
    general: "\u5E38\u89C4",
    language: {
      name: "\u8BED\u8A00",
      desc: "\u81EA\u52A8\u7F16\u53F7\u7684\u8BED\u8A00"
    },
    statusBar: {
      name: "\u5728\u72B6\u6001\u680F\u663E\u793A",
      desc: "\u5728\u72B6\u6001\u680F\u663E\u793A\u5F53\u524D\u6807\u9898\u7EA7\u522B"
    },
    autoDetect: {
      name: "\u81EA\u52A8\u68C0\u6D4B\u6807\u9898\u7EA7\u522B",
      desc: "\u6839\u636E\u4E0A\u4E0B\u6587\u81EA\u52A8\u68C0\u6D4B\u6807\u9898\u7EA7\u522B"
    },
    headerLevel: {
      start: {
        name: "\u8D77\u59CB\u6807\u9898\u7EA7\u522B",
        desc: "\u6807\u9898\u7684\u8D77\u59CB\u7EA7\u522B"
      },
      max: {
        name: "\u6700\u5927\u6807\u9898\u7EA7\u522B",
        desc: "\u6807\u9898\u7684\u6700\u5927\u7EA7\u522B"
      }
    },
    autoNumbering: {
      title: "\u6807\u9898\u81EA\u52A8\u7F16\u53F7",
      enable: {
        name: "\u542F\u7528\u81EA\u52A8\u7F16\u53F7",
        desc: "\u542F\u7528\u6807\u9898\u81EA\u52A8\u7F16\u53F7",
        notice: "\u6B64\u9009\u9879\u53EA\u80FD\u5728\u4FA7\u8FB9\u680F\u4E2D\u66F4\u6539"
      },
      useYaml: {
        name: "\u4F7F\u7528YAML",
        desc: "\u4F7F\u7528YAML\u63A7\u5236\u6807\u9898\u7F16\u53F7\u683C\u5F0F"
      },
      headerLevel: {
        name: "\u6807\u9898\u7EA7\u522B\u8BBE\u7F6E",
        desc: "\u914D\u7F6E\u6807\u9898\u7EA7\u522B\u8BBE\u7F6E"
      },
      startNumber: {
        name: "\u8D77\u59CB\u7F16\u53F7",
        desc: "\u81EA\u52A8\u7F16\u53F7\u7684\u8D77\u59CB\u6570\u5B57",
        placeholder: "\u8F93\u5165\u6570\u5B57"
      },
      separator: {
        name: "\u6570\u5B57\u5206\u9694\u7B26",
        desc: "\u6570\u5B57\u4E4B\u95F4\u7684\u5206\u9694\u7B26\uFF08\u53EF\u9009 '. , / -'\uFF09",
        placeholder: "\u8F93\u5165\u5206\u9694\u7B26"
      },
      headerSeparator: {
        name: "\u6807\u9898\u5206\u9694\u7B26",
        desc: "\u6807\u9898\u7F16\u53F7\u548C\u6587\u672C\u4E4B\u95F4\u7684\u5206\u9694\u7B26",
        error: "\u542F\u7528\u81EA\u52A8\u7F16\u53F7\u65F6\u65E0\u6CD5\u66F4\u6539\u6807\u9898\u5206\u9694\u7B26"
      },
      format: {
        name: "\u4F60\u7684\u81EA\u52A8\u7F16\u53F7\u683C\u5F0F\u662F",
        fromLevel: "\u4ECE H",
        toLevel: "\u5230 H",
        autoDetect: "[\u81EA\u52A8\u68C0\u6D4B]",
        manual: "[\u624B\u52A8]"
      }
    },
    font: {
      title: "\u6807\u9898\u5B57\u4F53\u8BBE\u7F6E",
      separate: {
        name: "\u72EC\u7ACB\u6807\u9898\u5B57\u4F53",
        desc: "\u4E3A\u6807\u9898\u4F7F\u7528\u4E0D\u540C\u7684\u5B57\u4F53\u8BBE\u7F6E",
        notice: "\u6B64\u529F\u80FD\u6682\u4E0D\u53EF\u7528\uFF0C\u8BF7\u7B49\u5F85\u4E0B\u4E00\u4E2A\u7248\u672C"
      },
      family: {
        name: "\u5B57\u4F53\u7CFB\u5217",
        desc: "\u6807\u9898\u5B57\u4F53\u7CFB\u5217\uFF08\u9ED8\u8BA4\u7EE7\u627F\u5168\u5C40\u5B57\u4F53\uFF09",
        placeholder: "\u5168\u5C40\u5B57\u4F53"
      },
      size: {
        name: "\u5B57\u4F53\u5927\u5C0F",
        desc: "\u6807\u9898\u5B57\u4F53\u5927\u5C0F\uFF08\u9ED8\u8BA4\u7EE7\u627F\u5168\u5C40\u5B57\u4F53\u5927\u5C0F\uFF09",
        placeholder: "\u5168\u5C40\u5B57\u4F53\u5927\u5C0F"
      }
    },
    reset: {
      name: "\u91CD\u7F6E\u8BBE\u7F6E",
      confirm: "\u786E\u5B9A\u8981\u5C06\u8BBE\u7F6E\u91CD\u7F6E\u4E3A\u9ED8\u8BA4\u503C\u5417\uFF1F"
    },
    moreInfo: "\u66F4\u591A\u4FE1\u606F",
    author: "\u4F5C\u8005\uFF1A",
    license: "\u8BB8\u53EF\u8BC1\uFF1A",
    githubRepo: "Github\u4ED3\u5E93\uFF1A",
    anyQuestion: "\u6709\u4EFB\u4F55\u95EE\u9898\uFF1F\u8BF7\u5728\u8FD9\u91CC\u53CD\u9988"
  }
};

// src/i18n/index.ts
var translations = {
  en: en_default,
  zh: zh_default
};
var I18n = class {
  constructor() {
    this.currentLanguage = "en";
  }
  static getInstance() {
    if (!I18n.instance) {
      I18n.instance = new I18n();
    }
    return I18n.instance;
  }
  setLanguage(lang) {
    if (translations[lang]) {
      this.currentLanguage = lang;
    }
  }
  t(key) {
    const keys = key.split(".");
    let value = translations[this.currentLanguage];
    for (const k of keys) {
      if (value && value[k]) {
        value = value[k];
      } else {
        value = translations["en"];
        for (const fallbackKey of keys) {
          if (value && value[fallbackKey]) {
            value = value[fallbackKey];
          } else {
            return key;
          }
        }
      }
    }
    return typeof value === "string" ? value : key;
  }
};

// src/setting.ts
var DEFAULT_SETTINGS = {
  language: "en",
  showOnStatusBar: true,
  isAutoDetectHeaderLevel: false,
  // TODO: auto detect header level is not available now
  startHeaderLevel: 1,
  maxHeaderLevel: 6,
  isAutoNumbering: true,
  isUseYaml: false,
  autoNumberingStartNumber: "1",
  autoNumberingSeparator: ".",
  autoNumberingHeaderSeparator: "	",
  isSeparateTitleFont: true,
  titleFontFamily: "inherit",
  titleFontSize: "inherit"
};
var HeaderEnhancerSettingTab = class extends import_obsidian2.PluginSettingTab {
  constructor(app2, plugin) {
    super(app2, plugin);
    this.plugin = plugin;
  }
  display() {
    const { containerEl } = this;
    const i18n = I18n.getInstance();
    containerEl.empty();
    containerEl.createEl("h1", { text: i18n.t("settings.title") });
    containerEl.createEl("h2", { text: i18n.t("settings.general") });
    new import_obsidian2.Setting(containerEl).setName(i18n.t("settings.language.name")).setDesc(i18n.t("settings.language.desc")).addDropdown((dropdown) => {
      dropdown.addOption("en", "English");
      dropdown.addOption("zh", "\u4E2D\u6587");
      dropdown.setValue(this.plugin.settings.language);
      dropdown.onChange(async (value) => {
        this.plugin.settings.language = value;
        i18n.setLanguage(value);
        await this.plugin.saveSettings();
        this.display();
      });
    });
    new import_obsidian2.Setting(containerEl).setName(i18n.t("settings.statusBar.name")).setDesc(i18n.t("settings.statusBar.desc")).addToggle((toggle) => {
      toggle.setValue(this.plugin.settings.showOnStatusBar).onChange(async (value) => {
        this.plugin.settings.showOnStatusBar = value;
        await this.plugin.saveSettings();
        this.plugin.handleShowStateBarChange();
      });
    });
    containerEl.createEl("h2", { text: i18n.t("settings.autoNumbering.title") });
    new import_obsidian2.Setting(containerEl).setName(i18n.t("settings.autoNumbering.enable.name")).setDesc(i18n.t("settings.autoNumbering.enable.desc")).setDisabled(true).addToggle((toggle) => {
      toggle.setValue(this.plugin.settings.isAutoNumbering).onChange(async (value) => {
        new import_obsidian2.Notice(
          i18n.t("settings.autoNumbering.enable.notice")
        );
      });
    });
    new import_obsidian2.Setting(containerEl).setName(i18n.t("settings.autoNumbering.useYaml.name")).setDesc(i18n.t("settings.autoNumbering.useYaml.desc")).addToggle((toggle) => {
      toggle.setValue(this.plugin.settings.isUseYaml).onChange(async (value) => {
        this.plugin.settings.isUseYaml = value;
        await this.plugin.saveSettings();
        this.plugin.handleShowStateBarChange();
      });
    });
    new import_obsidian2.Setting(containerEl).setName(i18n.t("settings.autoNumbering.headerLevel.name")).setDesc(i18n.t("settings.autoNumbering.headerLevel.desc")).addToggle((toggle) => {
      toggle.setValue(this.plugin.settings.isAutoDetectHeaderLevel).onChange(async (value) => {
        this.plugin.settings.isAutoDetectHeaderLevel = value;
        await this.plugin.saveSettings();
        this.display();
      }).setDisabled(true);
    }).addDropdown((dropdown) => {
      dropdown.addOption("1", "H1");
      dropdown.addOption("2", "H2");
      dropdown.addOption("3", "H3");
      dropdown.addOption("4", "H4");
      dropdown.addOption("5", "H5");
      dropdown.addOption("6", "H6");
      dropdown.setValue(
        this.plugin.settings.startHeaderLevel.toString()
      );
      dropdown.setDisabled(this.plugin.settings.isAutoDetectHeaderLevel);
      dropdown.onChange(async (value) => {
        this.plugin.settings.startHeaderLevel = parseInt(value, 10);
        await this.plugin.saveSettings();
        this.display();
      });
    }).addDropdown((dropdown) => {
      dropdown.addOption("1", "H1");
      dropdown.addOption("2", "H2");
      dropdown.addOption("3", "H3");
      dropdown.addOption("4", "H4");
      dropdown.addOption("5", "H5");
      dropdown.addOption("6", "H6");
      dropdown.setValue(
        this.plugin.settings.maxHeaderLevel.toString()
      );
      dropdown.setDisabled(this.plugin.settings.isAutoDetectHeaderLevel);
      dropdown.onChange(async (value) => {
        if (this.checkMaxLevel(parseInt(value, 10))) {
          this.plugin.settings.maxHeaderLevel = parseInt(
            value,
            10
          );
          await this.plugin.saveSettings();
          this.display();
        } else {
          new import_obsidian2.Notice(
            "Max header level should be greater than or equal to start header level"
          );
        }
      });
    });
    new import_obsidian2.Setting(containerEl).setName(i18n.t("settings.autoNumbering.startNumber.name")).setDesc(i18n.t("settings.autoNumbering.startNumber.desc")).addText(
      (text) => text.setPlaceholder(i18n.t("settings.autoNumbering.startNumber.placeholder")).setValue(this.plugin.settings.autoNumberingStartNumber).onChange(async (value) => {
        if (this.checkStartNumber(value)) {
          this.plugin.settings.autoNumberingStartNumber = value;
          await this.plugin.saveSettings();
          this.display();
        }
      })
    );
    new import_obsidian2.Setting(containerEl).setName(i18n.t("settings.autoNumbering.separator.name")).setDesc(i18n.t("settings.autoNumbering.separator.desc")).addText(
      (text) => text.setPlaceholder(i18n.t("settings.autoNumbering.separator.placeholder")).setValue(this.plugin.settings.autoNumberingSeparator).onChange(async (value) => {
        if (this.checkSeparator(value)) {
          this.plugin.settings.autoNumberingSeparator = value;
          await this.plugin.saveSettings();
          this.display();
        }
      })
    );
    new import_obsidian2.Setting(containerEl).setName(i18n.t("settings.autoNumbering.headerSeparator.name")).setDesc(i18n.t("settings.autoNumbering.headerSeparator.desc")).addDropdown((dropdown) => {
      dropdown.addOption("	", "Tab");
      dropdown.addOption(" ", "Space");
      dropdown.setValue(
        this.plugin.settings.autoNumberingHeaderSeparator
      );
      dropdown.onChange(async (value) => {
        if (this.checkHeaderSeparator(value)) {
          this.plugin.settings.autoNumberingHeaderSeparator = value;
          await this.plugin.saveSettings();
        } else {
          new import_obsidian2.Notice(i18n.t("settings.autoNumbering.headerSeparator.error"));
        }
      });
    });
    new import_obsidian2.Setting(containerEl).setName(
      i18n.t("settings.autoNumbering.format.name") + ": 	" + this.plugin.settings.autoNumberingStartNumber + this.plugin.settings.autoNumberingSeparator + "1" + this.plugin.settings.autoNumberingSeparator + "1	" + i18n.t("settings.autoNumbering.format.fromLevel") + " " + this.plugin.settings.startHeaderLevel + " " + i18n.t("settings.autoNumbering.format.toLevel") + " " + this.plugin.settings.maxHeaderLevel + " " + (this.plugin.settings.isAutoDetectHeaderLevel ? i18n.t("settings.autoNumbering.format.autoDetect") : i18n.t("settings.autoNumbering.format.manual"))
    );
    containerEl.createEl("h2", { text: i18n.t("settings.font.title") });
    new import_obsidian2.Setting(containerEl).setName(i18n.t("settings.font.separate.name")).setDesc(i18n.t("settings.font.separate.desc")).addToggle((toggle) => {
      toggle.setValue(this.plugin.settings.isSeparateTitleFont).onChange(async (value) => {
        new import_obsidian2.Notice(i18n.t("settings.font.separate.notice"));
      });
    });
    new import_obsidian2.Setting(containerEl).setName(i18n.t("settings.font.family.name")).setDesc(i18n.t("settings.font.family.desc")).addText(
      (text) => text.setPlaceholder(i18n.t("settings.font.family.placeholder")).setValue(this.plugin.settings.titleFontFamily).onChange(async (value) => {
        this.plugin.settings.titleFontFamily = value;
        await this.plugin.saveSettings();
      })
    );
    new import_obsidian2.Setting(containerEl).setName(i18n.t("settings.font.size.name")).setDesc(i18n.t("settings.font.size.desc")).addText(
      (text) => text.setPlaceholder(i18n.t("settings.font.size.placeholder")).setValue(this.plugin.settings.titleFontSize).onChange(async (value) => {
        this.plugin.settings.titleFontSize = value;
        await this.plugin.saveSettings();
      })
    );
    new import_obsidian2.Setting(containerEl).addButton((button) => {
      button.setButtonText(i18n.t("settings.resetSettings.name")).onClick(async () => {
        if (confirm(
          i18n.t("settings.resetSettings.confirm")
        )) {
          this.plugin.settings = DEFAULT_SETTINGS;
          await this.plugin.saveSettings();
          this.display();
        }
      });
    });
    containerEl.createEl("h2", { text: i18n.t("settings.moreInfo") });
    containerEl.createEl("p", { text: i18n.t("settings.author") }).createEl("a", {
      text: "Hobee Liu",
      href: "https://github.com/HoBeedzc"
    });
    containerEl.createEl("p", { text: i18n.t("settings.license") }).createEl("a", {
      text: "MIT",
      href: "https://github.com/HoBeedzc/obsidian-header-enhancer-plugin/blob/master/LICENSE"
    });
    containerEl.createEl("p", { text: i18n.t("settings.githubRepo") }).createEl("a", {
      text: "obsidian-header-enhancer",
      href: "https://github.com/HoBeedzc/obsidian-header-enhancer-plugin"
    });
    containerEl.createEl("p", { text: i18n.t("settings.anyQuestion") }).createEl("a", {
      text: "Github Issues",
      href: "https://github.com/HoBeedzc/obsidian-header-enhancer-plugin/issues"
    });
  }
  checkMaxLevel(maxLevel) {
    return this.plugin.settings.startHeaderLevel <= maxLevel;
  }
  checkStartNumber(startNumber) {
    const reg = /^[0-9]*$/;
    return reg.test(startNumber);
  }
  checkSeparator(separator) {
    if (separator.length != 1) {
      return false;
    }
    const separators = [".", ",", "-", "/"];
    return separators.includes(separator);
  }
  checkHeaderSeparator(separator) {
    if (this.plugin.settings.isAutoNumbering) {
      return false;
    }
    return true;
  }
};

// src/config.ts
function getAutoNumberingConfig(setting, editor) {
  const config = {
    state: setting.isAutoNumbering,
    startLevel: setting.startHeaderLevel,
    maxLevel: setting.maxHeaderLevel,
    startNumber: parseInt(setting.autoNumberingStartNumber),
    separator: setting.autoNumberingSeparator
  };
  if (setting.isUseYaml) {
    const yaml = getAutoNumberingYaml(editor);
    if (yaml === "")
      return config;
    for (const item of yaml) {
      const [key, value] = item.split(" ");
      switch (key) {
        case "state":
          config.state = value == "on" ? true : false;
          break;
        case "start-level":
          config.startLevel = parseInt(value[1]);
          break;
        case "max-level":
          config.maxLevel = parseInt(value[1]);
          break;
        case "start-number":
          config.startNumber = parseInt(value);
          break;
        case "separator":
          config.separator = value;
          break;
      }
    }
  }
  return config;
}

// src/main.ts
var HeaderEnhancerPlugin = class extends import_obsidian3.Plugin {
  async onload() {
    await this.loadSettings();
    const ribbonIconEl = this.addRibbonIcon(
      "heading-glyph",
      "Header Enhancer",
      (evt) => {
        const app2 = this.app;
        const activeView = app2.workspace.getActiveViewOfType(import_obsidian3.MarkdownView);
        if (!activeView) {
          new import_obsidian3.Notice(
            "No active MarkdownView, cannot toggle auto numbering."
          );
          return;
        }
        if (this.settings.isAutoNumbering) {
          this.settings.isAutoNumbering = false;
          new import_obsidian3.Notice("Auto numbering is off");
          this.handleRemoveHeaderNumber(activeView);
        } else {
          this.settings.isAutoNumbering = true;
          new import_obsidian3.Notice("Auto numbering is on");
          this.handleAddHeaderNumber(activeView);
        }
        this.handleShowStateBarChange();
      }
    );
    this.statusBarItemEl = this.addStatusBarItem();
    this.handleShowStateBarChange();
    this.registerEditorExtension(
      import_state.Prec.highest(
        import_view.keymap.of([
          {
            key: "Enter",
            run: (view) => {
              const success = this.handlePressEnter(view);
              return success;
            }
          }
        ])
      )
    );
    this.registerEditorExtension(
      import_state.Prec.highest(
        import_view.keymap.of([
          {
            key: "Backspace",
            run: (view) => {
              const success = this.handlePressBackspace(view);
              return success;
            }
          }
        ])
      )
    );
    this.addCommand({
      id: "toggle-auto-numbering",
      name: "toggle auto numbering",
      callback: () => {
        const app2 = this.app;
        const activeView = app2.workspace.getActiveViewOfType(import_obsidian3.MarkdownView);
        if (!activeView) {
          new import_obsidian3.Notice(
            "No active MarkdownView, cannot toggle auto numbering."
          );
          return;
        }
        if (this.settings.isAutoNumbering) {
          this.settings.isAutoNumbering = false;
          new import_obsidian3.Notice("Auto numbering is off");
          this.handleRemoveHeaderNumber(activeView);
        } else {
          this.settings.isAutoNumbering = true;
          new import_obsidian3.Notice("Auto numbering is on");
          this.handleAddHeaderNumber(activeView);
        }
        this.handleShowStateBarChange();
      }
    });
    this.addCommand({
      id: "add-auto-numbering-yaml",
      name: "add auto numbering yaml",
      callback: () => {
        app = this.app;
        const activeView = app.workspace.getActiveViewOfType(import_obsidian3.MarkdownView);
        if (!activeView) {
          new import_obsidian3.Notice(
            "No active MarkdownView, cannot add auto numbering yaml."
          );
          return;
        } else {
          const editor = activeView.editor;
          const yaml = getAutoNumberingYaml(editor);
          if (yaml === "") {
            setAutoNumberingYaml(editor);
          } else {
            new import_obsidian3.Notice("auto numbering yaml already exists");
          }
        }
      }
    });
    this.addCommand({
      id: "reset-auto-numbering-yaml",
      name: "reset auto numbering yaml",
      callback: () => {
        app = this.app;
        const activeView = app.workspace.getActiveViewOfType(import_obsidian3.MarkdownView);
        if (!activeView) {
          new import_obsidian3.Notice(
            "No active MarkdownView, cannot reset auto numbering yaml."
          );
          return;
        } else {
          const editor = activeView.editor;
          const yaml = getAutoNumberingYaml(editor);
          if (yaml === "") {
            new import_obsidian3.Notice("auto numbering yaml not exists");
          } else {
            const value = [
              "state on",
              "first-level h2",
              "max 1",
              "start-at 1",
              "separator ."
            ];
            setAutoNumberingYaml(editor, value);
          }
        }
      }
    });
    this.addCommand({
      id: "remove-auto-numbering-yaml",
      name: "remove auto numbering yaml",
      callback: () => {
        app = this.app;
        const activeView = app.workspace.getActiveViewOfType(import_obsidian3.MarkdownView);
        if (!activeView) {
          new import_obsidian3.Notice(
            "No active MarkdownView, cannot remove auto numbering yaml."
          );
          return;
        } else {
          const editor = activeView.editor;
          const yaml = getAutoNumberingYaml(editor);
          if (yaml === "") {
            new import_obsidian3.Notice("auto numbering yaml not exists");
          } else {
            setAutoNumberingYaml(editor, []);
          }
        }
      }
    });
    this.addSettingTab(new HeaderEnhancerSettingTab(this.app, this));
    this.registerInterval(
      window.setInterval(() => console.log("setInterval"), 5 * 60 * 1e3)
    );
  }
  onunload() {
  }
  async loadSettings() {
    this.settings = Object.assign(
      {},
      DEFAULT_SETTINGS,
      await this.loadData()
    );
  }
  async saveSettings() {
    await this.saveData(this.settings);
  }
  handleShowStateBarChange() {
    if (this.settings.showOnStatusBar) {
      const autoNumberingStatus = this.settings.isAutoNumbering ? "On" : "Off";
      this.statusBarItemEl.setText(
        "Auto Numbering: " + autoNumberingStatus
      );
    } else {
      this.statusBarItemEl.setText("");
    }
  }
  handleAddHeaderNumber(view) {
    const editor = view.editor;
    const lineCount = editor.lineCount();
    let docCharCount = 0;
    const config = getAutoNumberingConfig(this.settings, editor);
    if (!this.settings.isAutoNumbering) {
      return false;
    }
    if (config.state) {
      let insertNumber = [Number(config.startNumber) - 1];
      let isCodeBlock = false;
      for (let i = 0; i <= lineCount; i++) {
        const line = editor.getLine(i);
        docCharCount += line.length;
        if (line.startsWith("```")) {
          isCodeBlock = !isCodeBlock;
          if (line.slice(3).contains("```")) {
            isCodeBlock = !isCodeBlock;
          }
        }
        if (isCodeBlock) {
          continue;
        }
        if (isHeader(line)) {
          const [headerLevel, realHeaderLevel] = getHeaderLevel(
            line,
            config.startLevel
          );
          if (headerLevel <= 0) {
            continue;
          }
          insertNumber = getNextNumber(insertNumber, headerLevel);
          const insertNumberStr = insertNumber.join(config.separator);
          if (isNeedInsertNumber(
            line,
            this.settings.autoNumberingHeaderSeparator
          )) {
            editor.setLine(
              i,
              "#".repeat(realHeaderLevel) + " " + insertNumberStr + this.settings.autoNumberingHeaderSeparator + line.substring(realHeaderLevel + 1)
            );
          } else if (isNeedUpdateNumber(
            insertNumberStr,
            line,
            this.settings.autoNumberingHeaderSeparator
          )) {
            const originNumberLength = line.split(
              this.settings.autoNumberingHeaderSeparator
            )[0].split(" ")[1].length;
            editor.setLine(
              i,
              "#".repeat(realHeaderLevel) + " " + insertNumberStr + line.substring(
                realHeaderLevel + originNumberLength + 1
              )
            );
          }
        }
      }
    }
    return true;
  }
  handleRemoveHeaderNumber(view) {
    const editor = view.editor;
    const lineCount = editor.lineCount();
    const config = getAutoNumberingConfig(this.settings, editor);
    if (!this.settings.isAutoNumbering) {
      for (let i = 0; i <= lineCount; i++) {
        const line = editor.getLine(i);
        if (isHeader(line)) {
          const [headerLevel, _] = getHeaderLevel(
            line,
            config.startLevel
          );
          if (headerLevel <= 0) {
            continue;
          }
          editor.setLine(
            i,
            removeHeaderNumber(
              line,
              this.settings.autoNumberingHeaderSeparator
            )
          );
        }
      }
    }
    return true;
  }
  handlePressEnter(view) {
    let state = view.state;
    let doc = state.doc;
    const pos = state.selection.main.to;
    const lineCount = doc.lines;
    const changes = [];
    let docCharCount = 0;
    let insertCharCount = 0;
    let insertCharCountBeforePos = 0;
    const app2 = this.app;
    const activeView = app2.workspace.getActiveViewOfType(import_obsidian3.MarkdownView);
    if (!activeView) {
      new import_obsidian3.Notice("No active MarkdownView, cannot toggle auto numbering.");
      return false;
    }
    if (!this.settings.isAutoNumbering) {
      return false;
    }
    const editor = activeView.editor;
    const config = getAutoNumberingConfig(this.settings, editor);
    if (!isHeader(doc.lineAt(pos).text)) {
      return false;
    }
    changes.push({
      from: pos,
      to: pos,
      insert: "\n"
    });
    if (config.state) {
      let insertNumber = [Number(config.startNumber) - 1];
      let isCodeBlock = false;
      for (let i = 1; i <= lineCount; i++) {
        const line = doc.line(i);
        const fromPos = line.from;
        docCharCount += line.length;
        if (line.text.startsWith("```")) {
          isCodeBlock = !isCodeBlock;
          if (line.text.slice(3).contains("```")) {
            isCodeBlock = !isCodeBlock;
          }
        }
        if (isCodeBlock) {
          continue;
        }
        if (isHeader(line.text)) {
          const [headerLevel, realHeaderLevel] = getHeaderLevel(
            line.text,
            config.startLevel
          );
          if (headerLevel <= 0) {
            continue;
          }
          insertNumber = getNextNumber(insertNumber, headerLevel);
          const insertNumberStr = insertNumber.join(config.separator);
          if (isNeedInsertNumber(
            line.text,
            this.settings.autoNumberingHeaderSeparator
          )) {
            if (docCharCount <= pos) {
              insertCharCountBeforePos += insertNumberStr.length + 1;
            }
            insertCharCount += insertNumberStr.length + 1;
            docCharCount += insertNumberStr.length + 1;
            changes.push({
              from: fromPos + realHeaderLevel + 1,
              to: fromPos + realHeaderLevel + 1,
              insert: insertNumberStr + this.settings.autoNumberingHeaderSeparator
            });
          } else if (isNeedUpdateNumber(
            insertNumberStr,
            line.text,
            this.settings.autoNumberingHeaderSeparator
          )) {
            const fromPos2 = line.from + realHeaderLevel + 1;
            const toPos = fromPos2 + line.text.split(
              this.settings.autoNumberingHeaderSeparator
            )[0].split(" ")[1].length;
            if (docCharCount <= pos) {
              insertCharCountBeforePos += insertNumberStr.length - toPos + fromPos2;
            }
            insertCharCount += insertNumberStr.length - toPos + fromPos2;
            docCharCount += insertNumberStr.length - toPos + fromPos2;
            changes.push({
              from: fromPos2,
              to: toPos,
              insert: insertNumberStr
            });
          }
        }
      }
    }
    view.dispatch({
      changes,
      selection: { anchor: pos + 1 + insertCharCountBeforePos },
      userEvent: "HeaderEnhancer.changeAutoNumbering"
    });
    return true;
  }
  handlePressBackspace(view) {
    let state = view.state;
    let doc = state.doc;
    const pos = state.selection.main.to;
    const lineCount = doc.lines;
    const changes = [];
    let docCharCount = 0;
    let insertCharCount = 0;
    let insertCharCountBeforePos = 0;
    if (!isHeader(doc.lineAt(pos).text)) {
      return false;
    }
    changes.push({
      from: pos - 1,
      to: pos,
      insert: ""
    });
    if (this.settings.isAutoNumbering) {
    }
    view.dispatch({
      changes,
      selection: { anchor: pos - 1 },
      userEvent: "HeaderEnhancer.changeAutoNumbering"
    });
    return true;
  }
};
