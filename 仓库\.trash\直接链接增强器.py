#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
直接链接增强器
直接为指定目录下的笔记增加链接，不管现有链接数
"""

import os
import re
from pathlib import Path
from datetime import datetime

def extract_links(content):
    """提取现有链接"""
    return re.findall(r'\[\[([^\]]+)\]\]', content)

def simple_keyword_match(text1, text2):
    """简单关键词匹配"""
    keywords = ['股权', '融资', '投资', '管理', '法律', '财务', '战略', '团队', '风险', '市场']
    
    score = 0
    for keyword in keywords:
        if keyword in text1 and keyword in text2:
            score += 1
    return score

def find_popular_notes(base_dir):
    """找到被链接最多的笔记"""
    all_links = []
    
    for file_path in base_dir.rglob("*.md"):
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            links = extract_links(content)
            all_links.extend(links)
        except:
            continue
    
    # 统计链接频率
    link_count = {}
    for link in all_links:
        link_count[link] = link_count.get(link, 0) + 1
    
    # 返回前30个热门笔记
    popular = sorted(link_count.items(), key=lambda x: x[1], reverse=True)[:30]
    return [link[0] for link in popular]

def add_simple_links(file_path, popular_notes):
    """为单个笔记添加简单链接"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
    except:
        return False
    
    title = file_path.stem
    existing_links = extract_links(content)
    
    # 如果已经有很多链接，跳过
    if len(existing_links) >= 5:
        return False
    
    # 找到相关的热门笔记
    related_links = []
    full_text = f"{title} {content}".lower()
    
    for popular_note in popular_notes:
        # 如果已经有这个链接，跳过
        if popular_note in existing_links:
            continue
        
        # 简单匹配
        if (popular_note.lower() in full_text or 
            simple_keyword_match(full_text, popular_note.lower()) > 0):
            related_links.append(popular_note)
        
        if len(related_links) >= 3:  # 最多添加3个
            break
    
    # 如果没找到相关的，随机添加一些热门笔记
    if len(related_links) == 0:
        for popular_note in popular_notes[:5]:
            if popular_note not in existing_links:
                related_links.append(popular_note)
                if len(related_links) >= 2:
                    break
    
    # 添加链接
    if related_links:
        if '## 相关笔记' not in content:
            content += f"\n\n---\n\n## 相关笔记\n"
            for link in related_links:
                content += f"- [[{link}]]\n"
        
        try:
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(content)
            return True
        except:
            return False
    
    return False

def process_directory(target_dir):
    """处理指定目录"""
    base_dir = Path(r"c:\Users\<USER>\OneDrive\obsidian笔记系统")
    target_path = base_dir / target_dir
    
    if not target_path.exists():
        print(f"目录不存在: {target_path}")
        return
    
    print(f"处理目录: {target_path}")
    
    # 获取热门笔记
    print("分析热门笔记...")
    popular_notes = find_popular_notes(base_dir)
    print(f"找到 {len(popular_notes)} 个热门笔记")
    
    # 处理目录下的所有笔记
    md_files = list(target_path.rglob("*.md"))
    print(f"找到 {len(md_files)} 个笔记文件")
    
    success_count = 0
    for i, file_path in enumerate(md_files):
        if i % 20 == 0:
            print(f"进度: {i+1}/{len(md_files)}")
        
        if add_simple_links(file_path, popular_notes):
            success_count += 1
    
    print(f"\n完成！成功为 {success_count} 个笔记添加了链接")

def main():
    """主函数"""
    print("直接链接增强器")
    print("=" * 30)
    
    print("选择要处理的目录:")
    print("1. 战略与战术")
    print("2. 整个系统")
    print("3. 自定义目录")
    
    choice = input("请选择 (1/2/3): ").strip()
    
    if choice == "1":
        process_directory("战略与战术")
    elif choice == "2":
        base_dir = Path(r"c:\Users\<USER>\OneDrive\obsidian笔记系统")
        
        # 获取热门笔记
        print("分析热门笔记...")
        popular_notes = find_popular_notes(base_dir)
        print(f"找到 {len(popular_notes)} 个热门笔记")
        
        # 处理所有笔记
        md_files = list(base_dir.rglob("*.md"))
        # 排除系统文件
        md_files = [f for f in md_files if '.obsidian' not in str(f) and '.trash' not in str(f)]
        
        print(f"找到 {len(md_files)} 个笔记文件")
        
        success_count = 0
        for i, file_path in enumerate(md_files):
            if i % 50 == 0:
                print(f"进度: {i+1}/{len(md_files)}")
            
            if add_simple_links(file_path, popular_notes):
                success_count += 1
        
        print(f"\n完成！成功为 {success_count} 个笔记添加了链接")
    
    elif choice == "3":
        custom_dir = input("请输入目录名（相对于obsidian笔记系统）: ").strip()
        process_directory(custom_dir)

if __name__ == "__main__":
    main()
