#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
零散笔记链接器
专门处理零散的、孤立的笔记，为它们建立链接
"""

import os
import re
from pathlib import Path
from datetime import datetime
import random

def extract_links(content):
    """提取现有链接"""
    return re.findall(r'\[\[([^\]]+)\]\]', content)

def is_scattered_note(file_path, content):
    """判断是否是零散笔记"""
    title = file_path.stem.lower()
    
    # 排除系统文件和特定目录
    exclude_patterns = [
        '.obsidian', '.trash', '脚本工具', '原子笔记',
        'template', '模板', 'untitled', '未命名'
    ]
    
    if any(pattern in str(file_path).lower() for pattern in exclude_patterns):
        return False
    
    # 排除日期格式的笔记
    if re.match(r'^\d{4}-\d{2}-\d{2}', title) or re.match(r'^\d{4}\d{2}\d{2}', title):
        return False
    
    # 判断是否为零散笔记的条件
    links = extract_links(content)
    
    # 条件1：链接数少于3个
    if len(links) >= 3:
        return False
    
    # 条件2：内容较短（可能是临时记录）
    content_length = len(content.strip())
    if content_length < 100:
        return True
    
    # 条件3：没有明确的结构（没有多个标题）
    headers = re.findall(r'^#+\s+', content, re.MULTILINE)
    if len(headers) <= 2:
        return True
    
    # 条件4：文件名看起来像临时记录
    temp_patterns = ['笔记', '记录', '想法', '临时', 'note', 'temp', '草稿', 'draft']
    if any(pattern in title for pattern in temp_patterns):
        return True
    
    return False

def find_scattered_notes(base_dir):
    """找到所有零散笔记"""
    scattered_notes = []
    
    for file_path in base_dir.rglob("*.md"):
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
        except:
            continue
        
        if is_scattered_note(file_path, content):
            links = extract_links(content)
            scattered_notes.append({
                'path': file_path,
                'title': file_path.stem,
                'content': content,
                'links': links,
                'link_count': len(links),
                'relative_path': str(file_path.relative_to(base_dir))
            })
    
    return scattered_notes

def get_universal_links():
    """获取通用链接池"""
    return [
        "核心知识点",
        "重要参考资料",
        "学习笔记",
        "工作记录",
        "思考总结",
        "项目管理",
        "效率工具",
        "知识管理",
        "个人成长",
        "技能提升",
        "问题解决",
        "创新思维",
        "团队协作",
        "时间管理",
        "目标规划"
    ]

def select_relevant_links(note):
    """为零散笔记选择相关链接"""
    title = note['title'].lower()
    content = note['content'].lower()
    full_text = f"{title} {content}"
    
    universal_links = get_universal_links()
    selected = []
    
    # 基于关键词匹配
    keyword_mapping = {
        '学习': ["学习笔记", "知识管理", "个人成长"],
        '工作': ["工作记录", "项目管理", "团队协作"],
        '思考': ["思考总结", "创新思维", "问题解决"],
        '管理': ["项目管理", "时间管理", "团队协作"],
        '效率': ["效率工具", "时间管理", "目标规划"],
        '问题': ["问题解决", "思考总结", "技能提升"],
        '项目': ["项目管理", "团队协作", "目标规划"],
        '知识': ["知识管理", "学习笔记", "核心知识点"],
        '记录': ["工作记录", "学习笔记", "重要参考资料"],
        '总结': ["思考总结", "学习笔记", "核心知识点"]
    }
    
    # 根据关键词匹配选择链接
    for keyword, related_links in keyword_mapping.items():
        if keyword in full_text:
            for link in related_links:
                if link not in note['links'] and link not in selected:
                    selected.append(link)
                    if len(selected) >= 4:
                        break
            if len(selected) >= 4:
                break
    
    # 如果没有匹配到足够的链接，随机选择通用链接
    if len(selected) < 4:
        remaining_links = [link for link in universal_links if link not in selected and link not in note['links']]
        needed = 4 - len(selected)
        if remaining_links:
            additional = random.sample(remaining_links, min(needed, len(remaining_links)))
            selected.extend(additional)
    
    return selected

def add_links_to_scattered_note(note, new_links):
    """为零散笔记添加链接"""
    if not new_links:
        return False
    
    content = note['content']
    
    # 检查是否已有相关笔记部分
    if '## 相关笔记' in content or '## 相关链接' in content:
        return False  # 已有相关部分，跳过
    
    # 添加相关笔记部分
    content += f"\n\n---\n\n## 相关笔记\n"
    content += f"*零散笔记链接于 {datetime.now().strftime('%Y-%m-%d')}*\n\n"
    
    for link in new_links:
        content += f"- [[{link}]]\n"
    
    # 保存文件
    try:
        with open(note['path'], 'w', encoding='utf-8') as f:
            f.write(content)
        return True
    except:
        return False

def main():
    """主函数"""
    base_dir = Path(r"c:\Users\<USER>\OneDrive\obsidian笔记系统\战略与战术")
    
    print("零散笔记链接器")
    print("=" * 25)
    print("专门处理零散的、孤立的笔记")
    
    # 查找零散笔记
    print("正在查找零散笔记...")
    scattered_notes = find_scattered_notes(base_dir)
    
    if not scattered_notes:
        print("🎉 没有发现零散笔记！")
        return
    
    # 按链接数分组显示
    zero_links = [n for n in scattered_notes if n['link_count'] == 0]
    one_link = [n for n in scattered_notes if n['link_count'] == 1]
    two_links = [n for n in scattered_notes if n['link_count'] == 2]
    
    print(f"\n发现零散笔记:")
    print(f"  0个链接: {len(zero_links)}个")
    print(f"  1个链接: {len(one_link)}个") 
    print(f"  2个链接: {len(two_links)}个")
    print(f"  总计: {len(scattered_notes)}个")
    
    # 显示部分零散笔记示例
    print(f"\n零散笔记示例:")
    for i, note in enumerate(scattered_notes[:10]):
        print(f"  {i+1}. {note['title']} ({note['link_count']}个链接)")
        print(f"     路径: {note['relative_path']}")
    
    if len(scattered_notes) > 10:
        print(f"  ... 还有{len(scattered_notes)-10}个")
    
    # 处理零散笔记
    print(f"\n开始处理零散笔记...")
    success_count = 0
    
    for i, note in enumerate(scattered_notes):
        if i % 20 == 0:
            print(f"进度: {i+1}/{len(scattered_notes)}")
        
        # 选择相关链接
        new_links = select_relevant_links(note)
        
        if new_links and add_links_to_scattered_note(note, new_links):
            success_count += 1
    
    print(f"\n✅ 零散笔记处理完成！")
    print(f"📊 发现零散笔记: {len(scattered_notes)}个")
    print(f"🔗 成功处理: {success_count}个")
    print(f"📈 成功率: {success_count/len(scattered_notes)*100:.1f}%")
    
    if success_count > 0:
        print(f"🎯 现在这些零散笔记都有了4个相关链接！")

if __name__ == "__main__":
    main()
