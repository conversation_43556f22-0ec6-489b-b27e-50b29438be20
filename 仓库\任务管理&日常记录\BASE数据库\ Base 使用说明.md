---
account:
---
# Base 使用说明
视图 视图允许您以多种不同的方式组织 Base 中的信息。一个 base 可以包含多个视图，并且每个视图都可以具有唯一的配置来显示、排序和筛选文件。 例如，您可能希望创建一个名为“Books”的基库，该库具有“Reading list”和“Recently finished”的单独视图。 布局 目前，基础可以显示为表格或卡片。将来将添加更多布局类型。请参阅 Bases 路线图。 当前布局选项： 表布局将每个文件显示为表中的一行。列是从注释中的 Properties 填充的。 Cards 布局将每个文件显示为网格中的卡片。视图设置允许您选择性地配置图像属性，该属性可以是图像 URL 或附件链接。 每种布局类型都提供自己的配置选项和作。要查看视图的配置选项，请执行以下作： 单击 bases 工具栏左上角的视图名称。 单击当前视图旁边的向右箭头。 选择 Configure view （配置视图）。 桌子 Table 没有任何特定的视图配置。 卡 Image 属性 卡片支持可选的封面图像，该图像是在卡片顶部显示为图像的属性。该属性可以是以下任何一项： 指向本地附件的链接"[[link/to/attachment.jpg]]" 外部链接 （URL） 十六进制颜色代码 （#000000) 图像拟合 如果您配置了 image 属性，则此选项将确定图像在卡中的显示方式。 盖：图像将填充卡片的内容框。如果不适合，图像将被裁剪。 包含：图像将缩放，直到它适合卡片的内容框。图像不会被裁剪。 图像长宽比 封面图像的大小取决于其长宽比。默认纵横比为 1：1，这意味着您的所有图像都将是正方形。调整此选项可使图像更宽或更高。 过滤 器 不带过滤器的 base 显示 Vault 中的所有文件。过滤器允许您缩小结果范围，仅显示满足特定条件的文件。例如，您可以使用过滤器来仅显示具有特定标签或特定文件夹中的文件。有许多过滤器类型可供选择。 单击基础顶部的 Filters 按钮以添加过滤器。 过滤器可以应用于基础中的所有视图，也可以通过从 Filters （过滤器） 菜单中的两个部分中进行选择来仅应用于单个视图。 所有视图将筛选器应用于基础中的所有视图。 此视图将筛选器应用于活动视图。 属性、运算符、值 筛选器有三个组件： Property — 用于在 Vault 中选择属性，包括隐式属性 运算符 - 用于选择如何比较条件。可用运算符的列表取决于属性类型（文本、日期、数字等） Value （值） - 用于选择要比较的值。值可以包括 math 和 functions。 连词 All the following are true 是一个语句 — 仅当满足筛选条件组中的所有条件时，才会显示结果。and 以下任何一项为 true 都是一个语句 — 如果满足筛选条件组中的任何条件，则将显示结果。or 以下任何一项都不为 true 是一个语句 — 如果满足筛选条件组中的任何条件，则不会显示结果。not 筛选器组 Filter groups 允许您通过在 junction 上创建组合来创建更复杂的 logic。 高级过滤器编辑器 单击代码按钮lucide-code-xml.svg > icon以使用高级过滤器编辑器。这将显示筛选器的原始语法，并且可以与无法使用点击界面显示的更复杂的函数一起使用。 限制、复制和导出结果 结果菜单显示视图中的结果数。单击结果按钮以限制结果数量，并访问其他作。 复制到剪贴板 此作会将视图复制到剪贴板。进入剪贴板后，您可以将其粘贴到 Markdown 文件或其他文档应用程序中，包括 Google 表格、Excel 和 Numbers 等电子表格。 导出 CSV 此作将保存当前视图的 CSV。 链接到此页面

以下是关于Notion中视图功能的整理摘要，以清晰的结构呈现：

---

### **视图概述**

- **作用**：以不同方式组织Base中的信息，支持多视图配置（显示/排序/筛选）。
    
- **示例**：在“Books”Base中创建“Reading list”和“Recently finished”视图。
    

---

### **布局类型**

1. **表格布局（Table）**
    
    - 文件显示为行，列由属性填充。
        
    - **配置**：无特定选项。
        
2. **卡片布局（Cards）**
    
    - 文件显示为网格卡片，支持封面图像。
        
    - **配置选项**：
        
        - **图像属性**：支持本地附件链接、URL或颜色代码（如`#000000`）。
            
        - **图像拟合**：
            
            - `Cover`：填充并裁剪图像。
                
            - `Contain`：缩放图像以适应（不裁剪）。
                
        - **图像纵横比**：默认1:1（正方形），可调整比例。
            

---

### **过滤器功能**

- **作用**：筛选满足条件的文件（如按标签、文件夹等）。
    
- **组件**：
    
    1. **属性**：选择文件属性（包括隐式属性）。
        
    2. **运算符**：根据属性类型（文本/日期/数字）选择比较方式。
        
    3. **值**：支持数学和函数表达式。
        
- **逻辑连接词**：
    
    - **AND**
        

reate a base Bases 可让您将任何一组笔记变成一个强大的数据库。以下是创建基础并将其嵌入到笔记中的方法。每个基地都可以有一个或多个视图，以不同的方式显示信息。 创建新基地 命令面板： 打开命令面板。 选择 基础：创建新基础以在与活动文件相同的文件夹中创建基础。 底座：插入新底座以创建底座并将其嵌入到当前文件中。 文件资源管理器： 在文件资源管理器中，右键单击要在其中创建基础的文件夹。 选择“新建基地”。 嵌入底座 嵌入基本文件 您可以使用语法将基本文件嵌入到任何其他文件中。要指定默认视图，请使用 。![[File.base]]![[File.base#View]] 将基础嵌入为代码块 Bases 还可以使用代码块和 bases 语法直接嵌入到笔记中。base ```base filters: and: - file.hasTag("example") views: - type: table name: Table ``` 链接到此页面 基本语法 基地简介

### **如何在 Obsidian 中创建和嵌入 Bases（数据库）**

Bases（数据库）允许你将笔记转换成结构化数据，支持多种视图（表格、卡片等）和筛选功能。以下是创建和嵌入 Bases 的方法：

---

## **1. 创建新 Base**

### **方法 1：使用命令面板**

1. 按 `Ctrl/Cmd + P` 打开命令面板。
    
2. 输入并选择以下选项之一：
    
    - **`Base: Create new base`** → 在 **当前文件夹** 创建新 Base 文件（`.base` 扩展名）。
        
    - **`Base: Insert new base`** → 在当前笔记中 **直接嵌入一个新 Base**。
        

### **方法 2：文件管理器右键创建**

1. 在文件管理器（左侧边栏）中，**右键点击目标文件夹**。
    
2. 选择 **“New Base”** 创建新的 Base 文件。
    

---

## **2. 嵌入 Base 到笔记**

### **方法 1：嵌入整个 Base 文件**

功能 函数在 Bases 中用于作过滤器和公式中属性中的数据。请参阅 bases 语法参考，了解有关如何使用函数的更多信息。 除了全局函数之外，大多数函数都取决于要修改的值类型： 任何 日期 字符串 数 列表 链接 文件 对象 正则表达式 全球 全局函数在没有类型的情况下使用。 date() date(date: string): date date(string): date解析提供的字符串并返回一个日期对象。 字符串的格式应为 .dateYYYY-MM-DD HH:mm:ss if() if(condition: any, trueResult: any, falseResult?: any): any condition是要评估的条件。 trueResult是 condition 为 true 时的输出。 falseResult如果条件为 false，则为可选输出。如果未给出，则假定为 。null 返回 if 为 true，或者为 truey 值，否则返回。trueResultconditionfalseResult 例：if(isModified, "Modified", "Unmodified") image() image(path: string | file | url): image 返回一个 image 对象，该对象将在视图中呈现图像。 示例：或image(image-property)image("https://obsidian.md/images/obsidian-logo-gradient.svg") icon() icon(name: string): icon 返回一个值，该值将在视图中呈现为图标。图标名称必须与支持的 Lucide 图标匹配。 例：。icon("arrow-right") max() max(value1: number, value2: number...): number 返回所有提供的数字中最大的一个。 min() min(value1: number, value2: number...): number 返回所有提供的数字中的最小值。 link() link(path: string | file, display?: value): Link 解析字符串并返回一个 Link 对象，该对象呈现为指向给定路径的链接。path （可选）提供参数以更改链接显示的文本。display list() list(element: any): List 如果提供的元素是列表，则返回它而不加修改。 否则，将提供的包装在列表中，创建具有单个元素的列表。element 当属性在整个 Vault 中包含字符串或列表的混合时，此函数非常有用。 示例：返回 .list("value")["value"] now() now(): date now()返回表示当前时刻的 Date 对象。 number() number(input: any): number 尝试以数字形式返回提供的值。 Date 对象将以自 unix 纪元以来的毫秒数返回。 布尔值将返回 1 或 0。 字符串将被解析为数字，如果结果无效，则返回错误。 Example，返回 。number("3.4")3.4 duration() duration(value: string): duration 将字符串解析为 duration。有关字符串格式，请参阅日期算术部分。value 在执行日期算术时不需要显式解析 durations（例如，），但在对 durations 执行算术时（例如 ）。now() + '1d'now() + (duration('1d') * 2) When performing arithmetic on durations with scalars, the duration must be on the left. For example , instead of .duration('5h') * 22 * duration('5h') today() today(): date today() returns a date object representing the current date. The time portion is set to zero. date() date(input: string | date): date Returns a date object representing the parsed input timestamp or date object. Any Functions you can use with any value. This includes strings (e.g. ), numbers (e.g. ), lists (e.g. ), objects, and more."hello"42[1,2,3] toString() any.toString(): string Returns the string representation of any value. Example: returns .123.toString()"123" isTruthy() any.isTruthy(): boolean Return the value coerced into a boolean. Example: returns .1.isTruthy()true Date Functions you can use with a date and time such as . Date comparisons can be done using date arithmetic.date("2025-05-27") Fields The following fields are available for dates: Field Type Description date.year number The year of the date date.month number The month of the date (1–12) date.day number The day of the month date.hour number The hour (0–23) date.minute number The minute (0–59) date.second number The second (0–59) date.millisecond number The millisecond (0–999) date() date.date(): date 返回删除时间的日期对象。 示例：返回字符串，例如“2025-12-31 00：00：00”now().date().format("YYYY-MM-DD HH:mm:ss" format() date.format(format: string): string format是格式字符串（例如，）。"YYYY-MM-DD" 返回由Moment.js格式字符串指定格式化的日期。 示例：返回 .date.format("YYYY-MM-DD")"2025-05-27" time() date.time(): string 返回时间。 示例：返回字符串，例如“23：59：59”now().time() relative() date.relative(): string 返回日期与当前日期时间的可读比较。 示例：返回一个值，例如 .file.mtime.relative()3 days ago isEmpty() date.isEmpty(): boolean 返回 false。 字符串 可与一系列字符一起使用的函数，例如"hello". 领域 田 类型 描述 string.length number 字符串中的字符数 contains() string.contains(value: string): boolean value是要搜索的子字符串。 如果字符串包含 ，则返回 true 。value 示例：返回 ."hello".contains("ell")true containsAll() string.containsAll(...values: string): boolean values是一个或多个要搜索的子字符串。 如果字符串包含所有 .values 示例：返回 ."hello".containsAll("h", "e")true containsAny() string.containsAny(...values: string): boolean values是一个或多个要搜索的子字符串。 如果字符串至少包含一个 .values 示例：返回 ."hello".containsAny("x", "y", "e")true endsWith() string.endsWith(query: string): boolean query是末尾要检查的字符串。 如果此字符串以 结尾，则返回 true 。query 示例：返回 ."hello".endsWith("lo")true isEmpty() string.isEmpty(): boolean 如果字符串没有字符或不存在，则返回 true。 示例：返回 ."Hello world".isEmpty()false Example: returns ."".isEmpty()true replace() string.replace(pattern: string | Regexp, replacement: string): string pattern is the value to search for in the target string. replacement is the value to replace found patterns with. If is a string, all occurrences of the pattern will be replaced.pattern If is a Regexp, the flag determines if only the first or if all occurrences are replaced.patterng Example: returns , where as returns ."a,b,c,d".replace(/,/, "-")"a-b,c,d""a,b,c,d".replace(/,/g, "-")"a-b-c-d" lower() string.lower(): string Returns the string converted to lower case. reverse() string.reverse(): string Reverses the string. Example: returns ."hello".reverse()"olleh" slice() string.slice(start: number, end?: number): string start is the inclusive start index. end is the optional exclusive end index. Returns a substring from (inclusive) to (exclusive).startend Example: returns ."hello".slice(1, 4)"ell" If is omitted, slices to the end of the string.end split() string.split(separator: string | Regexp, n?: number): list separator is the delimiter for splitting the string. n is an optional number. If provided, the result will have the first elements.n Returns an list of substrings. Example: or returns ."a,b,c,d".split(",", 3)"a,b,c,d".split(/,/, 3)["a", "b", "c"] startsWith() string.startsWith(query: string): boolean query is the string to check at the beginning. Returns true if this string starts with .query Example: returns ."hello".startsWith("he")true title() string.title(): string Converts the string to title case (first letter of each word capitalized). Example: returns ."hello world".title()"Hello World" trim() string.trim(): string Removes whitespace from both ends of the string. Example: returns ." hi ".trim()"hi" Number Functions you can use with numeric values such as , .423.14 abs() number.abs(): number Returns the absolute value of the number. Example: returns .(-5).abs()5 ceil() number.ceil(): number Rounds the number up to the nearest integer. Example: returns .(2.1).ceil()3 floor() number.floor(): number Rounds the number down to the nearest integer. Example: returns .(2.9).floor()2 round() number.round(digits: number): number Rounds the number to the nearest integer. Optionally, provided a parameter to round to that number of decimal digits.digits Example: returns , and returns .(2.5).round()3(2.3333).round(2)2.33 toFixed() number.toFixed(precision: number): string precision is the number of decimal places. Returns a string with the number in fixed-point notation. Example: returns .(3.14159).toFixed(2)"3.14" isEmpty() number.isEmpty(): boolean Returns true if the number is not present. Example: returns .5.isEmpty()false List Functions you can use with an ordered list of elements such as .[1, 2, 3] Fields Field Type Description list.length number The number of elements in the list contains() list.contains(value: any): boolean value是要搜索的元素。 如果列表包含 ，则返回 true。value 示例：返回 .[1,2,3].contains(2)true containsAll() list.containsAll(...values: any): boolean values是要搜索的一个或多个元素。 如果列表包含所有 .values 示例：返回 .[1,2,3].containsAll(2,3)true containsAny() list.containsAny(...values: any): boolean values是要搜索的一个或多个元素。 如果列表至少包含一个 .values 示例：返回 .[1,2,3].containsAny(3,4)true isEmpty() list.isEmpty(): boolean 如果列表没有元素，则返回 true。 示例：返回 .[1,2,3].isEmpty()false join() list.join(separator: string): string separator是要在元素之间插入的字符串。 将所有列表元素联接到一个字符串中。 示例：返回 .[1,2,3].join(",")"1,2,3" reverse() list.reverse(): list 就地反转列表。 示例：返回 .[1,2,3].reverse()[3,2,1] sort() list.sort(): list 按从小到大的顺序对列表元素进行排序。 示例：返回 .[3, 1, 2].sort()[1, 2, 3] 示例：返回 .["c", "a", "b"].sort()["a", "b", "c"] flat() list.flat(): list 将嵌套列表展平为单个列表。 示例：返回 .[1,[2,3]].flat()[1,2,3] unique() list.unique(): list 删除重复的元素。 示例：返回 .[1,2,2,3].unique()[1,2,3] slice() list.slice(start: number, end?: number): list start是非独占起始索引。 end是可选的独占结束索引。 返回列表的一部分从 （含） 到 （不包括） 的浅表副本。startend 示例：返回 .[1,2,3,4].slice(1,3)[2,3] 如果省略，则切片到列表的末尾。end map() list.map(value: Any): list 通过调用转换函数来转换此列表的每个元素，该函数使用变量 和 ，并返回要放置在列表中的新值。indexvalue value is the value of an item in the list. index is the index of the current value. Example: returns .[1,2,3,4].map(value + 1)[2,3,4,5] filter() list.filter(value: Boolean): list Filter the elements of this list by calling a filter function, which uses the variables and , and returns a boolean value for whether the element should be kept.indexvalue value is the value of an item in the list. index is the index of the current value. Example: returns .[1,2,3,4].filter(value > 2)[3,4] Link Functions you can use on a link. Links can be created from a file () or a path ().file.asLink()link("path") linksTo() link.linksTo(file): boolean Returns whether the file represented by the has a link to .linkfile File Functions you can use with file in the vault. asLink() file.asLink(display?: string): Link display optional display text for the link. Returns a Link object that renders as a functioning link. Example: file.asLink() hasLink() file.hasLink(otherFile: file | string): boolean otherFile is another file object or string path to check. Returns true if links to .fileotherFile Example: returns if there’s a link from to .file.hasLink(otherFile)truefileotherFile hasTag() file.hasTag(...values: string): boolean values are one or more tag names. Returns true if the file has any of the tags in .values Example: returns if the file has either tag.file.hasTag("tag1", "tag2")true inFolder() file.inFolder(folder: string): boolean folder is the folder name to check. Returns true if the file is in the specified folder. 示例：返回 .file.inFolder("notes")true 对象 可以与键值对集合一起使用的函数，例如 .{"a": 1, "b": 2} isEmpty() object.isEmpty(): boolean 如果对象没有自己的属性，则返回 true。 示例：返回 .{}.isEmpty()true keys() object.keys(): list 返回包含对象键的列表。 values() object.values(): list 返回包含对象值的列表。 正则表达式 可以与正则表达式模式一起使用的函数。例：。/abc/ matches() regexp.matches(value: string): boolean value是要测试的字符串。 如果正则表达式匹配，则返回 true 。value 示例：返回 ./abc/.matches("abcde")true 链接到此页面

以下是 **Obsidian Bases 函数参考手册** 的整理，按功能分类，便于快速查阅：

---

### **全局函数（适用于所有类型）**

|函数|语法|说明|示例|
|---|---|---|---|
|`if()`|`if(条件, 真值, 假值?)`|条件判断，返回真值或假值|`if(score > 60, "及格", "不及格")`|
|`now()`|`now()`|返回当前时间（含日期和时间）|`now().format("YYYY-MM-DD")`|
|`today()`|`today()`|返回当前日期（时间部分为00:00:00）|`today().relative()` → "Today"|
|`number()`|`number(输入)`|将输入转为数字（支持布尔值、日期、字符串）|`number("3.14")` → `3.14`|
|`date()`|`date(字符串/日期)`|解析字符串为日期对象|`date("2025-05-27")`|
|`list()`|`list(元素)`|将元素包装为列表|`list("A")` → `["A"]`|
|`image()`|`image(|

基本语法 正在进行的工作 Bases 仍处于测试阶段，语法正在根据我们在抢先体验阶段收到的反馈而不断发展。预计语法会发生变化。 在 Obsidian 中创建底座时，它会保存为文件。基础通常使用应用程序界面进行编辑，但语法也可以手动编辑，并嵌入到代码块中。.base Bases 语法定义视图、筛选器和公式。基数必须是符合下面定义的架构的有效 YAML。 例 下面是基本文件的示例。我们将详细介绍每个部分。 filters: or: - file.hasTag("tag") - and: - file.hasTag("book") - file.hasLink("Textbook") - not: - file.hasTag("book") - file.inFolder("Required Reading") formulas: formatted_price: 'if(price, price.toFixed(2) + " dollars")' ppu: "(price / age).toFixed(2)" properties: status: displayName: Status formula.formatted_price: displayName: "Price" file.ext: displayName: Extension views: - type: table name: "My table" limit: 10 filters: and: - 'status != "done"' - or: - "formula.ppu > 5" - "price > 2.1" group_by: "status" order: - file.name - file.ext - note.age - formula.ppu - formula.formatted_price - type: map name: "Example map" filters: "has_coords == true" lat: lat long: long title: file.name 过滤 器 默认情况下，基础包括 Vault 中的每个文件。SQL 或 Dataview 中没有 or like。该部分允许您定义条件以缩小数据集范围。fromsourcefilters filters: or: - file.hasTag("tag") - and: - file.hasTag("book") - file.hasLink("Textbook") - not: - file.hasTag("book") - file.inFolder("Required Reading") 有两种机会可以应用过滤器： 在全局级别（如上所示），它们适用于基中的所有视图。filters 在仅适用于特定视图的级别。view 这两个部分在功能上是等效的，在评估视图时，它们将与 .AND 该部分包含作为字符串的单个过滤器语句，或递归定义的过滤器对象。筛选器对象可以包含 、 或 之一。这些键是字符串中其他筛选器对象或筛选器语句的异构列表。过滤语句是应用于音符时计算为真或假的行。它可以是以下之一：filtersandornot 使用标准算术运算符的基本比较。 一个函数。内置了多种功能，插件可以添加附加功能。 筛选器和公式的语法和可用函数相同。 公式 该部分定义可在基本文件的所有视图中显示的公式属性。formulas formulas: formatted_price: 'if(price, price.toFixed(2) + " dollars")' ppu: "(price / age).toFixed(2)" 公式属性支持基本算术运算符和各种内置函数。将来，插件将能够添加用于公式的函数。 公式中的属性可以通过多种方式引用，具体取决于属性的类型： frontmatter 中的属性称为 properties。例如 或 .如果属性没有前缀，则假定它是属性。notenote.pricenote["price"]note 有关文件的属性（隐式属性）称为属性。例如，或 .您还可以使用 来引用文件本身，例如 .filefile.sizefile.extfilefile.hasLink() 公式的前缀为 ，例如 。formulaformula.formatted_price 公式属性可以使用其他公式属性中的值，只要没有循环引用即可。它们在 YAML 中始终定义为字符串，但是数据的数据类型和函数返回将用于确定输出数据类型。 请注意，在 YAML 字段中包含文本文本所需的嵌套引号的使用。文本文本必须用单引号或双引号括起来。 性能 该部分允许存储有关每个属性的配置信息。如何使用这些配置值取决于各个视图。例如，在表中，显示名称用于列标题。properties properties: status: displayName: Status formula.formatted_price: displayName: "Price" file.ext: displayName: Extension 显示名称不用于筛选器或公式。 视图 该部分定义如何呈现数据。列表中的每个条目都定义了相同数据的单独视图，并且可以根据需要有任意数量的不同视图。viewsviews views: - type: table name: "My table" limit: 10 filters: and: - 'status != "done"' - or: - "formula.ppu > 5" - "price > 2.1" order: - file.name - file.ext - note.age - formula.ppu - formula.formatted_price - type: map name: "Example map" filters: "has_coords == true" lat: lat long: long title: file.name type selects from the built-in and plugin-added view types. name is the display name, and can be used to define the default view. filters are exactly the same as described above, but apply only to the view. Views can add additional data to store any information needed to maintain state or properly render, however plugin authors should take care to not use keys already in use by the core Bases plugin. As an example, a table view may use this to limit the number of rows or to select which column is used to sort rows and in which direction. A different view type such as a map could use this for mapping which property in the note corresponds to the latitude and longitude and which property should be displayed as the pin title. In the future, API will allow views to read and write these values, allowing the view to build its own interface for configuration. Properties There are three kinds of properties used in bases: Note properties, stored in frontmatter of Markdown files. File properties, accessible for all file types. Formula properties, defined in the file itself (see above)..base Note properties Note properties are only available for Markdown files, and are stored in the YAML frontmatter of each note. These properties can be accessed using the format or simply as a shorthand.note.authorauthor File properties File properties refer to the file currently being tested or evaluated. File properties are available for all file types, including attachments. For example, a filter will be true for all Markdown files and false otherwise.file.ext == "md" Property Type Description file.ctime Date Created time file.embeds List List of all embeds in the note file.ext String File extension file.file File File object, only usable in specific functions file.folder String Path of the file folder file.mtime Date Modified time file.name String File name file.path String Path of the file file.size Number File size file.links List List of all internal links in the note, including frontmatter Access properties of the current file Embedded bases can use to access properties of the current file. For example, will resolve to the name of the file which has embedded the base, instead of the file being evaluated.thisthis.file.name In a sidebar, takes on the special meaning of "the currently active file". This allows you to create contextual queries based on the active file in the main content area. For example, this can be used to replicate the backlinks pane with this filter: .thisfile.hasLink(this.file) Operators Arithmetic operators Arithmetic operators perform arithmetic on numbers. For example, .radius * (2 * 3.14) Operator Description + plus - minus * multiply / divide % modulo ( ) parenthesis Date arithmetic Dates can be modified by adding and subtracting durations. Duration units accept multiple formats: Unit Duration y, , yearyears year M, , monthmonths month d, , daydays day w, , weekweeks week h, , hourhours hour m, , minuteminutes minute s, , secondseconds second To modify or offset Date objects, use the or operator with a duration string. For example, adds 1 month to the date, while subtracts 2 hours from the date.+-date + "1M"date - "2h" The global function can be used to get the current date, and can be used to get the current date with time.today()now() now() + "1 day" returns a datetime exactly 24 hours from the time of execution. file.mtime > now() - "1 week" returns if the file was modified within the last week.true date("2024-12-01") + "1M" + "4h" + "3m" returns a Date object representing .2025-01-01 04:03:00 Subtract two dates to get the millisecond difference between the two, for example, .now() - file.ctime To get the date portion of a Date with time, use .datetime.date() To format a Date object, use the function, for example .format()datetime.format("YYYY-MM-DD") Comparison operators Comparison operators can be used to compare numbers, or Date objects. Equal and not equal can be used with any kind of value, not just numbers and dates. Operator Description == equals != not equal > greater than < less than >= greater than or equal to <= less than or equal to Boolean operators Boolean operators can be used to combine or invert logical values, resulting in a true or false value. Operator Description ! logical not && logical and || logical or Functions 请参阅可在公式和筛选器中使用的函数列表。 类型 基类具有一个类型系统，公式和筛选器使用该类型系统将函数应用于属性。 字符串、数字和布尔值 字符串、数字和布尔值是不需要函数创建的“原始”值。 字符串括在单引号或双引号中，例如 ."message" 数字写成数字，为清楚起见，可以选择用括号括起来。例如，或 .1(2.5) 布尔值以引号形式写入或不以引号形式写入。truefalse 日期和持续时间 日期表示特定日期，或者日期和时间，具体取决于用于创建日期的函数或已分配给属性的类型。 要构造日期，请使用函数，例如datedate("2025-01-01 12:00:00") 要修改日期，请添加或删除持续时间，例如now() + "1 hour"today() + "7d" 使用比较运算符（例如 或 ） 和算术运算符（例如，返回毫秒）。><(now() + "1d") - now()86400000 要提取日期的某些部分，请使用可用字段 （） 或便捷函数 （）。now().hournow.time() 许多其他字段和函数可用于 date 对象。 对象和列表 使用该函数将单个元素转换为列表。这对于可能包含列表或单个值混合的属性特别有用。list() 使用方括号和从 0 开始的索引访问列表元素。例如，返回列表中的第一个元素。property[0] 使用方括号和元素名称或点表示法访问对象元素。例如，或 .property.subpropproperty["subprop"] 文件和链接 frontmatter 属性中的 Wiki 链接会自动识别为 Link 对象。链接将在视图中呈现为可单击的链接。 要构建链接，请使用 global 函数，例如 或 。linklink("filename")link("https://obsidian.md") 您可以从任何字符串创建链接，例如 .link(file.ctime.date().toString()) 要设置显示文本，请传入可选的字符串或图标作为第二个参数，例如 或 。link("filename", "display")link("filename", icon("plus")) File 对象可以使用 可选的显示文本转换为链接。file.asLink() 链接可以与 和 进行比较。只要它们指向同一个文件，它们就是等效的，或者如果在查找文件时文件不存在，则它们的链接文本必须相同。==!= 可以将链接与 或 等文件进行比较。如果链接解析为文件，它们将相等。例如。filethisauthor == this 也可以在 list contains 中选中链接，例如 .authors.contains(this)

以下是 **Obsidian Bases 语法完整指南** 的整理，结构化呈现核心概念和用法：

---

### **1. 基础结构**

Bases 文件是有效的 YAML 文件（`.base` 扩展名），包含以下主要部分：

yaml

filters:     # 全局筛选条件
formulas:    # 自定义公式属性
properties:  # 属性显示配置
views:       # 数据视图定义

---

### **2. 核心组件**

#### **2.1 筛选器 (`filters`)**

- **作用**：限定数据范围，支持嵌套逻辑。
    
- **语法**：
    
    yaml
    
    filters:
      and|or|not:
        - 条件1
        - and|or|not: [嵌套条件]
    
- **示例**：
    
    yaml
    
    filters:
      or:
        - file.hasTag("book")
        - and:
            - file.inFolder("Notes")
            - "rating > 3"
    

#### **2.2 公式 (`formulas`)**

- **作用**：动态计算属性值。
    
- **语法**：
    
    yaml
    
    formulas:
      公式名: '表达式'
    
- **示例**：
    
    yaml
    
    formulas:
      price_with_tax: '(price * 1.08).toFixed(2)'
      is_recent: 'file.mtime > now() - "7d"'
    

#### **2.3 属性 (`properties`)**

- **作用**：配置属性显示名称。
    
- **语法**：
    
    yaml
    
    properties:
      属性路径:
        displayName: "显示名称"
    
- **示例**：
    
    yaml
    
    properties:
      formula.price_with_tax:
        displayName: "含税价格"
      file.ext:
        displayName: "文件类型"
    

#### **2.4 视图 (`views`)**

- **作用**：定义数据展示方式（表格、地图等）。
    
- **语法**：
    
    yaml
    
    views:
      - type: 视图类型
        name: "视图名称"
        filters: [视图专属筛选]
        order: [排序字段]
        group_by: 分组字段
    
- **示例**：
    
    yaml
    
    views:
      - type: table
        name: "待办列表"
        filters:
          and:
            - 'status != "done"'
        order: [file.mtime]
      - type: map
        name: "地点分布"
        lat: latitude
        long: longitude
    

---

### **3. 属性类型与访问**

|**类型**|**访问方式**|**示例**|
|---|---|---|
|Frontmatter 属性|`note.属性名` 或 `属性名`|`note.author` 或 `price`|
|文件属性|`file.属性名`|`file.size`, `file.mtime`|
|公式属性|`formula.公式名`|`formula.price_with_tax`|
|当前文件|`this.file.属性名`|`this.file.name` (嵌入时用)|

---

### **4. 运算符**

#### **4.1 算术运算符**

`+`, `-`, `*`, `/`, `%`, `( )`

yaml

示例: (price * quantity).toFixed(2)

#### **4.2 日期运算**

- **加减持续时间**：`date + "1d"`, `now() - "2h"`
    
- **单位**：`y`/`year`, `M`/`month`, `d`/`day`, `h`/`hour` 等。
    

yaml

示例: file.mtime > now() - "7d"  # 最近7天内修改的文件

#### **4.3 比较与逻辑**

|**运算符**|**说明**|**示例**|
|---|---|---|
|`==`, `!=`|等于/不等于|`status == "done"`|
|`>`, `<`|大小比较|`rating >= 4`|
|`!`, `&&`, `||`|逻辑运算|`!file.hasTag("archived")`|

---

### **5. 常用函数速查**

#### **5.1 日期处理**

|函数|说明|示例|
|---|---|---|
|`date("YYYY-MM-DD")`|字符串转日期|`date("2025-01-01")`|
|`now()`|当前时间|`now().format("HH:mm")`|
|`file.mtime.relative()`|相对时间（如"2天前"）|`file.ctime.relative()`|

#### **5.2 字符串处理**

|函数|说明|示例|
|---|---|---|
|`"text".contains("ex")`|是否包含子串|`title.contains("重要")`|
|`"A,B,C".split(",")`|分割字符串为列表|`tags.split(",")`|
|`"hello".upper()`|转大写|`author.upper()`|

#### **5.3 列表处理**

|函数|说明|示例|
|---|---|---|
|`[1,2,3].join(",")`|列表转字符串|`tags.join(";")`|
|`list.unique()`|去重|`authors.unique()`|
|`list.filter(x > 2)`|过滤|`prices.filter(x > 100)`|

---

### **6. 高级技巧**

- **动态链接**：`link("文件名", "显示文本")` 或 `file.asLink()`
    
- **正则匹配**：`/abc/.matches("abcde")`
    
- **嵌套逻辑**：在 `filters` 中组合 `and`/`or`/`not`
    

yaml

filters:
  not:
    - or:
        - file.hasTag("archived")
        - file.inFolder("Trash")

---

通过此指南，您可以快速掌握 Obsidian Bases 的核心功能，高效构建自定义数据库视图！