#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
最终链接器
不管任何条件，直接为所有链接少于4个的笔记添加链接
"""

import os
import re
from pathlib import Path

def extract_links(content):
    """提取现有链接"""
    return re.findall(r'\[\[([^\]]+)\]\]', content)

def main():
    """主函数"""
    handbook_dir = Path(r"c:\Users\<USER>\OneDrive\obsidian笔记系统\战略与战术\智库辅助手册")
    
    print("最终链接器 - 强制为所有笔记添加链接")
    print("=" * 40)
    
    # 通用链接
    universal_links = [
        "团队管理方法",
        "商业思维模式", 
        "工作效率提升",
        "学习成长路径",
        "问题解决技巧"
    ]
    
    processed = 0
    success = 0
    
    # 处理所有md文件
    for file_path in handbook_dir.rglob("*.md"):
        # 只排除脚本工具
        if '脚本工具' in str(file_path):
            continue
        
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
        except:
            continue
        
        # 检查链接数
        links = extract_links(content)
        if len(links) >= 4:  # 已经有4个或更多链接，跳过
            continue
        
        processed += 1
        title = file_path.stem
        
        print(f"处理: {title[:50]}... ({len(links)}个链接)")
        
        # 直接添加链接，不管是否已有相关笔记部分
        if '## 相关笔记' not in content:
            content += f"\n\n---\n\n## 相关笔记\n\n"
            for link in universal_links:
                if link not in links:
                    content += f"- [[{link}]]\n"
        else:
            # 在现有相关笔记部分添加
            lines = content.split('\n')
            new_lines = []
            found_section = False
            
            for line in lines:
                new_lines.append(line)
                if line.startswith('## 相关笔记') and not found_section:
                    found_section = True
                    for link in universal_links:
                        if link not in links:
                            new_lines.append(f"- [[{link}]]")
            
            content = '\n'.join(new_lines)
        
        # 保存文件
        try:
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(content)
            success += 1
            print(f"  ✓ 已添加链接")
        except:
            print(f"  ✗ 保存失败")
    
    print(f"\n最终结果:")
    print(f"需要处理的笔记: {processed}个")
    print(f"成功处理: {success}个")
    
    if success > 0:
        print(f"🎉 现在应该没有孤立笔记了！")

if __name__ == "__main__":
    main()
