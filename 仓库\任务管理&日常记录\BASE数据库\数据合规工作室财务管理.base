filters:
  and:
    - file.inFolder("BASE数据库/工作室财务数据")
    - file.ext == "md"
formulas:
  project_profit_rate: if(contract_amount && human_cost && outsource_cost, ((contract_amount - human_cost - outsource_cost) / contract_amount * 100).toFixed(2) + "%", "未计算")
  remaining_amount: if(contract_amount && received_amount, contract_amount - received_amount, 0)
  overdue_days: if(payment_due_date && is_overdue, (now() - date(payment_due_date)) / (1000 * 60 * 60 * 24), 0)
  consultant_efficiency: if(revenue_generated && human_cost, (revenue_generated / human_cost).toFixed(1), "0")
  utilization_rate: if(project_hours && total_hours, (project_hours / total_hours * 100).toFixed(1) + "%", "0%")
  idle_rate: if(idle_hours && total_hours, (idle_hours / total_hours * 100).toFixed(1) + "%", "0%")
  cash_flow_status: if(cash_balance > 500000, "✅ 安全", if(cash_balance > 250000, "⚠️ 注意", "❗ 预警"))
  months_runway: if(monthly_expense && cash_balance, (cash_balance / monthly_expense).toFixed(1) + "个月", "未知")
  client_risk_level: if(is_overdue, "🔴 高风险", if(payment_cycle > 60, "🟡 中风险", "🟢 低风险"))
  client_value_score: if(repeat_rate && contract_amount, (repeat_rate * 0.6 + (contract_amount / 100000) * 0.4).toFixed(1), "未评估")
properties:
  project_name:
    displayName: 项目名称
  client_type:
    displayName: 客户类型
  contract_amount:
    displayName: 合同金额(万元)
  received_amount:
    displayName: 已收款(万元)
  formula.remaining_amount:
    displayName: 应收款(万元)
  formula.project_profit_rate:
    displayName: 项目毛利率
  payment_cycle:
    displayName: 付款账期(天)
  is_overdue:
    displayName: 是否逾期
  formula.overdue_days:
    displayName: 逾期天数
  consultant_name:
    displayName: 顾问姓名
  project_hours:
    displayName: 项目工时
  idle_hours:
    displayName: 闲置工时
  total_hours:
    displayName: 总工时
  project_count:
    displayName: 参与项目数
  revenue_generated:
    displayName: 创收金额(万元)
  human_cost:
    displayName: 人力成本(万元)
  formula.consultant_efficiency:
    displayName: 人效比
  formula.utilization_rate:
    displayName: 利用率
  formula.idle_rate:
    displayName: 闲置率
  month:
    displayName: 月份
  expected_income:
    displayName: 预计收入(万元)
  expected_expense:
    displayName: 预计支出(万元)
  cash_balance:
    displayName: 现金结余(万元)
  formula.cash_flow_status:
    displayName: 现金流状态
  formula.months_runway:
    displayName: 资金可用月数
  key_actions:
    displayName: 关键动作
  client_name:
    displayName: 客户名称
  industry:
    displayName: 所属行业
  repeat_rate:
    displayName: 复购率
  referral_count:
    displayName: 转介绍次数
  formula.client_risk_level:
    displayName: 风险等级
  formula.client_value_score:
    displayName: 客户价值分
  record_type:
    displayName: 记录类型
  date:
    displayName: 日期
  notes:
    displayName: 备注
views:
  - type: table
    name: 项目利润分析
    filters:
      and:
        - record_type == "项目"
    order:
      - project_name
      - client_type
      - contract_amount
      - received_amount
      - formula.remaining_amount
      - human_cost
      - outsource_cost
      - formula.project_profit_rate
      - payment_cycle
      - is_overdue
      - formula.overdue_days
    sort:
      - property: formula.project_profit_rate
        direction: DESC
    limit: 50
  - type: table
    name: 人力效率监控
    filters:
      and:
        - record_type == "人力"
    order:
      - consultant_name
      - project_hours
      - idle_hours
      - formula.utilization_rate
      - formula.idle_rate
      - project_count
      - revenue_generated
      - human_cost
      - formula.consultant_efficiency
    sort:
      - property: formula.consultant_efficiency
        direction: DESC
    limit: 30
  - type: table
    name: 现金流预警
    filters:
      and:
        - record_type == "现金流"
    order:
      - month
      - expected_income
      - expected_expense
      - cash_balance
      - formula.cash_flow_status
      - formula.months_runway
      - key_actions
    sort:
      - property: month
        direction: ASC
    limit: 12
  - type: cards
    name: 高风险项目
    filters:
      and:
        - record_type == "项目"
        - is_overdue == true
    order:
      - project_name
      - client_name
      - formula.remaining_amount
      - formula.overdue_days
      - formula.client_risk_level
    sort:
      - property: formula.overdue_days
        direction: DESC
    limit: 20
  - type: table
    name: 客户价值分析
    filters:
      and:
        - record_type == "客户"
    order:
      - client_name
      - industry
      - contract_amount
      - repeat_rate
      - referral_count
      - formula.client_value_score
      - formula.client_risk_level
    sort:
      - property: formula.client_value_score
        direction: DESC
    limit: 50
  - type: cards
    name: 低效顾问预警
    filters:
      and:
        - record_type == "人力"
        - formula.consultant_efficiency < 5
    order:
      - consultant_name
      - formula.consultant_efficiency
      - formula.idle_rate
      - project_count
      - revenue_generated
    sort:
      - property: formula.consultant_efficiency
        direction: ASC
    limit: 10
  - type: table
    name: 月度财务仪表盘
    filters:
      and:
        - record_type == "现金流"
        - date >= now() - "90d"
    order:
      - month
      - expected_income
      - expected_expense
      - cash_balance
      - formula.cash_flow_status
    sort:
      - property: month
        direction: DESC
    limit: 6
