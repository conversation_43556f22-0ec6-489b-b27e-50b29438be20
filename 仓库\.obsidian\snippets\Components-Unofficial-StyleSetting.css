/* MIT License
Author: RavenHogwarts
Note: If you decide to implement it in your theme or redistribute it, please keep this comment (Especially for *certain* individuals who may try to rebrand it as their own :))
Follow me: https://github.com/RavenHogWarts
*/
/* @settings
name: Components Unofficial StyleSetting
id: Components-Unofficial-StyleSetting
settings:
- id: Components--component-dynamicDataView-settings
  title: 数据视图组件
  type: heading
  level: 1
  collapsed: true
- id: Components--component-dynamicDataView-fullWidth
  title: 数据视图全宽
  type: class-toggle
  default: true
  description: 数据视图是否全宽显示
- id: Components--component-dynamicDataView-gallery-settings
  title: 画廊视图设置
  type: heading
  level: 2
  collapsed: true
- id: Components-gallery-border-color
  title: 边框颜色
  type: variable-themed-color
  format: hex
  default-light: '#'
  default-dark: '#'
  description: 数据视图画廊边框颜色设置
- id: Components--component-dynamicDataView-table-settings
  title: 表格视图设置
  type: heading
  level: 2
  collapsed: true
- id: Components-table-bottom-float-hide
  title: 隐藏表格底部的统计
  type: class-toggle
  default: false
- id: Components-table-markdown-optimize-settings
  title: Markdown类型布局优化
  type: heading
  level: 3
  collapsed: true
  description: 需要开启markdown类型的自动换行
- id: Components-table-markdown-optimize
  title: 布局选择
  type: class-select
  default: Components-table-markdown-optimize-default
  description: 固定高度：鼠标选中后可上下滚动查看内容；折叠：鼠标选中后全高展开
  options:
  - label: 默认
    value: Components-table-markdown-optimize-default
  - label: 固定高度
    value: Components-table-markdown-optimize-limit
  - label: 折叠
    value: Components-table-markdown-optimize-collapse
- id: Components-table-markdown-limit-height
  title: Markdown类型高度限制
  type: variable-text
  default: 32px
- id: Components--component-dynamicDataView-kanban-settings
  title: 看板视图设置
  type: heading
  level: 2
  collapsed: true
- id: Components-kanban-border-color
  title: 边框颜色
  type: variable-themed-color
  format: hex
  default-light: '#'
  default-dark: '#'
  description: 数据视图看板边框颜色设置
- id: Components--component-dynamicDataView-list-settings
  title: 列表视图设置(空)
  type: heading
  level: 2
  collapsed: true
- id: Components--component-dynamicDataView-calendar-settings
  title: 日历视图设置
  type: heading
  level: 2
  collapsed: true
- id: Components-calendar-active-date-color
  title: 选中日期边框颜色
  type: variable-themed-color
  format: hex
  default-light: '#'
  default-dark: '#'
  description: 默认为外观中的设置的基础颜色
- id: Components-calendar-background-color
  title: 今日单元格背景色
  type: variable-themed-color
  format: hex
  default-light: '#'
  default-dark: '#'
  description: 今日单元格背景颜色设置
- id: Components--component-dynamicDataView-progress-bar-settings
  title: 进度条
  type: heading
  level: 2
  collapsed: true
- id: Components-dynamicDataView-progress-bar-progress-bar-label-hide
  title: 进度条指示器隐藏
  type: class-toggle
  default: false
  description: 进度条指示器是否隐藏
- id: Components-dynamicDataView-progress-bar-style
  title: 进度条样式
  type: class-select
  default: Components-dynamicDataView-progress-bar-style-default
  options:
  - label: 默认
    value: Components-dynamicDataView-progress-bar-style-default
  - label: 彩虹猫
    value: Components-dynamicDataView-progress-bar-style-rainbow
  - label: 软萌猫
    value: Components-dynamicDataView-progress-bar-style-soft
- id: Components--component-dynamicDataView-progress-ring-settings
  title: 进度圆环
  type: heading
  level: 2
  collapsed: true
- id: Components-dynamicDataView-progress-ring-progress-indicator-hide
  title: 进度指示器隐藏
  type: class-toggle
  default: false
  description: 进度圆环进度指示器是否隐藏
- id: Components-dynamicDataView-PageTasks-settings
  title: 任务列表
  type: heading
  level: 2
  collapsed: true
- id: Components-dynamicDataView-PageTasks-font-size
  title: 字体大小
  type: variable-text
  default: 12px
  description: 任务列表字体大小设置
- id: Components-dynamicDataView-PageTasks-completedTask-decoration-hide
  title: 完成任务删除线移除
  type: class-toggle
  default: false
  description: 任务列表完成任务删除线是否移除
- id: Components--component-count-settings
  title: 统计数字组件(空)
  type: heading
  level: 1
  collapsed: true
- id: Components--component-timing-settings
  title: 正计时组件
  type: heading
  level: 1
  collapsed: true
- id: Components--component-timing-font-bold
  title: 字体加粗
  type: class-toggle
  default: false
  description: 正计时字体是否加粗
- id: Components--component-countdown-settings
  title: 倒计时组件
  type: heading
  level: 1
  collapsed: true
- id: Components--component-countdown-font-bold
  title: 字体加粗
  type: class-toggle
  default: false
  description: 倒计时字体是否加粗
- id: Components--component-dateProgress-settings
  title: 日期进度组件
  type: heading
  level: 1
  collapsed: true
- id: Components--component-dateProgress-font-bold
  title: 字体加粗
  type: class-toggle
  default: false
  description: 日期进度字体是否加粗
- id: Components--component-dateProgress-progress-bar-style
  title: 进度条样式
  type: class-select
  default: Components--component-dateProgress-progress-bar-style-default
  options:
  - label: 默认
    value: Components--component-dateProgress-progress-bar-style-default
  - label: 彩虹
    value: Components--component-dateProgress-progress-bar-style-rainbow
- id: Components--component-clock-settings
  title: 时钟组件(空)
  type: heading
  level: 1
  collapsed: true
- id: Components--component-quote-settings
  title: 摘录组件(空)
  type: heading
  level: 1
  collapsed: true
- id: Components--component-button-settings
  title: 按钮组件(空)
  type: heading
  level: 1
  collapsed: true
- id: Components--component-card-settings
  title: 卡片组件
  type: heading
  level: 1
  collapsed: true
- id: Components--component-card-title-align
  title: 卡片标题文字位置
  type: class-select
  default: Components--component-card-title-align-default
  options:
  - label: 默认
    value: Components--component-card-title-align-default
  - label: 居中
    value: Components--component-card-title-align-center
  - label: 左对齐
    value: Components--component-card-title-align-left
  - label: 右对齐
    value: Components--component-card-title-align-right
- id: Components--component-card-description-align
  title: 卡片描述文字位置
  type: class-select
  default: Components--component-card-description-align-default
  options:
  - label: 默认
    value: Components--component-card-description-align-default
  - label: 居中
    value: Components--component-card-description-align-center
  - label: 双端对齐居中
    value: Components--component-card-description-align-center-justify
  - label: 左对齐
    value: Components--component-card-description-align-left
  - label: 右对齐
    value: Components--component-card-description-align-right
- id: Components--component-chart-settings
  title: 图表组件(空)
  type: heading
  level: 1
  collapsed: true
- id: Components--component-dataview-settings
  title: Dataview组件(空)
  type: heading
  level: 1
  collapsed: true
- id: Components--component-markdown-settings
  title: Markdown组件(空)
  type: heading
  level: 1
  collapsed: true
- id: Components--component-dailyCheck-settings
  title: 打卡组件
  type: heading
  level: 1
  collapsed: true
- id: Components-dailyCheck-border-hide
  title: 打卡边框线隐藏
  type: class-toggle
  default: false
- id: Components--component-attachments-settings
  title: 附件库组件
  type: heading
  level: 1
  collapsed: true
- id: Components--component-attachments-border-settings
  title: 边框设置
  type: heading
  level: 2
  collapsed: true
- id: Components--component-attachments-border-color
  title: 边框颜色
  type: variable-themed-color
  format: hex
  default-light: '#'
  default-dark: '#'
  description: 附件库边框颜色设置，默认为透明
- id: Components--component-attachments-border-radius
  title: 边框圆角
  type: variable-text
  default: 0px
  description: 附件库边框圆角设置
- id: Components--component-attachments-border-shadow
  title: 启用边框阴影
  type: class-toggle
  default: false
  description: 启用附件库边框阴影设置
- id: Components--component-attachments-border-shadow-color
  title: 边框阴影颜色
  type: variable-themed-color
  format: hex
  default-light: '#'
  default-dark: '#'
  description: 附件库边框阴影颜色设置，默认为透明
- id: Components--component-attachments-border-shadow-offset-x
  title: 边框阴影X偏移
  type: variable-text
  default: 0px
  description: 附件库边框阴影X偏移设置
- id: Components--component-attachments-border-shadow-offset-y
  title: 边框阴影Y偏移
  type: variable-text
  default: 2px
  description: 附件库边框阴影Y偏移设置
- id: Components--component-attachments-border-shadow-blur-radius
  title: 边框阴影模糊度
  type: variable-text
  default: 6px
  description: 附件库边框阴影模糊度设置
- id: Components--component-attachments-border-shadow-spread-radius
  title: 边框阴影扩散度
  type: variable-text
  default: 0px
  description: 附件库边框阴影扩散度设置
- id: Components--component-extra-settings
  title: 其他
  type: heading
  level: 1
  collapsed: true
- id: Components--component-colorSet-settings
  title: 颜色集
  type: heading
  level: 2
  collapsed: true
- id: Components--component-colorSet-enable
  title: 使用自定义颜色集
  type: class-toggle
  default: false
  description: 启用自定义颜色集
- id: Components--component-colorSet-light-gray
  title: 浅灰
  type: heading
  level: 3
  collapsed: true
  description: 颜色集浅灰 light-gray
- id: Components--component-colorSet-light-gray-bgColor
  title: 背景颜色
  type: variable-themed-color
  format: hex
  default-light: '#ebeced'
  default-dark: '#454b4e'
- id: Components--component-colorSet-light-gray-fontColor
  title: 文字颜色
  type: variable-themed-color
  format: hex
  default-light: '#9b9a97'
  default-dark: '#979a9bf2'
- id: Components--component-colorSet-gray
  title: 灰
  type: heading
  level: 3
  collapsed: true
  description: 颜色集灰 gray
- id: Components--component-colorSet-gray-bgColor
  title: 背景颜色
  type: variable-themed-color
  format: hex
  default-light: '#e9e5e3'
  default-dark: '#434040'
- id: Components--component-colorSet-gray-fontColor
  title: 文字颜色
  type: variable-themed-color
  format: hex
  default-light: '#64473a'
  default-dark: '#937264'
- id: Components--component-colorSet-orange
  title: 橙
  type: heading
  level: 3
  collapsed: true
  description: 颜色集橙 orange
- id: Components--component-colorSet-orange-bgColor
  title: 背景颜色
  type: variable-themed-color
  format: hex
  default-light: '#faebdd'
  default-dark: '#594a3a'
- id: Components--component-colorSet-orange-fontColor
  title: 文字颜色
  type: variable-themed-color
  format: hex
  default-light: '#d9730d'
  default-dark: '#ffa344'
- id: Components--component-colorSet-yellow
  title: 黄
  type: heading
  level: 3
  collapsed: true
  description: 颜色集黄 yellow
- id: Components--component-colorSet-yellow-bgColor
  title: 背景颜色
  type: variable-themed-color
  format: hex
  default-light: '#fbf3db'
  default-dark: '#59563b'
- id: Components--component-colorSet-yellow-fontColor
  title: 文字颜色
  type: variable-themed-color
  format: hex
  default-light: '#dfab01'
  default-dark: '#ffdc49'
- id: Components--component-colorSet-green
  title: 绿
  type: heading
  level: 3
  collapsed: true
  description: 颜色集绿 green
- id: Components--component-colorSet-green-bgColor
  title: 背景颜色
  type: variable-themed-color
  format: hex
  default-light: '#ddedea'
  default-dark: '#354c4b'
- id: Components--component-colorSet-green-fontColor
  title: 文字颜色
  type: variable-themed-color
  format: hex
  default-light: '#0f7b6c'
  default-dark: '#4dab9a'
- id: Components--component-colorSet-blue
  title: 蓝
  type: heading
  level: 3
  collapsed: true
  description: 颜色集蓝 blue
- id: Components--component-colorSet-blue-bgColor
  title: 背景颜色
  type: variable-themed-color
  format: hex
  default-light: '#ddebf1'
  default-dark: '#364954'
- id: Components--component-colorSet-blue-fontColor
  title: 文字颜色
  type: variable-themed-color
  format: hex
  default-light: '#0b6e99'
  default-dark: '#529cca'
- id: Components--component-colorSet-purple
  title: 紫
  type: heading
  level: 3
  collapsed: true
  description: 颜色集紫 purple
- id: Components--component-colorSet-purple-bgColor
  title: 背景颜色
  type: variable-themed-color
  format: hex
  default-light: '#eae4f2'
  default-dark: '#443f57'
- id: Components--component-colorSet-purple-fontColor
  title: 文字颜色
  type: variable-themed-color
  format: hex
  default-light: '#6940a5'
  default-dark: '#9a6dd7'
- id: Components--component-colorSet-pink
  title: 粉
  type: heading
  level: 3
  collapsed: true
  description: 颜色集粉 pink
- id: Components--component-colorSet-pink-bgColor
  title: 背景颜色
  type: variable-themed-color
  format: hex
  default-light: '#f4dfeb'
  default-dark: '#533b4c'
- id: Components--component-colorSet-pink-fontColor
  title: 文字颜色
  type: variable-themed-color
  format: hex
  default-light: '#ad1a72'
  default-dark: '#e255a1'
- id: Components--component-colorSet-red
  title: 红
  type: heading
  level: 3
  collapsed: true
  description: 颜色集红 red
- id: Components--component-colorSet-red-bgColor
  title: 背景颜色
  type: variable-themed-color
  format: hex
  default-light: '#fbe4e4'
  default-dark: '#594141'
- id: Components--component-colorSet-red-fontColor
  title: 文字颜色
  type: variable-themed-color
  format: hex
  default-light: '#e03e3e'
  default-dark: '#ff7369'
- id: Components--component-fix-settings
  title: 样式覆盖
  type: heading
  level: 2
  collapsed: true
- id: condition-item-width-fix
  title: 条件项宽度
  type: class-toggle
  default: false
  description: Ray：(筛选/排序)等的条件项宽度不再固定，设置为容器宽度的30%
- id: dynamicDataView-margin-top-fix
  title: 数据视图按钮/标签上边距
  type: class-toggle
  default: false
  description: 数据视图按钮/标签上边距调整为2px，保持与其他单元格上边距基本一致
*/
/* Components-body */
body {
  --Components-gallery-border-color: transparent;
  --Components-kanban-border-color: transparent;
  --Components-calendar-background-color: transparent;
  --Components-calendar-active-date-color: var(--interactive-accent);
  --Components-table-markdown-top: -10px;
  --Components-table-markdown-limit-height: 32px;
  --Components--component-attachments-border-color: transparent;
  --Components--component-attachments-border-radius: 0px;
  --Components--component-attachments-border-shadow-color: transparent;
  --Components--component-attachments-border-shadow-offset-x: 0px;
  --Components--component-attachments-border-shadow-offset-y: 2px;
  --Components--component-attachments-border-shadow-blur-radius: 6px;
  --Components--component-attachments-border-shadow-spread-radius: 0px;
  --Components-dynamicDataView-PageTasks-font-size: var(--font-ui-smaller);
}

.theme-light {
  --Components--component-colorSet-light-gray-bgColor: #ebeced;
  --Components--component-colorSet-light-gray-fontColor: #9b9a97;
  --Components--component-colorSet-gray-bgColor: #e9e5e3;
  --Components--component-colorSet-gray-fontColor: #64473a;
  --Components--component-colorSet-orange-bgColor: #faebdd;
  --Components--component-colorSet-orange-fontColor: #d9730d;
  --Components--component-colorSet-yellow-bgColor: #fbf3db;
  --Components--component-colorSet-yellow-fontColor: #dfab01;
  --Components--component-colorSet-green-bgColor: #ddedea;
  --Components--component-colorSet-green-fontColor: #0f7b6c;
  --Components--component-colorSet-blue-bgColor: #ddebf1;
  --Components--component-colorSet-blue-fontColor: #0b6e99;
  --Components--component-colorSet-purple-bgColor: #eae4f2;
  --Components--component-colorSet-purple-fontColor: #6940a5;
  --Components--component-colorSet-pink-bgColor: #f4dfeb;
  --Components--component-colorSet-pink-fontColor: #ad1a72;
  --Components--component-colorSet-red-bgColor: #fbe4e4;
  --Components--component-colorSet-red-fontColor: #e03e3e;
}
.theme-dark {
  --Components--component-colorSet-light-gray-bgColor: #454b4e;
  --Components--component-colorSet-light-gray-fontColor: #979a9bf2;
  --Components--component-colorSet-gray-bgColor: #434040;
  --Components--component-colorSet-gray-fontColor: #937264;
  --Components--component-colorSet-orange-bgColor: #594a3a;
  --Components--component-colorSet-orange-fontColor: #ffa344;
  --Components--component-colorSet-yellow-bgColor: #59563b;
  --Components--component-colorSet-yellow-fontColor: #ffdc49;
  --Components--component-colorSet-green-bgColor: #354c4b;
  --Components--component-colorSet-green-fontColor: #4dab9a;
  --Components--component-colorSet-blue-bgColor: #364954;
  --Components--component-colorSet-blue-fontColor: #529cca;
  --Components--component-colorSet-purple-bgColor: #443f57;
  --Components--component-colorSet-purple-fontColor: #9a6dd7;
  --Components--component-colorSet-pink-bgColor: #533b4c;
  --Components--component-colorSet-pink-fontColor: #e255a1;
  --Components--component-colorSet-red-bgColor: #594141;
  --Components--component-colorSet-red-fontColor: #ff7369;
}

/* Components--component-dailyCheck-settings */
.Components-dailyCheck-border-hide .components--component-dailyCheck .components--DailyCheckComponentItem {    
	border: 0px solid var(--background-modifier-border);
}

/* Components--component-countdown-settings */
/* Components--component-countdown-font-bold */
.Components--component-countdown-font-bold .components--component-countdown .components--component-title {
  font-weight: bold;
}

/* Components--component-timing-settings */
/* Components--component-timing-font-bold */
.Components--component-timing-font-bold .components--component-timing .components--component-title {
  font-weight: bold;
}

/* Components--component-dateProgress-settings */
/* Components--component-dateProgress-font-bold */
.Components--component-dateProgress-font-bold .components--component-dateProgress .components--component-title {
  font-weight: bold;
}
/* Components--component-dateProgress-progress-bar-style */
/* Components--component-dateProgress-progress-bar-style-default */
.Components--component-dateProgress-progress-bar-style-default .components--component-dateProgress .components--DateProgressBarContainer-BarTrack {
  display: flex;
}

/* Components--component-dateProgress-progress-bar-style-rainbow */
.Components--component-dateProgress-progress-bar-style-rainbow .components--component-dateProgress .components--DateProgressBarContainer-BarTrack {
  background-color: var(--background-secondary);
  background: url("data:image/gif;base64,R0lGODlhMAAMAIAAAAxBd////yH/C05FVFNDQVBFMi4wAwEAAAAh+QQECgAAACwAAAAAMAAMAAACJYSPqcvtD6MKstpLr24Z9A2GYvJ544mhXQmxoesElIyCcB3dRgEAIfkEBAoAAAAsAQACAC0ACgAAAiGEj6nLHG0enNQdWbPefOHYhSLydVhJoSYXPO04qrAmJwUAIfkEBAoAAAAsBQABACkACwAAAiGEj6nLwQ8jcC5ViW3evHt1GaE0flxpphn6BNTEqvI8dQUAIfkEBAoAAAAsAQABACoACwAAAiGEj6nLwQ+jcU5VidPNvPtvad0GfmSJeicUUECbxnK0RgUAIfkEBAoAAAAsAAAAACcADAAAAiCEj6mbwQ+ji5QGd6t+c/v2hZzYiVpXmuoKIikLm6hXAAAh+QQECgAAACwAAAAALQAMAAACI4SPqQvBD6NysloTXL480g4uX0iW1Wg21oem7ismLUy/LFwAACH5BAQKAAAALAkAAAAkAAwAAAIghI8Joe0Po0yBWTaz3g/z7UXhMX7kYmplmo0rC8cyUgAAIfkEBAoAAAAsBQAAACUACgAAAh2Ejwmh7Q+jbIFZNrPeEXPudU74IVa5kSiYqOtRAAAh+QQECgAAACwEAAAAIgAKAAACHISPELfpD6OcqTGKs4bWRp+B36YFi0mGaVmtWQEAIfkEBAoAAAAsAAAAACMACgAAAh2EjxC36Q+jnK8xirOW1kavgd+2BYtJhmnpiGtUAAAh+QQECgAAACwAAAAALgALAAACIYSPqcvtD+MKicqLn82c7e6BIhZQ5jem6oVKbfdqQLzKBQAh+QQECgAAACwCAAIALAAJAAACHQx+hsvtD2OStDplKc68r2CEm0eW5uSN6aqe1lgAADs=");
  width: 100%;
  height: 12px;
  overflow: hidden;
  padding: 0;
}
.Components--component-dateProgress-progress-bar-style-rainbow .components--component-dateProgress .components--DateProgressBarContainer-BarValue {
  background: linear-gradient(to bottom, #FF0000 0%, #FF0000 16.5%, #FF9900 16.5%, #FF9900 33%, #FFFF00 33%, #FFFF00 50%, #33FF00 50%, #33FF00 66%, #0099FF 66%, #0099FF 83.5%, #6633ff 83.5%, #6633ff 100%) !important;
  overflow: hidden;
  height: 12px;
  border-radius: 6px;
  overflow: hidden;
}

.Components-dynamicDataView-PageTasks-completedTask-decoration-hide .components--PageTasks .components--PageTaskItemInner[data-completed=true] {
  text-decoration: unset;
}

.components--PageTaskItemText,
.components--PageTaskItemText .components--PageTaskItemTextSpan>* {
  font-size: var(--Components-dynamicDataView-PageTasks-font-size);
}

/* Components--component-card-settings */
.Components--component-card-title-align-default .components--component-card .components-CardComponentMainTitle:not(svg) .components-CardComponentMainTitleLabel {
  display: flex;
}
.Components--component-card-title-align-center .components--component-card .components-CardComponentMainTitle:not(svg) .components-CardComponentMainTitleLabel {
  display: flex;
  justify-content: center;
  align-items: center;
}
.Components--component-card-title-align-left .components--component-card .components-CardComponentMainTitle:not(svg) .components-CardComponentMainTitleLabel {
  display: flex;
  justify-content: flex-start;
  align-items: center;
}
.Components--component-card-title-align-right .components--component-card .components-CardComponentMainTitle:not(svg) .components-CardComponentMainTitleLabel {
  display: flex;
  justify-content: flex-end;
  align-items: center;
}
.Components--component-card-description-align-default .components--component-card .components-CardComponentMainDescription {
  display: flex;
}
.Components--component-card-description-align-center .components--component-card .components-CardComponentMainDescription {
  display: flex;
  justify-content: center;
  align-items: center;
}
.Components--component-card-description-align-center-justify .components--component-card .components-CardComponentMainDescription {
  display: flex;
  justify-content: center;
  align-items: center;
  text-align: justify;
  text-align-last: center;
}
.Components--component-card-description-align-left .components--component-card .components-CardComponentMainDescription {
  display: flex;
  justify-content: flex-start;
  align-items: center;
}
.Components--component-card-description-align-right .components--component-card .components-CardComponentMainDescription {
  display: flex;
  justify-content: flex-end;
  align-items: center;
}

/* Components--component-markdown-settings */


/* Components--component-dynamicDataView-settings */
/* Components--component-dynamicDataView-fullWidth */
body.Components--component-dynamicDataView-fullWidth .workspace-leaf:has(.components--component-dynamicDataView) {
  --file-line-width: 95vw;
	--line-width: 95vw;
	--container-img-width: 100%;
	--table-wrapper-width: 100%;
}

/* components--GalleryDynamicDataView */
/* Components--component-dynamicDataView-gallery-border */
.components--GalleryDynamicDataView .components--DynamicDataView-PageCard {
  border-color: var(--Components-gallery-border-color);
}

/* components--TableDynamicDataView */
.Components-table-markdown-optimize-default .components--TableDynamicDataView .ag-cell-auto-height .components--MarkdownCellContainer {
  height: auto;
}

.Components-table-markdown-optimize-limit .components--TableDynamicDataView .ag-cell-auto-height .components--MarkdownCellContainer {
  height: var(--Components-table-markdown-limit-height) !important;
  overflow: auto;
  text-overflow: ellipsis;

  &:hover {
    height: var(--Components-table-markdown-limit-height) !important;
  }
}

.Components-table-markdown-optimize-collapse .components--TableDynamicDataView .ag-cell-auto-height .components--MarkdownCellContainer {
  height: var(--Components-table-markdown-limit-height) !important;
  overflow: hidden;
  text-overflow: ellipsis;

  &:hover {
    height: auto !important;
  }
}

.Components-table-bottom-float-hide .components--TableDynamicDataView .ag-floating-bottom {
  display: none !important;
}

/* components--kanban */
/* Components--component-dynamicDataView-kanban-border */
.components--KanbanDynamicDataView .components--DynamicDataView-PageCard {
  border-color: var(--Components-kanban-border-color);
}
/* Components-list */

/* Components-calendar */
/* @Ray 日历视图当天的背景色 */
.components--CalendarDynamicDataView .components--CalendarDateCell:has(.components--CalendarDateCellLabel_today) {
  background-color: var(--Components-calendar-background-color);
  /* border-right: 1px solid var(--Components-calendar-border-color); */
  /* border-bottom: 1px solid var(--Components-calendar-border-color); */
}
.components--CalendarDynamicDataView .components--CalendarDateCell_active {
  border-color: var(--Components-calendar-active-date-color);
}

/* components--progress-bar-el */
.Components-dynamicDataView-progress-bar-progress-bar-label-hide .components--component-dynamicDataView .components--progress-bar-el .components--progress-bar-label {
  /* display: none; */
  visibility: hidden;
}
/* Components-dynamicDataView-progress-bar-style */
/* Components-dynamicDataView-progress-bar-style-default */
.Components-dynamicDataView-progress-bar-style-default .components--component-dynamicDataView .components--progress-bar-track {
  display: flex;
}

/* Components-dynamicDataView-progress-bar-style-rainbow */
.Components-dynamicDataView-progress-bar-style-rainbow .components--component-dynamicDataView .components--progress-bar-track,
.Components-dynamicDataView-progress-bar-style-rainbow .components--component-dynamicDataView .components--PageTaskProgressBar:not(.components--PageTaskProgressBar_none) {
  background-color: var(--background-secondary);
  background: url("data:image/gif;base64,R0lGODlhMAAMAIAAAAxBd////yH/C05FVFNDQVBFMi4wAwEAAAAh+QQECgAAACwAAAAAMAAMAAACJYSPqcvtD6MKstpLr24Z9A2GYvJ544mhXQmxoesElIyCcB3dRgEAIfkEBAoAAAAsAQACAC0ACgAAAiGEj6nLHG0enNQdWbPefOHYhSLydVhJoSYXPO04qrAmJwUAIfkEBAoAAAAsBQABACkACwAAAiGEj6nLwQ8jcC5ViW3evHt1GaE0flxpphn6BNTEqvI8dQUAIfkEBAoAAAAsAQABACoACwAAAiGEj6nLwQ+jcU5VidPNvPtvad0GfmSJeicUUECbxnK0RgUAIfkEBAoAAAAsAAAAACcADAAAAiCEj6mbwQ+ji5QGd6t+c/v2hZzYiVpXmuoKIikLm6hXAAAh+QQECgAAACwAAAAALQAMAAACI4SPqQvBD6NysloTXL480g4uX0iW1Wg21oem7ismLUy/LFwAACH5BAQKAAAALAkAAAAkAAwAAAIghI8Joe0Po0yBWTaz3g/z7UXhMX7kYmplmo0rC8cyUgAAIfkEBAoAAAAsBQAAACUACgAAAh2Ejwmh7Q+jbIFZNrPeEXPudU74IVa5kSiYqOtRAAAh+QQECgAAACwEAAAAIgAKAAACHISPELfpD6OcqTGKs4bWRp+B36YFi0mGaVmtWQEAIfkEBAoAAAAsAAAAACMACgAAAh2EjxC36Q+jnK8xirOW1kavgd+2BYtJhmnpiGtUAAAh+QQECgAAACwAAAAALgALAAACIYSPqcvtD+MKicqLn82c7e6BIhZQ5jem6oVKbfdqQLzKBQAh+QQECgAAACwCAAIALAAJAAACHQx+hsvtD2OStDplKc68r2CEm0eW5uSN6aqe1lgAADs=");
  width: 100%;
  height: 12px;
  overflow: visible;
}
.Components-dynamicDataView-progress-bar-style-rainbow .components--component-dynamicDataView .components--progress-bar-track .components--progress-bar-value,
.Components-dynamicDataView-progress-bar-style-rainbow .components--component-dynamicDataView .components--PageTaskProgressItem.components--PageTaskProgressItem_completed {
  background: linear-gradient(to bottom, #FF0000 0%, #FF0000 16.5%, #FF9900 16.5%, #FF9900 33%, #FFFF00 33%, #FFFF00 50%, #33FF00 50%, #33FF00 66%, #0099FF 66%, #0099FF 83.5%, #6633ff 83.5%, #6633ff 100%);
  overflow: visible;
}
.Components-dynamicDataView-progress-bar-style-rainbow .components--component-dynamicDataView .components--progress-bar-track .components--progress-bar-value {
  border-radius: var(--radius-s);
}
.Components-dynamicDataView-progress-bar-style-rainbow .components--component-dynamicDataView .components--progress-bar-label {
  margin-left: 12px;
}
.Components-dynamicDataView-progress-bar-style-rainbow .components--component-dynamicDataView .components--PageTaskProgressBar {
  gap: 0px;

  .components--PageTaskProgressItem.components--PageTaskProgressItem_completed {
    border-radius: 0px;
  }

  .components--PageTaskProgressItem.components--PageTaskProgressItem_completed:first-child {
    border-radius: var(--radius-s) 0 0 var(--radius-s);
  }
}
.Components-dynamicDataView-progress-bar-style-rainbow .components--component-dynamicDataView .components--progress-bar-track .components--progress-bar-value::after,
.Components-dynamicDataView-progress-bar-style-rainbow .components--component-dynamicDataView .components--PageTaskProgressBar .components--PageTaskProgressItem_completed:not(:has(+ .components--PageTaskProgressItem_completed))::after {
  content: '';
  display: flex;
  position: relative;
  left: 100%;
  top: 50%;      /* 垂直居中 */
  transform: translate(-50%, -50%);
  width: 34px;   /* 图片宽度 */
  height: 21px;  /* 图片高度 */
  background-image: url("data:image/gif;base64,R0lGODlhIgAVAKIHAL3/9/+Zmf8zmf/MmZmZmf+Z/wAAAAAAACH/C05FVFNDQVBFMi4wAwEAAAAh/wtYTVAgRGF0YVhNUDw/eHBhY2tldCBiZWdpbj0i77u/IiBpZD0iVzVNME1wQ2VoaUh6cmVTek5UY3prYzlkIj8+IDx4OnhtcG1ldGEgeG1sbnM6eD0iYWRvYmU6bnM6bWV0YS8iIHg6eG1wdGs9IkFkb2JlIFhNUCBDb3JlIDUuMy1jMDExIDY2LjE0NTY2MSwgMjAxMi8wMi8wNi0xNDo1NjoyNyAgICAgICAgIj4gPHJkZjpSREYgeG1sbnM6cmRmPSJodHRwOi8vd3d3LnczLm9yZy8xOTk5LzAyLzIyLXJkZi1zeW50YXgtbnMjIj4gPHJkZjpEZXNjcmlwdGlvbiByZGY6YWJvdXQ9IiIgeG1sbnM6eG1wTU09Imh0dHA6Ly9ucy5hZG9iZS5jb20veGFwLzEuMC9tbS8iIHhtbG5zOnN0UmVmPSJodHRwOi8vbnMuYWRvYmUuY29tL3hhcC8xLjAvc1R5cGUvUmVzb3VyY2VSZWYjIiB4bWxuczp4bXA9Imh0dHA6Ly9ucy5hZG9iZS5jb20veGFwLzEuMC8iIHhtcE1NOk9yaWdpbmFsRG9jdW1lbnRJRD0ieG1wLmRpZDpDMkJBNjY5RTU1NEJFMzExOUM4QUM2MDAwNDQzRERBQyIgeG1wTU06RG9jdW1lbnRJRD0ieG1wLmRpZDpCREIzOEIzMzRCN0IxMUUzODhEQjgwOTYzMTgyNTE0QiIgeG1wTU06SW5zdGFuY2VJRD0ieG1wLmlpZDpCREIzOEIzMjRCN0IxMUUzODhEQjgwOTYzMTgyNTE0QiIgeG1wOkNyZWF0b3JUb29sPSJBZG9iZSBQaG90b3Nob3AgQ1M2IChXaW5kb3dzKSI+IDx4bXBNTTpEZXJpdmVkRnJvbSBzdFJlZjppbnN0YW5jZUlEPSJ4bXAuaWlkOkM1QkE2NjlFNTU0QkUzMTE5QzhBQzYwMDA0NDNEREFDIiBzdFJlZjpkb2N1bWVudElEPSJ4bXAuZGlkOkMyQkE2NjlFNTU0QkUzMTE5QzhBQzYwMDA0NDNEREFDIi8+IDwvcmRmOkRlc2NyaXB0aW9uPiA8L3JkZjpSREY+IDwveDp4bXBtZXRhPiA8P3hwYWNrZXQgZW5kPSJyIj8+Af/+/fz7+vn49/b19PPy8fDv7u3s6+rp6Ofm5eTj4uHg397d3Nva2djX1tXU09LR0M/OzczLysnIx8bFxMPCwcC/vr28u7q5uLe2tbSzsrGwr66trKuqqainpqWko6KhoJ+enZybmpmYl5aVlJOSkZCPjo2Mi4qJiIeGhYSDgoGAf359fHt6eXh3dnV0c3JxcG9ubWxramloZ2ZlZGNiYWBfXl1cW1pZWFdWVVRTUlFQT05NTEtKSUhHRkVEQ0JBQD8+PTw7Ojk4NzY1NDMyMTAvLi0sKyopKCcmJSQjIiEgHx4dHBsaGRgXFhUUExIREA8ODQwLCgkIBwYFBAMCAQAAIfkECQcABwAsAAAAACIAFQAAA6J4umv+MDpG6zEj682zsRaWFWRpltoHMuJZCCRseis7xG5eDGp93bqCA7f7TFaYoIFAMMwczB5EkTzJllEUttmIGoG5bfPBjDawD7CsJC67uWcv2CRov929C/q2ZpcBbYBmLGk6W1BRY4MUDnMvJEsBAXdlknk2fCeRk2iJliAijpBlEmigjR0plKSgpKWvEUheF4tUZqZID1RHjEe8PsDBBwkAIfkECQcABwAsAAAAACIAFQAAA6B4umv+MDpG6zEj682zsRaWFWRpltoHMuJZCCRseis7xG5eDGp93TqS40XiKSYgTLBgIBAMqE/zmQSaZEzns+jQ9pC/5dQJ0VIv5KMVWxqb36opxHrNvu9ptPfGbmsBbgSAeRdydCdjXWRPchQPh1hNAQF4TpM9NnwukpRyi5chGjqJEoSOIh0plaYsZBKvsCuNjY5ptElgDyFIuj6+vwcJACH5BAkHAAcALAAAAAAiABUAAAOfeLrc/vCZSaudUY7Nu99GxhhcYZ7oyYXiQQ5pIZgzCrYuLMd8MbAiUu802flYGIhwaCAQDKpQ86nUoWqF6dP00wIby572SXE6vyMrlmhuu9GKifWaddvNQAtszXYCxgR/Zy5jYTFeXmSDiIZGdQEBd06QSBQ5e4cEkE9nnZQaG2J4F4MSLx8rkqUSZBeurhlTUqsLsi60DpZxSWBJugcJACH5BAkHAAcALAAAAAAiABUAAAOgeLrc/vCZSaudUY7Nu99GxhhcYZ7oyYXiQQ5pIZgzCrYuLMd8MbAiUu802flYGIhwaCAQDKpQ86nUoWqF6dP00wIby572SXE6vyMrlmhuu9GuifWaddvNwMkZtmY7AWMEgGcKY2ExXl5khFMVc0Z1AQF3TpJShDl8iASST2efloV5JTyJFpgOch8dgW9KZxexshGNLqgLtbW0SXFwvaJfCQAh+QQJBwAHACwAAAAAIgAVAAADoXi63P7wmUmrnVGOzbvfRsYYXGGe6MmF4kEOaSGYMwq2LizHfDGwIlLPNKGZfi6gZmggEAy2iVPZEKZqzakq+1xUFFYe90lxTsHmim6HGpvf3eR7skYJ3PC5tyystc0AboFnVXQ9XFJTZIQOYUYFTQEBeWaSVF4bbCeRk1meBJYSL3WbaReMIxQfHXh6jaYXsbEQni6oaF21ERR7l0ksvA0JACH5BAkHAAcALAAAAAAiABUAAAOeeLrc/vCZSaudUY7Nu99GxhhcYZ7oyYXiQQ5pIZgzCrYuLMfFlA4hTITEMxkIBMOuADwmhzqeM6mashTCXKw2TVKQyKuTRSx2wegnNkyJ1ozpOFiMLqcEU8BZHx6NYW8nVlZefQ1tZgQBAXJIi1eHUTRwi0lhl48QL0sogxaGDhMlUo2gh14fHhcVmnOrrxNqrU9joX21Q0IUElm7DQkAIfkECQcABwAsAAAAACIAFQAAA6J4umv+MDpG6zEj682zsRaWFWRpltoHMuJZCCRseis7xG5eDGp93bqCA7f7TFaYoIFAMMwczB5EkTzJllEUttmIGoG5bfPBjDawD7CsJC67uWcv2CRov929C/q2ZpcBbYBmLGk6W1BRY4MUDnMvJEsBAXdlknk2fCeRk2iJliAijpBlEmigjR0plKSgpKWvEUheF4tUZqZID1RHjEe8PsDBBwkAIfkECQcABwAsAAAAACIAFQAAA6B4umv+MDpG6zEj682zsRaWFWRpltoHMuJZCCRseis7xG5eDGp93TqS40XiKSYgTLBgIBAMqE/zmQSaZEzns+jQ9pC/5dQJ0VIv5KMVWxqb36opxHrNvu9ptPfGbmsBbgSAeRdydCdjXWRPchQPh1hNAQF4TpM9NnwukpRyi5chGjqJEoSOIh0plaYsZBKvsCuNjY5ptElgDyFIuj6+vwcJACH5BAkHAAcALAAAAAAiABUAAAOfeLrc/vCZSaudUY7Nu99GxhhcYZ7oyYXiQQ5pIZgzCrYuLMd8MbAiUu802flYGIhwaCAQDKpQ86nUoWqF6dP00wIby572SXE6vyMrlmhuu9GKifWaddvNQAtszXYCxgR/Zy5jYTFeXmSDiIZGdQEBd06QSBQ5e4cEkE9nnZQaG2J4F4MSLx8rkqUSZBeurhlTUqsLsi60DpZxSWBJugcJACH5BAkHAAcALAAAAAAiABUAAAOgeLrc/vCZSaudUY7Nu99GxhhcYZ7oyYXiQQ5pIZgzCrYuLMd8MbAiUu802flYGIhwaCAQDKpQ86nUoWqF6dP00wIby572SXE6vyMrlmhuu9GuifWaddvNwMkZtmY7AWMEgGcKY2ExXl5khFMVc0Z1AQF3TpJShDl8iASST2efloV5JTyJFpgOch8dgW9KZxexshGNLqgLtbW0SXFwvaJfCQAh+QQJBwAHACwAAAAAIgAVAAADoXi63P7wmUmrnVGOzbvfRsYYXGGe6MmF4kEOaSGYMwq2LizHfDGwIlLPNKGZfi6gZmggEAy2iVPZEKZqzakq+1xUFFYe90lxTsHmim6HGpvf3eR7skYJ3PC5tyystc0AboFnVXQ9XFJTZIQOYUYFTQEBeWaSVF4bbCeRk1meBJYSL3WbaReMIxQfHXh6jaYXsbEQni6oaF21ERR7l0ksvA0JACH5BAkHAAcALAAAAAAiABUAAAOeeLrc/vCZSaudUY7Nu99GxhhcYZ7oyYXiQQ5pIZgzCrYuLMfFlA4hTITEMxkIBMOuADwmhzqeM6mashTCXKw2TVKQyKuTRSx2wegnNkyJ1ozpOFiMLqcEU8BZHx6NYW8nVlZefQ1tZgQBAXJIi1eHUTRwi0lhl48QL0sogxaGDhMlUo2gh14fHhcVmnOrrxNqrU9joX21Q0IUElm7DQkAOw==") ;
  background-size: contain;
  background-repeat: no-repeat;
  z-index: 1;    /* 确保图片显示在上层 */
}

/* Components-dynamicDataView-progress-bar-style-soft */
.Components-dynamicDataView-progress-bar-style-soft .components--component-dynamicDataView .components--progress-bar-track,
.Components-dynamicDataView-progress-bar-style-soft .components--component-dynamicDataView .components--PageTaskProgressBar:not(.components--PageTaskProgressBar_none) {
  background-color: var(--background-secondary);
  background: var(--text-selection);
  width: 100%;
  height: 12px;
  overflow: visible;
}
.Components-dynamicDataView-progress-bar-style-soft .components--component-dynamicDataView .components--progress-bar-track .components--progress-bar-value,
.Components-dynamicDataView-progress-bar-style-soft .components--component-dynamicDataView .components--PageTaskProgressItem.components--PageTaskProgressItem_completed {
  background: linear-gradient(to bottom, var(--color-red) 0%, var(--color-orange) 16.5%, var(--color-yellow) 33%, var(--color-green) 50%, var(--color-cyan) 66%, var(--color-purple) 83.5%, var(--color-red) 100%);
  overflow: visible;
}
.Components-dynamicDataView-progress-bar-style-soft .components--component-dynamicDataView .components--progress-bar-track .components--progress-bar-value {
  border-radius: var(--radius-s);
}
.Components-dynamicDataView-progress-bar-style-soft .components--component-dynamicDataView .components--progress-bar-label {
  margin-left: 12px;
}
.Components-dynamicDataView-progress-bar-style-soft .components--component-dynamicDataView .components--PageTaskProgressBar {
  gap: 0px;

  .components--PageTaskProgressItem.components--PageTaskProgressItem_completed {
    border-radius: 0px;
  }

  .components--PageTaskProgressItem.components--PageTaskProgressItem_completed:first-child {
    border-radius: var(--radius-s) 0 0 var(--radius-s);
  }
}
.Components-dynamicDataView-progress-bar-style-soft .components--component-dynamicDataView .components--progress-bar-value::after,
.Components-dynamicDataView-progress-bar-style-soft .components--component-dynamicDataView .components--PageTaskProgressBar .components--PageTaskProgressItem_completed:not(:has(+ .components--PageTaskProgressItem_completed))::after {
  content: '';
  display: flex;
  position: relative;
  left: 100%;
  top: 50%;      /* 垂直居中 */
  transform: translate(-50%, -70%);
  width: 34px;   /* 图片宽度 */
  height: 21px;  /* 图片高度 */
  background-image: url("data:image/png;base64,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") ;
  background-size: contain;
  background-repeat: no-repeat;
  z-index: 1;    /* 确保图片显示在上层 */
}

/* components--progress-ring */
.Components-dynamicDataView-progress-ring-progress-indicator-hide .components--component-dynamicDataView .components--progress-ring .progress-indicator{
  /* display: none; */
  visibility: hidden;
}

/* Components--component-attachments-settings */
.components--component-attachments .components--ImageAttachmentsItemContainer {
  margin: auto;
  display: block;
  border: 1px solid var(--Components--component-attachments-border-color);
  border-radius: var(--Components--component-attachments-border-radius);
}
.Components--component-attachments-border-shadow .components--component-attachments .components--ImageAttachmentsItemContainer {
  box-shadow: var(--Components--component-attachments-border-shadow-offset-x) var(--Components--component-attachments-border-shadow-offset-y) var(--Components--component-attachments-border-shadow-blur-radius) var(--Components--component-attachments-border-shadow-spread-radius) var(--Components--component-attachments-border-shadow-color)
}

/* Components--component-colorSet-settings */
.theme-light.Components--component-colorSet-enable, 
.theme-dark.Components--component-colorSet-enable {
  .components--color-light-gray {
    background-color: var(--Components--component-colorSet-light-gray-bgColor);
    color: var(--Components--component-colorSet-light-gray-fontColor);
  }
  .components--color-gray {
    background-color: var(--Components--component-colorSet-gray-bgColor);
    color: var(--Components--component-colorSet-gray-fontColor);
  }
  .components--color-orange {
    background-color: var(--Components--component-colorSet-orange-bgColor);
    color: var(--Components--component-colorSet-orange-fontColor);
  }
  .components--color-yellow {
    background-color: var(--Components--component-colorSet-yellow-bgColor);
    color: var(--Components--component-colorSet-yellow-fontColor);
  }
  .components--color-green {
    background-color: var(--Components--component-colorSet-green-bgColor);
    color: var(--Components--component-colorSet-green-fontColor);
  }
  .components--color-blue {
    background-color: var(--Components--component-colorSet-blue-bgColor);
    color: var(--Components--component-colorSet-blue-fontColor);
  }
  .components--color-purple {
    background-color: var(--Components--component-colorSet-purple-bgColor);
    color: var(--Components--component-colorSet-purple-fontColor);
  }
  .components--color-pink {
    background-color: var(--Components--component-colorSet-pink-bgColor);
    color: var(--Components--component-colorSet-pink-fontColor);
  }
  .components--color-red {
    background-color: var(--Components--component-colorSet-red-bgColor);
    color: var(--Components--component-colorSet-red-fontColor);
  }
}

.condition-item-width-fix{
  .components--Component,
  .components--floating-container,
  .components--Dialog {
    .condition-item-container,
    .components--filters-item  {
      display: flex;

      .condition-item {
        width: 25%;
      }

      .components--FilterJoiner {
        width: 15%;
      }
    }

    .condition-container {
      width: 100%;
    }
  }
}

.dynamicDataView-margin-top-fix {
  .components--DynamicDataView-Button,
  .components--tag {
    margin-block-start: 2px;
  }
}