# 默写本—数据合规里面的日志审计。啊为什么要做日志审计_原子笔记索引

> 本索引由原子笔记切分工具自动生成
> 生成时间: 2025/7/30 12:07:08
> 原始笔记: [[默写本—数据合规里面的日志审计。啊为什么要做日志审计]]

## 统计信息
- 原始笔记: [[默写本—数据合规里面的日志审计。啊为什么要做日志审计]]
- 切分出的原子笔记数量: 8
- 生成时间: 2025/7/30 12:07:08

## 原子笔记列表

1. [[日志审计的重要性]] - 日志审计的重要性
2. [[日志审计解决的问题-防止数据被窃取，避免合法违规操作，并在监管部门检查时证明数据保护的义务]] - 日志审计解决的问题
3. [[日志审计的核心操作-追踪敏感数据访问、监控数据流向、验证数据删除和审查权限]] - 日志审计的核心操作
4. [[日志审计案例分析-电商平台未删除用户数据被罚款，医疗APP违规收集人脸数据被罚款]] - 日志审计案例分析
5. [[日志审计的自查方法-建立自查清单、定期审查权限和数据传输、核对备份日志]] - 日志审计的自查方法
6. [[日志审计工具-数据血缘图谱和自动化监控体系可以帮助追踪数据生命周期和实时发现异常访问]] - 日志审计工具
7. [[日志审计的心理障碍与能力短板]] - 日志审计的心理障碍与能力短板
8. [[数据合规的核心职责和价值]] - 数据合规的核心职责和价值

## 标签分类

### #日志审计
- [[日志审计的重要性]]
- [[日志审计解决的问题-防止数据被窃取，避免合法违规操作，并在监管部门检查时证明数据保护的义务]]
- [[日志审计的核心操作-追踪敏感数据访问、监控数据流向、验证数据删除和审查权限]]
- [[日志审计案例分析-电商平台未删除用户数据被罚款，医疗APP违规收集人脸数据被罚款]]
- [[日志审计的自查方法-建立自查清单、定期审查权限和数据传输、核对备份日志]]
- [[日志审计工具-数据血缘图谱和自动化监控体系可以帮助追踪数据生命周期和实时发现异常访问]]
- [[日志审计的心理障碍与能力短板]]

### #数据安全
- [[日志审计的重要性]]
- [[数据合规的核心职责和价值]]

### #合规
- [[日志审计的重要性]]

### #数据保护
- [[日志审计解决的问题-防止数据被窃取，避免合法违规操作，并在监管部门检查时证明数据保护的义务]]

### #合规义务
- [[日志审计解决的问题-防止数据被窃取，避免合法违规操作，并在监管部门检查时证明数据保护的义务]]

### #数据操作
- [[日志审计的核心操作-追踪敏感数据访问、监控数据流向、验证数据删除和审查权限]]

### #权限审查
- [[日志审计的核心操作-追踪敏感数据访问、监控数据流向、验证数据删除和审查权限]]

### #案例分析
- [[日志审计案例分析-电商平台未删除用户数据被罚款，医疗APP违规收集人脸数据被罚款]]

### #数据合规
- [[日志审计案例分析-电商平台未删除用户数据被罚款，医疗APP违规收集人脸数据被罚款]]
- [[日志审计的自查方法-建立自查清单、定期审查权限和数据传输、核对备份日志]]
- [[日志审计工具-数据血缘图谱和自动化监控体系可以帮助追踪数据生命周期和实时发现异常访问]]
- [[数据合规的核心职责和价值]]

### #自查方法
- [[日志审计的自查方法-建立自查清单、定期审查权限和数据传输、核对备份日志]]

### #工具
- [[日志审计工具-数据血缘图谱和自动化监控体系可以帮助追踪数据生命周期和实时发现异常访问]]

### #心理障碍
- [[日志审计的心理障碍与能力短板]]

### #能力短板
- [[日志审计的心理障碍与能力短板]]

### #核心职责
- [[数据合规的核心职责和价值]]

---
*此索引文件由原子笔记切分工具生成*
