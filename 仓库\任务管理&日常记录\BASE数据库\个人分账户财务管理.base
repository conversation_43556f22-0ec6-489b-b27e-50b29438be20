filters:
  and:
    - file.inFolder("BASE数据库/个人财务数据")
    - file.ext == "md"
formulas:
  blue_bag_total: if(living_expense && emergency_fund && utilities, living_expense + emergency_fund + utilities, 0)
  green_bag_total: if(travel_fund && social_fund && insurance, travel_fund + social_fund + insurance, 0)
  yellow_bag_total: if(fixed_deposit && investment_fund, fixed_deposit + investment_fund, 0)
  rent_months_left: if(rent_reserve && monthly_rent, (rent_reserve / monthly_rent).toFixed(1) + "个月", "未知")
  salary_months_left: if(salary_reserve && monthly_salary, (salary_reserve / monthly_salary).toFixed(1) + "个月", "未知")
  marketing_budget_left: if(marketing_reserve && monthly_marketing, (marketing_reserve / monthly_marketing).toFixed(1) + "个月", "未知")
  blue_bag_status: if(formula.blue_bag_total >= target_blue_amount, "✅ 充足", if(formula.blue_bag_total >= target_blue_amount * 0.7, "⚠️ 注意", "❗ 不足"))
  green_bag_status: if(formula.green_bag_total >= target_green_amount, "✅ 充足", if(formula.green_bag_total >= target_green_amount * 0.7, "⚠️ 注意", "❗ 不足"))
  yellow_bag_status: if(formula.yellow_bag_total >= target_yellow_amount, "✅ 充足", if(formula.yellow_bag_total >= target_yellow_amount * 0.7, "⚠️ 注意", "❗ 不足"))
  savings_rate: if(monthly_income && total_savings, (total_savings / monthly_income * 100).toFixed(1) + "%", "0%")
  freedom_fund_progress: if(freedom_fund_target && freedom_fund_current, (freedom_fund_current / freedom_fund_target * 100).toFixed(1) + "%", "0%")
  emergency_level: if(emergency_type == "垫资接单", if(formula.green_bag_total >= amount * 0.5, "🟢 可处理", "🔴 资金不足"), if(emergency_type == "突发疾病", if(formula.blue_bag_total >= amount, "🟢 可处理", "🔴 资金不足"), "⚪ 待评估"))
  monthly_surplus: if(monthly_income && monthly_expense, monthly_income - monthly_expense, 0)
  allocation_efficiency: if(planned_allocation && actual_allocation, (actual_allocation / planned_allocation * 100).toFixed(1) + "%", "未计算")
properties:
  record_type:
    displayName: 记录类型
  date:
    displayName: 日期
  month:
    displayName: 月份
  monthly_income:
    displayName: 月收入(元)
  income_source:
    displayName: 收入来源
  income_stability:
    displayName: 收入稳定性
  bag_color:
    displayName: 资金袋颜色
  living_expense:
    displayName: 生活费(元)
  emergency_fund:
    displayName: 备用金(元)
  utilities:
    displayName: 水电物业(元)
  travel_fund:
    displayName: 旅游基金(元)
  social_fund:
    displayName: 人情往来(元)
  insurance:
    displayName: 保险费(元)
  fixed_deposit:
    displayName: 定期存款(元)
  investment_fund:
    displayName: 投资基金(元)
  formula.blue_bag_total:
    displayName: 🔵蓝色袋总额
  formula.green_bag_total:
    displayName: 🟢绿色袋总额
  formula.yellow_bag_total:
    displayName: 🟡黄色袋总额
  target_blue_amount:
    displayName: 蓝色袋目标(元)
  target_green_amount:
    displayName: 绿色袋目标(元)
  target_yellow_amount:
    displayName: 黄色袋目标(元)
  formula.blue_bag_status:
    displayName: 🔵蓝色袋状态
  formula.green_bag_status:
    displayName: 🟢绿色袋状态
  formula.yellow_bag_status:
    displayName: 🟡黄色袋状态
  medicine_box_type:
    displayName: 药箱格子类型
  rent_reserve:
    displayName: 💊房租储备(元)
  salary_reserve:
    displayName: 💉工资储备(元)
  marketing_reserve:
    displayName: 🧪推广储备(元)
  tax_reserve:
    displayName: 🔬税收储备(元)
  profit_reserve:
    displayName: 💰利润储备(元)
  monthly_rent:
    displayName: 月租金(元)
  monthly_salary:
    displayName: 月工资支出(元)
  monthly_marketing:
    displayName: 月推广支出(元)
  formula.rent_months_left:
    displayName: 房租可用月数
  formula.salary_months_left:
    displayName: 工资可用月数
  formula.marketing_budget_left:
    displayName: 推广预算月数
  freedom_fund_current:
    displayName: 赎身基金现值(元)
  freedom_fund_target:
    displayName: 赎身基金目标(元)
  formula.freedom_fund_progress:
    displayName: 自由基金进度
  emergency_type:
    displayName: 应急类型
  amount:
    displayName: 所需金额(元)
  formula.emergency_level:
    displayName: 应急处理能力
  solution:
    displayName: 解决方案
  monthly_expense:
    displayName: 月支出(元)
  total_savings:
    displayName: 总储蓄(元)
  planned_allocation:
    displayName: 计划分配(元)
  actual_allocation:
    displayName: 实际分配(元)
  formula.monthly_surplus:
    displayName: 月度结余(元)
  formula.savings_rate:
    displayName: 储蓄率
  formula.allocation_efficiency:
    displayName: 分配执行率
  notes:
    displayName: 备注
  action_needed:
    displayName: 需要行动
views:
  - type: table
    name: 三色资金袋总览
    filters:
      and:
        - file.folder == "任务管理&日常记录/财务管理系统/个人财务数据"
    order:
      - bag_color
      - formula.blue_bag_total
      - formula.green_bag_total
      - formula.yellow_bag_total
      - formula.blue_bag_status
      - formula.green_bag_status
      - formula.yellow_bag_status
      - target_blue_amount
      - target_green_amount
      - target_yellow_amount
    sort:
      - property: bag_color
        direction: DESC
      - property: date
        direction: DESC
    limit: 20
  - type: cards
    name: 工作室药箱监控
    filters:
      and:
        - record_type == "工作室药箱"
    order:
      - medicine_box_type
      - rent_reserve
      - salary_reserve
      - marketing_reserve
      - tax_reserve
      - profit_reserve
      - formula.rent_months_left
      - formula.salary_months_left
    sort:
      - property: date
        direction: DESC
    limit: 15
  - type: table
    name: 月度财务分析
    filters:
      and:
        - record_type == "月度分析"
    order:
      - month
      - monthly_income
      - monthly_expense
      - formula.monthly_surplus
      - total_savings
      - formula.savings_rate
      - formula.allocation_efficiency
    sort:
      - property: month
        direction: DESC
    limit: 12
  - type: cards
    name: 预警提醒
    filters:
      or:
        - formula.blue_bag_status == "❗ 不足"
        - formula.green_bag_status == "❗ 不足"
        - formula.yellow_bag_status == "❗ 不足"
        - formula.rent_months_left < 3
        - formula.salary_months_left < 3
    order:
      - record_type
      - formula.blue_bag_status
      - formula.green_bag_status
      - formula.yellow_bag_status
      - formula.rent_months_left
      - action_needed
    sort:
      - property: date
        direction: DESC
    limit: 10
  - type: table
    name: 自由基金追踪
    filters:
      and:
        - record_type == "自由基金"
    order:
      - date
      - freedom_fund_current
      - freedom_fund_target
      - formula.freedom_fund_progress
      - monthly_income
      - notes
    sort:
      - property: date
        direction: DESC
    limit: 24
  - type: table
    name: 应急处理记录
    filters:
      and:
        - record_type == "应急处理"
    order:
      - date
      - emergency_type
      - amount
      - formula.emergency_level
      - solution
      - notes
    sort:
      - property: date
        direction: DESC
    limit: 20
  - type: cards
    name: 储蓄目标达成
    filters:
      and:
        - record_type == "月度分析"
        - formula.savings_rate >= "15%"
    order:
      - month
      - formula.savings_rate
      - total_savings
      - formula.monthly_surplus
    sort:
      - property: formula.savings_rate
        direction: DESC
    limit: 12
