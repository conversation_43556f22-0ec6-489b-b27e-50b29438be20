---
title: "自建方案安全性"
source: "[[端到端加密（E2EE）你的笔记在离开设备前就被加密，只有你的设备能解密]]"
tags: ["自建方案", "安全性", "LiveSync"]
keywords: ["自建方案", "数据保护", "安全性"]
created: 2025-08-02
type: 原子笔记
---

# 自建方案安全性

#### 自建方案（如LiveSync）为什么安全？

##### 核心保障

- **数据完全私有**：你的笔记只存在你自己的服务器（如家里的NAS/租的云主机），**没有第三方经手**。
- **加密自主权**：你可以用更狠的加密工具（如`Veracrypt`全盘加密），连服务器管理员都看不懂。
- **网络隔离**：自建服务器可以关闭所有外网端口，只允许你的设备连接。

##### 举个栗子

> 你把笔记锁在自家保险箱（自建服务器），钥匙你拿着，连造保险箱的厂商都进不去。



---

## 元信息
- **来源笔记**: [[端到端加密（E2EE）你的笔记在离开设备前就被加密，只有你的设备能解密]]
- **创建时间**: 2025/8/2 20:32:29
- **标签**: #自建方案 #安全性 #LiveSync
- **关键词**: 自建方案, 数据保护, 安全性

## 相关链接
- 返回原笔记: [[端到端加密（E2EE）你的笔记在离开设备前就被加密，只有你的设备能解密]]
