# 改进版大纲插件

一个功能强大的 Obsidian 大纲插件，能够识别和显示多种标题格式，提供更好的笔记结构导航体验。

## 功能特点

### 🎯 多格式标题识别
- **标准 Markdown 标题**: `# ## ### #### ##### ######`
- **Setext 风格标题**: 使用 `=` 和 `-` 下划线
- **HTML 标题标签**: `<h1>` 到 `<h6>`
- **自定义格式**: `【标题】` 或 `[标题]`
- **数字编号**: `1. 标题内容`
- **粗体标题**: `**标题内容**`

### 🚀 增强功能
- **实时更新**: 文档内容变化时自动刷新大纲
- **智能跳转**: 点击大纲项目直接跳转到对应位置
- **行号显示**: 显示每个标题所在的行号
- **类型标识**: 用图标区分不同类型的标题
- **层级缩进**: 清晰的视觉层级结构
- **悬停效果**: 鼠标悬停时的视觉反馈

### 🎨 界面优化
- **现代化设计**: 简洁美观的界面
- **主题适配**: 支持亮色和暗色主题
- **响应式布局**: 适配不同屏幕尺寸
- **高对比度支持**: 无障碍访问优化
- **平滑动画**: 流畅的交互体验

## 使用方法

### 激活插件
1. 插件安装后会自动在右侧边栏显示大纲视图
2. 点击左侧功能区的列表图标可以打开/关闭大纲
3. 使用命令面板搜索"打开改进版大纲"命令

### 导航操作
- **点击标题**: 跳转到文档中的对应位置
- **查看行号**: 了解标题在文档中的具体位置
- **识别类型**: 通过颜色标识区分不同类型的标题

## 支持的标题格式示例

```markdown
# 一级标题 (Markdown)
## 二级标题 (Markdown)

一级标题 (Setext)
================

二级标题 (Setext)
----------------

<h3>三级标题 (HTML)</h3>

【自定义标题格式】
[另一种自定义格式]

1. 数字编号标题
2. 另一个编号标题

**粗体标题**
```

## 技术特性

- **高性能**: 优化的解析算法，快速处理大型文档
- **内存友好**: 智能缓存机制，减少资源占用
- **兼容性强**: 支持 Obsidian 0.15.0 及以上版本
- **扩展性好**: 模块化设计，易于添加新功能

## 版本历史

### v1.0.0
- 初始版本发布
- 支持多种标题格式识别
- 实现基础大纲显示功能
- 添加点击跳转功能
- 优化界面设计

## 开发者信息

- **作者**: 范秋萍
- **版本**: 1.0.0
- **最低支持版本**: Obsidian 0.15.0

## 反馈与支持

如果您在使用过程中遇到问题或有改进建议，欢迎反馈。这个插件旨在提供更好的笔记大纲体验，让您的知识管理更加高效。