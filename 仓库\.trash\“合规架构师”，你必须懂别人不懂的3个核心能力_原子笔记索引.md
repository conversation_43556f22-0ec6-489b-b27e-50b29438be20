# “合规架构师”，你必须懂别人不懂的3个核心能力_原子笔记索引

> 本索引由原子笔记切分工具自动生成
> 生成时间: 2025/7/30 11:58:46
> 原始笔记: [[“合规架构师”，你必须懂别人不懂的3个核心能力]]

## 统计信息
- 原始笔记: [[“合规架构师”，你必须懂别人不懂的3个核心能力]]
- 切分出的原子笔记数量: 8
- 生成时间: 2025/7/30 11:58:46

## 原子笔记列表

1. [[合规架构师核心能力1：理解监管执法逻辑]] - 合规架构师核心能力1：理解监管执法逻辑
2. [[合规架构师核心能力2：技术+法律交叉方案]] - 合规架构师核心能力2：技术+法律交叉方案
3. [[合规架构师核心能力3：商业平衡术]] - 合规架构师核心能力3：商业平衡术
4. [[合规架构师实战技巧：风险转移法]] - 合规架构师实战技巧：风险转移法
5. [[合规架构师实战技巧：数据脱敏法]] - 合规架构师实战技巧：数据脱敏法
6. [[合规架构师实战技巧：权限隔离法]] - 合规架构师实战技巧：权限隔离法
7. [[合规架构师实战技巧：协议补丁法]] - 合规架构师实战技巧：协议补丁法
8. [[合规架构师实战技巧：主动报备法]] - 合规架构师实战技巧：主动报备法

## 标签分类

### #合规架构师
- [[合规架构师核心能力1：理解监管执法逻辑]]
- [[合规架构师核心能力2：技术+法律交叉方案]]
- [[合规架构师核心能力3：商业平衡术]]
- [[合规架构师实战技巧：风险转移法]]
- [[合规架构师实战技巧：数据脱敏法]]
- [[合规架构师实战技巧：权限隔离法]]
- [[合规架构师实战技巧：协议补丁法]]
- [[合规架构师实战技巧：主动报备法]]

### #监管执法
- [[合规架构师核心能力1：理解监管执法逻辑]]

### #监管案例
- [[合规架构师核心能力1：理解监管执法逻辑]]

### #技术+法律
- [[合规架构师核心能力2：技术+法律交叉方案]]

### #合规工具
- [[合规架构师核心能力2：技术+法律交叉方案]]

### #商业平衡
- [[合规架构师核心能力3：商业平衡术]]

### #风险分级
- [[合规架构师核心能力3：商业平衡术]]

### #风险转移
- [[合规架构师实战技巧：风险转移法]]

### #用户告知
- [[合规架构师实战技巧：风险转移法]]

### #数据脱敏
- [[合规架构师实战技巧：数据脱敏法]]

### #最小必要原则
- [[合规架构师实战技巧：数据脱敏法]]

### #权限隔离
- [[合规架构师实战技巧：权限隔离法]]

### #IAM系统
- [[合规架构师实战技巧：权限隔离法]]

### #协议补丁
- [[合规架构师实战技巧：协议补丁法]]

### #数据跨境
- [[合规架构师实战技巧：协议补丁法]]

### #主动报备
- [[合规架构师实战技巧：主动报备法]]

### #自查报告
- [[合规架构师实战技巧：主动报备法]]

---
*此索引文件由原子笔记切分工具生成*
