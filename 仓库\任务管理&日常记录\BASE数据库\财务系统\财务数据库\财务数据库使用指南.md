# 财务数据库使用指南

## 概述
基于你的财务模板需求，我创建了两个专业的BASE数据库系统：
1. **数据合规工作室财务管理** - 专注于工作室经营分析
2. **个人分账户财务管理** - 基于三色资金袋+五格药箱系统

## 数据库文件位置
```
BASE数据库/
├── 数据合规工作室财务管理.base     # 工作室财务数据库
├── 个人分账户财务管理.base         # 个人财务数据库
├── 工作室财务数据/                 # 工作室数据文件夹
│   ├── A公司GDPR合规项目.md
│   ├── 张三顾问人效分析.md
│   └── 2024年4月现金流预测.md
└── 个人财务数据/                   # 个人数据文件夹
    ├── 2024年3月三色资金袋.md
    ├── 工作室五格药箱监控.md
    └── 赎身基金追踪.md
```

## 🏢 数据合规工作室财务管理

### 核心功能
基于你的工作室财务模板，包含3个核心分析表：

#### 1. 项目利润分析表
- **自动计算**: 项目毛利率、应收款、逾期天数
- **关键指标**: 人效比、客户风险等级
- **预警功能**: 逾期项目自动标红

#### 2. 人力效率监控表  
- **自动计算**: 顾问利用率、闲置率、人效比
- **预警标准**: 人效比<5自动预警
- **优化建议**: 基于数据的人力配置建议

#### 3. 现金流预警表
- **自动计算**: 资金可用月数、现金流状态
- **预警机制**: 低于3个月储备自动预警
- **行动指引**: 具体的现金流改善建议

### 主要视图
1. **项目利润分析** - 按毛利率排序的项目列表
2. **人力效率监控** - 按人效比排序的顾问分析
3. **现金流预警** - 按月份的现金流预测
4. **高风险项目** - 逾期项目卡片视图
5. **客户价值分析** - 客户价值评分排序
6. **低效顾问预警** - 人效比<5的顾问提醒

## 👤 个人分账户财务管理

### 核心功能
基于你的分账户存钱模板，实现双系统管理：

#### 三色资金袋系统（个人）
- **🔵 蓝色袋**: 生存资金（生活费+备用金+水电）
- **🟢 绿色袋**: 年度目标（旅游+人情+保险）  
- **🟡 黄色袋**: 财务自由燃料（定期+投资）

#### 五格药箱系统（工作室）
- **💊 抗生素**: 房租储备（3个月房租）
- **💉 止血剂**: 工资储备（3个月工资）
- **🧪 营养液**: 推广储备（营销预算）
- **🔬 显微镜**: 税收储备（税费预留）
- **💰 益生菌**: 利润储备（超额回流个人）

### 主要视图
1. **三色资金袋总览** - 个人资金分配状况
2. **工作室药箱监控** - 工作室资金储备情况
3. **月度财务分析** - 收支分析和储蓄率
4. **预警提醒** - 资金不足的预警卡片
5. **自由基金追踪** - 赎身基金进度追踪
6. **应急处理记录** - 紧急情况处理记录

## 🚀 快速开始

### 1. 嵌入数据库到笔记
在任何笔记中使用以下语法嵌入数据库：

```markdown
# 工作室财务仪表盘
![[数据合规工作室财务管理.base]]

# 个人财务总览  
![[个人分账户财务管理.base]]
```

### 2. 添加新数据
在对应的数据文件夹中创建新的Markdown文件，按照示例格式填写frontmatter属性。

#### 工作室数据示例
```yaml
---
record_type: "项目"  # 项目/人力/现金流/客户
project_name: "新项目名称"
client_type: "外企"  # 外企/国企/民企
contract_amount: 30  # 合同金额(万元)
received_amount: 10  # 已收款(万元)
human_cost: 8       # 人力成本(万元)
is_overdue: false   # 是否逾期
---
```

#### 个人数据示例
```yaml
---
record_type: "个人资金袋"  # 个人资金袋/工作室药箱/月度分析
living_expense: 1000      # 生活费
emergency_fund: 500       # 备用金
utilities: 400           # 水电物业
target_blue_amount: 1900 # 蓝色袋目标
---
```

### 3. 查看分析结果
- 打开对应的.base文件查看数据库视图
- 切换不同视图查看不同维度的分析
- 利用自动计算的公式字段进行决策

## 📊 关键指标说明

### 工作室关键指标
- **项目毛利率**: >40%健康，<30%需要调整定价
- **人效比**: >7健康，<5需要优化
- **现金流月数**: >3个月安全，<1个月危险
- **客户风险等级**: 基于付款历史和账期评估

### 个人关键指标  
- **储蓄率**: >27%优秀，15-27%健康，<15%需要改善
- **资金袋状态**: 绿灯充足，黄灯注意，红灯不足
- **自由基金进度**: 目标108,000元，当前进度追踪
- **应急处理能力**: 基于资金袋余额评估

## 🔧 自定义配置

### 修改预警阈值
在.base文件的formulas部分修改计算公式：

```yaml
formulas:
  # 修改人效比预警线（当前为5）
  consultant_efficiency: 'if(revenue_generated && human_cost, (revenue_generated / human_cost).toFixed(1), "0")'
  
  # 修改现金流预警线（当前为3个月）
  months_runway: 'if(monthly_expense && cash_balance, (cash_balance / monthly_expense).toFixed(1) + "个月", "未知")'
```

### 添加新的视图
在views部分添加新的视图配置：

```yaml
views:
  - type: table
    name: "自定义视图名称"
    filters:
      and:
        - 'record_type == "指定类型"'
    order:
      - 字段1
      - 字段2
    sort:
      - property: 排序字段
        direction: DESC
```

## 💡 使用技巧

1. **定期更新**: 建议每周更新项目数据，每月更新个人财务数据
2. **预警关注**: 重点关注红色预警项目，及时采取行动
3. **趋势分析**: 利用月度数据分析财务趋势变化
4. **决策支持**: 基于数据指标进行客户选择和资源配置
5. **目标追踪**: 定期检查自由基金等长期目标的进度

## 🆘 常见问题

**Q: 数据不显示怎么办？**
A: 检查文件是否在正确的文件夹中，frontmatter格式是否正确。

**Q: 公式计算错误怎么办？**  
A: 确保相关字段都有数值，空值会导致计算错误。

**Q: 如何备份数据？**
A: 定期导出CSV文件，或使用Obsidian的同步功能。

**Q: 可以修改字段名称吗？**
A: 可以在properties部分修改displayName，但不要修改实际字段名。
