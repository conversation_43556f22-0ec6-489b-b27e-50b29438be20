/* 提示词库插件样式 */

/* 主容器 */
.prompt-library-container {
    padding: 20px;
    max-width: 100%;
    margin: 0 auto;
    background: var(--background-primary);
    min-height: 100vh;
}

/* 头部样式 */
.prompt-library-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding-bottom: 10px;
    border-bottom: 1px solid var(--background-modifier-border);
}

.prompt-library-header h2 {
    margin: 0;
    color: var(--text-normal);
    font-size: 20px;
    font-weight: normal;
}

.prompt-header-buttons {
    display: flex;
    gap: 12px;
    align-items: center;
}

.prompt-add-btn {
    padding: 8px 16px;
    border: 1px solid var(--interactive-accent);
    border-radius: 4px;
    background: var(--interactive-accent);
    color: var(--text-on-accent);
    font-size: 14px;
    cursor: pointer;
    transition: all 0.2s ease;
}

.prompt-add-btn:hover {
    background: var(--interactive-accent-hover);
}

.prompt-ai-generate-btn {
    padding: 8px 16px;
    border: 1px solid var(--color-green);
    border-radius: 4px;
    background: var(--color-green);
    color: white;
    font-size: 14px;
    cursor: pointer;
    transition: all 0.2s ease;
}

.prompt-ai-generate-btn:hover {
    background: var(--color-green-hover);
    transform: translateY(-1px);
}

/* 搜索栏样式 */
.prompt-search-container {
    display: flex;
    gap: 12px;
    margin-bottom: 20px;
    align-items: center;
}

/* 标签筛选主容器 */
.prompt-tag-filter-main-container {
    margin-bottom: 16px;
}

.prompt-tag-filter-toggle-container {
    display: flex;
    justify-content: flex-end;
    margin-bottom: 8px;
}

.prompt-tag-filter-toggle-btn {
    padding: 8px 12px;
    border: 1px solid var(--background-modifier-border);
    border-radius: 6px;
    background: var(--background-secondary);
    color: var(--text-normal);
    cursor: pointer;
    transition: all 0.2s ease;
    font-size: 16px;
}

.prompt-tag-filter-toggle-btn:hover {
    background: var(--background-modifier-hover);
    border-color: var(--interactive-accent);
    transform: translateY(-1px);
}

.prompt-tag-filter-btn {
    padding: 10px 16px;
    border: 1px solid var(--background-modifier-border);
    border-radius: 8px;
    background: var(--background-primary);
    color: var(--text-normal);
    cursor: pointer;
    transition: all 0.2s ease;
    white-space: nowrap;
    font-size: 14px;
}

.prompt-tag-filter-btn:hover {
    background: var(--background-modifier-hover);
    border-color: var(--interactive-accent);
}

.prompt-search-input {
    flex: 1;
    padding: 10px 16px;
    border: 1px solid var(--background-modifier-border);
    border-radius: 8px;
    background: var(--background-primary);
    color: var(--text-normal);
    font-size: 14px;
    transition: border-color 0.2s ease;
}

.prompt-search-input:focus {
    outline: none;
    border-color: var(--interactive-accent);
    box-shadow: 0 0 0 2px var(--interactive-accent-hover);
}

.prompt-filter-btn {
    padding: 10px 16px;
    border: 1px solid var(--background-modifier-border);
    border-radius: 8px;
    background: var(--background-primary);
    color: var(--text-normal);
    cursor: pointer;
    transition: all 0.2s ease;
}

.prompt-filter-btn:hover {
    background: var(--background-modifier-hover);
    border-color: var(--interactive-accent);
}

/* 分类标签样式 */
.prompt-category-tabs {
    display: flex;
    gap: 8px;
    margin-bottom: 24px;
    overflow-x: auto;
    padding-bottom: 4px;
}

.prompt-tab {
    padding: 8px 16px;
    border-radius: 20px;
    background: var(--background-secondary);
    color: var(--text-muted);
    cursor: pointer;
    font-size: 14px;
    font-weight: 500;
    white-space: nowrap;
    transition: all 0.2s ease;
    border: 1px solid transparent;
}

.prompt-tab:hover {
    background: var(--background-modifier-hover);
    color: var(--text-normal);
}

.prompt-tab.active {
    background: var(--interactive-accent);
    color: var(--text-on-accent);
    border-color: var(--interactive-accent);
}

/* 提示词网格样式 */
.prompt-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 16px;
    margin-bottom: 20px;
}

/* 提示词卡片样式 */
.prompt-card {
    background: var(--background-secondary);
    border: 1px solid var(--background-modifier-border);
    border-radius: 6px;
    padding: 16px;
    cursor: pointer;
    position: relative;
}

.prompt-card:hover {
    border-color: var(--interactive-accent);
}

.prompt-card-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 12px;
}

.prompt-card-title {
    font-size: 16px;
    font-weight: 500;
    color: var(--text-normal);
    margin: 0 0 8px 0;
    line-height: 1.3;
}

.prompt-card-description {
    color: var(--text-muted);
    font-size: 13px;
    line-height: 1.4;
    margin-bottom: 12px;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

/* 标签样式 */
.prompt-card-tags {
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    gap: 8px;
    margin-bottom: 12px;
}

.prompt-card-tag {
    background: linear-gradient(135deg, var(--interactive-accent), var(--interactive-accent-hover));
    color: var(--text-on-accent);
    padding: 4px 10px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: 500;
    border: none;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    transition: all 0.2s ease;
}

.prompt-card-tag:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

/* 操作按钮样式 */
.prompt-card-actions {
    display: flex;
    gap: 8px;
    opacity: 0;
    transition: opacity 0.2s ease;
}

.prompt-card:hover .prompt-card-actions {
    opacity: 1;
}

.prompt-action-btn {
    padding: 8px 12px;
    border: none;
    border-radius: 8px;
    background: var(--background-primary);
    color: var(--text-normal);
    cursor: pointer;
    font-size: 13px;
    font-weight: 500;
    min-width: 36px;
    height: 36px;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.2s ease;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.prompt-action-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.prompt-action-btn.copy {
    background: linear-gradient(135deg, #10b981, #059669);
    color: white;
}

.prompt-action-btn.copy:hover {
    background: linear-gradient(135deg, #059669, #047857);
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(16, 185, 129, 0.3);
}

.prompt-action-btn.edit {
    background: linear-gradient(135deg, #f59e0b, #d97706);
    color: white;
}

.prompt-action-btn.edit:hover {
    background: linear-gradient(135deg, #d97706, #b45309);
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(245, 158, 11, 0.3);
}

.prompt-action-btn.ai-rewrite {
    background: linear-gradient(135deg, #8b5cf6, #7c3aed);
    color: white;
    position: relative;
}

.prompt-action-btn.ai-rewrite:hover {
    background: linear-gradient(135deg, #7c3aed, #6d28d9);
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(139, 92, 246, 0.3);
}

.prompt-action-btn.ai-rewrite::before {
    content: '🤖';
    margin-right: 4px;
    font-size: 12px;
}

.prompt-action-btn.danger {
    background: linear-gradient(135deg, #ef4444, #dc2626);
    color: white;
}

.prompt-action-btn.danger:hover {
    background: linear-gradient(135deg, #dc2626, #b91c1c);
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(239, 68, 68, 0.3);
}

.prompt-card-category {
    font-size: 12px;
    color: var(--text-muted);
    background: var(--background-modifier-border);
    padding: 4px 8px;
    border-radius: 8px;
    display: inline-block;
}

/* 空状态样式 */
.prompt-empty-state {
    text-align: center;
    padding: 60px 20px;
    color: var(--text-muted);
    grid-column: 1 / -1;
}

.prompt-empty-state p {
    font-size: 16px;
    margin: 0;
}

/* 模态框样式 */
.prompt-modal-buttons,
.category-modal-buttons {
    display: flex;
    gap: 12px;
    justify-content: flex-end;
    margin-top: 24px;
    padding-top: 16px;
    border-top: 1px solid var(--background-modifier-border);
}

.prompt-modal-buttons button,
.category-modal-buttons button {
    padding: 8px 16px;
    border-radius: 6px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
}

.prompt-modal-buttons button:not(.mod-cta),
.category-modal-buttons button:not(.mod-cta) {
    background: var(--background-secondary);
    color: var(--text-normal);
    border: 1px solid var(--background-modifier-border);
}

.prompt-modal-buttons button:not(.mod-cta):hover,
.category-modal-buttons button:not(.mod-cta):hover {
    background: var(--background-modifier-hover);
}

/* 设置页面样式 */
.category-management {
    margin-bottom: 20px;
}

.category-management .setting-item {
    border: 1px solid var(--background-modifier-border);
    border-radius: 8px;
    margin-bottom: 8px;
    padding: 12px;
    background: var(--background-primary);
}

/* 响应式设计 */
@media (max-width: 768px) {
    .prompt-grid {
        grid-template-columns: 1fr;
    }
    
    .prompt-library-header {
        flex-direction: column;
        gap: 12px;
        align-items: stretch;
    }
    
    .prompt-search-container {
        flex-direction: column;
    }
    
    .prompt-category-tabs {
        flex-wrap: wrap;
    }
}

/* 深色主题适配 */
.theme-dark .prompt-card {
    background: var(--background-primary-alt);
}

.theme-dark .prompt-card:hover {
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
}

/* 滚动条样式 */
.prompt-library-container::-webkit-scrollbar {
    width: 8px;
}

.prompt-library-container::-webkit-scrollbar-track {
    background: var(--background-secondary);
    border-radius: 4px;
}

.prompt-library-container::-webkit-scrollbar-thumb {
    background: var(--background-modifier-border);
    border-radius: 4px;
}

.prompt-library-container::-webkit-scrollbar-thumb:hover {
    background: var(--text-muted);
}

/* 移除动画效果 */

/* 加载状态 */
.prompt-loading {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 200px;
    color: var(--text-muted);
}

/* 工具提示样式 */
.prompt-tooltip {
    position: relative;
}

.prompt-tooltip::after {
    content: attr(data-tooltip);
    position: absolute;
    bottom: 100%;
    left: 50%;
    transform: translateX(-50%);
    background: var(--background-tooltip);
    color: var(--text-on-accent);
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 12px;
    white-space: nowrap;
    opacity: 0;
    pointer-events: none;
    transition: opacity 0.2s ease;
    z-index: 1000;
}

.prompt-tooltip:hover::after {
    opacity: 1;
}

/* 标签筛选器样式 */
.prompt-tag-filter {
    margin-bottom: 20px;
    padding: 16px;
    background: var(--background-secondary);
    border-radius: 8px;
    border: 1px solid var(--background-modifier-border);
}

.prompt-tag-filter h4 {
    margin: 0 0 12px 0;
    color: var(--text-normal);
    font-size: 14px;
    font-weight: 600;
}

.tag-filter-options {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
}

.tag-filter-btn {
    padding: 6px 12px;
    border: 1px solid var(--background-modifier-border);
    border-radius: 16px;
    background: var(--background-primary);
    color: var(--text-muted);
    font-size: 12px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
}

.tag-filter-btn:hover {
    background: var(--background-modifier-hover);
    color: var(--text-normal);
    border-color: var(--interactive-accent);
}

.tag-filter-btn.active {
    background: var(--interactive-accent);
    color: var(--text-on-accent);
    border-color: var(--interactive-accent);
}

.tag-filter-clear {
    padding: 6px 12px;
    border: 1px solid var(--text-error);
    border-radius: 16px;
    background: var(--background-primary);
    color: var(--text-error);
    font-size: 12px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
}

.tag-filter-clear:hover {
    background: var(--background-modifier-error);
    color: var(--text-on-accent);
}

/* 撤销删除按钮样式 */
.prompt-undo-main-container {
    margin-bottom: 20px;
}

.prompt-undo-toggle-container {
    display: flex;
    justify-content: flex-end;
    margin-bottom: 8px;
}

.prompt-undo-toggle-btn {
    padding: 6px 10px;
    border: 1px solid var(--background-modifier-border);
    border-radius: 6px;
    background: var(--background-primary);
    color: var(--text-normal);
    cursor: pointer;
    font-size: 16px;
    transition: all 0.2s ease;
}

.prompt-undo-toggle-btn:hover {
    background: var(--background-modifier-hover);
    border-color: var(--interactive-accent);
}

.prompt-undo-container {
    display: flex;
    gap: 8px;
    margin: 12px 0;
    padding: 12px;
    background: var(--background-secondary);
    border-radius: 8px;
    border: 1px solid var(--background-modifier-border);
    align-items: center;
}

.prompt-undo-toggle {
    padding: 4px 8px;
    border: 1px solid var(--background-modifier-border);
    border-radius: 3px;
    cursor: pointer;
    font-size: 14px;
    background: var(--background-primary);
    color: var(--text-normal);
    margin-right: 8px;
}

.prompt-undo-toggle:hover {
    background: var(--background-modifier-hover);
}

.prompt-undo-toggle.active {
    background: var(--interactive-accent);
    color: var(--text-on-accent);
    border-color: var(--interactive-accent);
}

.prompt-undo-buttons {
    display: flex;
    gap: 8px;
    align-items: center;
}

.prompt-undo-btn {
    padding: 6px 12px;
    border: 1px solid var(--interactive-accent);
    border-radius: 3px;
    cursor: pointer;
    font-size: 13px;
    background: var(--interactive-accent);
    color: var(--text-on-accent);
}

.prompt-undo-btn:hover {
    background: var(--interactive-accent-hover);
}

.prompt-undo-btn:disabled {
    background: var(--background-modifier-border);
    color: var(--text-muted);
    cursor: not-allowed;
    border-color: var(--background-modifier-border);
}

.prompt-clear-deleted-btn {
    padding: 6px 12px;
    border: 1px solid var(--text-error);
    border-radius: 3px;
    cursor: pointer;
    font-size: 13px;
    background: var(--text-error);
    color: white;
    transition: all 0.2s ease;
}

.prompt-clear-deleted-btn:hover {
    opacity: 0.8;
}

.prompt-clear-deleted-btn:disabled,
.prompt-clear-deleted-btn.disabled {
    background: var(--background-modifier-border);
    color: var(--text-muted);
    opacity: 1;
    border-color: var(--background-modifier-border);
    cursor: not-allowed;
}

.prompt-clear-btn {
    background: var(--color-red);
    color: white;
    border-color: var(--color-red);
}

.prompt-clear-btn:hover {
    opacity: 0.8;
}

.prompt-clear-btn:disabled {
    background: var(--background-modifier-border);
    color: var(--text-muted);
    opacity: 1;
    border-color: var(--background-modifier-border);
}

/* 分类管理按钮样式 */
.prompt-tab.add-category {
    border: 1px dashed var(--interactive-accent);
    background: transparent;
    color: var(--interactive-accent);
}

.prompt-tab.add-category:hover {
    background: var(--interactive-accent);
    color: var(--text-on-accent);
    border-style: solid;
}

.prompt-tab.edit-category {
    background: var(--background-modifier-border);
    color: var(--text-muted);
    border: 1px solid var(--background-modifier-border);
}

.prompt-tab.edit-category:hover {
    background: var(--background-modifier-hover);
    color: var(--text-normal);
    border-color: var(--interactive-accent);
}

/* 添加分类按钮样式 */
.prompt-add-category-btn {
    padding: 6px 12px;
    border: 1px dashed var(--interactive-accent);
    border-radius: 16px;
    background: transparent;
    color: var(--interactive-accent);
    cursor: pointer;
    font-size: 13px;
    margin-left: 8px;
    display: flex;
    align-items: center;
    gap: 4px;
}

.prompt-add-category-btn:hover {
    background: var(--interactive-accent);
    color: var(--text-on-accent);
    border-style: solid;
}

/* 移除分类标签双击提示 */

/* AI模态框样式 */
.ai-generate-modal,
.ai-rewrite-modal {
    max-width: 1200px;
    width: 90vw;
    max-height: 90vh;
    min-width: 600px;
    min-height: 500px;
    background: var(--background-primary);
    border: 1px solid var(--background-modifier-border);
    border-radius: 12px;
    box-shadow: 0 10px 40px rgba(0, 0, 0, 0.2);
    overflow: hidden;
}

.ai-generate-modal .modal-content,
.ai-rewrite-modal .modal-content {
    padding: 32px;
    max-height: calc(90vh - 80px);
    overflow-y: auto;
    display: flex;
    flex-direction: column;
    gap: 24px;
}

/* 模态框标题 */
.ai-generate-modal h2,
.ai-rewrite-modal h2 {
    margin: 0 0 24px 0;
    color: var(--text-normal);
    font-size: 24px;
    font-weight: 600;
    text-align: center;
    border-bottom: 2px solid var(--interactive-accent);
    padding-bottom: 12px;
}

/* 设置项样式优化 */
.ai-generate-modal .setting-item,
.ai-rewrite-modal .setting-item {
    border: none;
    padding: 16px 0;
    border-bottom: 1px solid var(--background-modifier-border-hover);
}

.ai-generate-modal .setting-item:last-child,
.ai-rewrite-modal .setting-item:last-child {
    border-bottom: none;
}

.ai-generate-modal .setting-item-info,
.ai-rewrite-modal .setting-item-info {
    margin-bottom: 8px;
}

.ai-generate-modal .setting-item-name,
.ai-rewrite-modal .setting-item-name {
    font-weight: 500;
    color: var(--text-normal);
    font-size: 15px;
}

.ai-generate-modal .setting-item-description,
.ai-rewrite-modal .setting-item-description {
    color: var(--text-muted);
    font-size: 13px;
    margin-top: 4px;
}

/* 文本区域样式 */
.ai-generate-modal textarea,
.ai-rewrite-modal textarea {
    width: 100%;
    min-height: 120px;
    padding: 12px;
    border: 1px solid var(--background-modifier-border);
    border-radius: 8px;
    background: var(--background-primary);
    color: var(--text-normal);
    font-family: var(--font-interface);
    font-size: 14px;
    line-height: 1.5;
    resize: vertical;
    transition: border-color 0.2s ease;
}

.ai-generate-modal textarea:focus,
.ai-rewrite-modal textarea:focus {
    outline: none;
    border-color: var(--interactive-accent);
    box-shadow: 0 0 0 2px var(--interactive-accent-hover);
}

/* 下拉选择框样式 */
.ai-generate-modal select,
.ai-rewrite-modal select {
    width: 100%;
    padding: 10px 12px;
    border: 1px solid var(--background-modifier-border);
    border-radius: 6px;
    background: var(--background-primary);
    color: var(--text-normal);
    font-size: 14px;
}

.requirement-section {
    margin-bottom: 24px;
}

.requirement-section h3 {
    margin: 0 0 16px 0;
    color: var(--text-normal);
    font-size: 16px;
    font-weight: 500;
}

.original-prompt-section {
    margin-bottom: 24px;
    padding: 16px;
    background: var(--background-secondary);
    border-radius: 8px;
    border: 1px solid var(--background-modifier-border);
}

.original-prompt-section h3 {
    margin: 0 0 12px 0;
    color: var(--text-normal);
    font-size: 16px;
    font-weight: 500;
}

.original-prompt-info {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.prompt-info-item {
    color: var(--text-muted);
    font-size: 14px;
}

.prompt-info-label {
    font-weight: 500;
    color: var(--text-normal);
    margin-bottom: 4px;
}

.prompt-content-preview {
    margin-top: 8px;
}

.prompt-content-text {
    background: var(--background-primary);
    padding: 12px;
    border-radius: 6px;
    border: 1px solid var(--background-modifier-border);
    font-family: var(--font-monospace);
    font-size: 13px;
    line-height: 1.4;
    color: var(--text-muted);
    max-height: 120px;
    overflow-y: auto;
}

/* 按钮样式优化 */
.ai-generate-modal button,
.ai-rewrite-modal button {
    padding: 12px 24px;
    border: 1px solid var(--background-modifier-border);
    border-radius: 8px;
    background: var(--background-primary);
    color: var(--text-normal);
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
    min-height: 40px;
}

.ai-generate-modal button:hover,
.ai-rewrite-modal button:hover {
    background: var(--background-modifier-hover);
    border-color: var(--interactive-accent);
    transform: translateY(-1px);
}

/* 主要操作按钮 */
.ai-generate-btn {
    background: var(--interactive-accent) !important;
    color: var(--text-on-accent) !important;
    border-color: var(--interactive-accent) !important;
    width: 100%;
    margin: 16px 0;
    padding: 14px 24px;
    font-size: 15px;
    font-weight: 600;
    border-radius: 8px;
}

.ai-generate-btn:hover {
    background: var(--interactive-accent-hover) !important;
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.ai-generate-btn:disabled {
    background: var(--background-modifier-border) !important;
    color: var(--text-muted) !important;
    cursor: not-allowed;
    transform: none;
    box-shadow: none;
}

/* 结果显示区域优化 */
.ai-result-container {
    margin-top: 24px;
    border: 1px solid var(--background-modifier-border);
    border-radius: 12px;
    background: var(--background-secondary);
    overflow: hidden;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.loading-container {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 50px;
    background: var(--background-secondary);
    border-radius: 8px;
    border: 1px solid var(--background-modifier-border);
}

.loading-text {
    color: var(--text-muted);
    font-size: 15px;
    font-weight: 500;
    animation: pulse 2s infinite;
}

.loading-spinner {
    width: 28px;
    height: 28px;
    border: 3px solid var(--background-modifier-border);
    border-top: 3px solid var(--interactive-accent);
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-right: 12px;
}

@keyframes pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.5; }
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.result-section {
    background: var(--background-secondary);
    border-radius: 8px;
    border: 1px solid var(--background-modifier-border);
    padding: 24px;
}

.result-section h3 {
    margin: 0 0 20px 0;
    color: var(--text-normal);
    font-size: 18px;
    font-weight: 600;
    border-bottom: 2px solid var(--interactive-accent);
    padding-bottom: 8px;
}

.generated-prompt-display,
.rewritten-prompt-display,
.original-prompt-display {
    background: var(--background-primary);
    border-radius: 8px;
    border: 1px solid var(--background-modifier-border);
    padding: 20px;
    margin-bottom: 20px;
    font-family: var(--font-monospace);
    font-size: 13px;
    line-height: 1.6;
    color: var(--text-normal);
    white-space: pre-wrap;
    word-wrap: break-word;
    max-height: 300px;
    overflow-y: auto;
    box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.1);
}

.result-item {
    margin-bottom: 16px;
    color: var(--text-normal);
    font-size: 14px;
    line-height: 1.5;
}

.result-label {
    font-weight: 600;
    color: var(--text-normal);
    margin-bottom: 8px;
    font-size: 15px;
}

.result-content {
    margin-top: 12px;
}

.result-content-text {
    background: var(--background-primary);
    padding: 16px;
    border-radius: 8px;
    border: 1px solid var(--background-modifier-border);
    font-family: var(--font-monospace);
    font-size: 13px;
    line-height: 1.6;
    color: var(--text-normal);
    white-space: pre-wrap;
    max-height: 300px;
    overflow-y: auto;
    box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.1);
}

.result-actions {
    display: flex;
    gap: 12px;
    flex-wrap: wrap;
    margin-top: 16px;
}

/* 模态框底部按钮容器优化 */
.modal-button-container {
    display: flex;
    justify-content: flex-end;
    gap: 12px;
    margin-top: auto;
    padding-top: 24px;
    border-top: 1px solid var(--background-modifier-border);
}

.modal-button {
    padding: 10px 20px;
    border: 1px solid var(--background-modifier-border);
    border-radius: 6px;
    background: var(--background-primary);
    color: var(--text-normal);
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
    min-width: 80px;
}

.modal-button:hover {
    background: var(--background-modifier-hover);
    border-color: var(--interactive-accent);
    transform: translateY(-1px);
}

.modal-button.mod-cta {
    background: var(--interactive-accent) !important;
    color: var(--text-on-accent) !important;
    border-color: var(--interactive-accent) !important;
}

.modal-button.mod-cta:hover {
    background: var(--interactive-accent-hover) !important;
    transform: translateY(-1px);
}

/* 错误消息优化 */
.error-message {
    color: var(--text-error);
    background: var(--background-modifier-error);
    padding: 16px 20px;
    border-radius: 8px;
    border: 1px solid var(--text-error);
    border-left: 4px solid var(--text-error);
    font-size: 14px;
    font-weight: 500;
    text-align: left;
    margin: 16px 0;
    box-shadow: 0 2px 8px rgba(239, 68, 68, 0.1);
}