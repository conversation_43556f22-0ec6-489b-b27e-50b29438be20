# Prompt Library Plugin for Obsidian

一个功能强大的 Obsidian 提示词库插件，帮助您管理和使用各种 AI 提示词。

## 功能特性

### 🎯 核心功能
- **提示词管理**: 创建、编辑、删除和分类管理提示词
- **智能搜索**: 支持按标题、描述、标签进行快速搜索
- **分类系统**: 灵活的分类管理，支持自定义分类
- **标签系统**: 多标签支持，便于精确查找
- **一键复制**: 快速复制提示词内容到剪贴板

### 🎨 界面特性
- **现代化UI**: 美观的卡片式布局
- **响应式设计**: 适配不同屏幕尺寸
- **深色主题**: 完美适配 Obsidian 的深色和浅色主题
- **流畅动画**: 丰富的交互动画效果

### 📱 用户体验
- **侧边栏集成**: 可在侧边栏中快速访问
- **快捷命令**: 支持命令面板快速打开
- **实时预览**: 即时查看提示词效果
- **批量操作**: 支持批量管理提示词

## 安装方法

### 手动安装
1. 下载插件文件
2. 将插件文件夹复制到 Obsidian vault 的 `.obsidian/plugins/` 目录下
3. 在 Obsidian 设置中启用插件

### 开发者安装
1. 克隆或下载此仓库
2. 在插件目录中运行 `npm install`
3. 运行 `npm run build` 构建插件
4. 将构建后的文件复制到 Obsidian 插件目录

## 使用指南

### 基本操作

#### 打开提示词库
- 点击侧边栏的图书馆图标
- 使用命令面板搜索"打开提示词库"
- 使用快捷键（可在设置中自定义）

#### 添加新提示词
1. 点击"添加新卡片"按钮
2. 填写提示词信息：
   - **标题**: 提示词的名称
   - **描述**: 简短的功能描述
   - **分类**: 选择合适的分类
   - **标签**: 添加相关标签（用逗号分隔）
   - **内容**: 完整的提示词文本
3. 点击"保存"完成添加

#### 使用提示词
1. 在提示词库中找到需要的提示词
2. 点击"复制"按钮
3. 提示词内容将复制到剪贴板
4. 在需要的地方粘贴使用

#### 编辑提示词
1. 在提示词卡片上点击"编辑"按钮
2. 修改相关信息
3. 点击"保存"保存更改

#### 删除提示词
1. 在提示词卡片上点击"删除"按钮
2. 确认删除操作

### 高级功能

#### 分类管理
1. 进入插件设置页面
2. 在"分类管理"部分添加、编辑或删除分类
3. 设置默认分类

#### 搜索和筛选
- **文本搜索**: 在搜索框中输入关键词
- **分类筛选**: 点击分类标签进行筛选
- **组合筛选**: 同时使用搜索和分类筛选

## 默认内容

插件预置了一些常用的提示词示例：

### 翻译类
- **中英翻译**: 专业的中英文互译助手

### 编程类
- **代码优化**: 代码质量改进和最佳实践建议

### 更多分类
- 写作辅助
- 数据分析
- 创意思维

## 配置选项

### 基本设置
- **默认分类**: 新建提示词时的默认分类
- **界面主题**: 自动适配 Obsidian 主题

### 分类管理
- 添加自定义分类
- 编辑分类名称
- 删除不需要的分类

## 数据存储

- 所有提示词数据存储在 Obsidian 的插件数据文件中
- 数据会随 Obsidian 的同步功能自动同步
- 支持导入导出功能（计划中）

## 技术特性

### 架构设计
- **TypeScript**: 使用 TypeScript 开发，类型安全
- **模块化**: 清晰的代码结构和模块划分
- **响应式**: 支持不同屏幕尺寸的自适应布局

### 性能优化
- **懒加载**: 按需加载内容
- **虚拟滚动**: 处理大量数据时的性能优化
- **缓存机制**: 智能缓存提高响应速度

## 开发计划

### 即将推出
- [ ] 导入导出功能
- [ ] 提示词模板系统
- [ ] 批量操作功能
- [ ] 快捷键自定义

### 长期规划
- [ ] 云端同步支持
- [ ] 提示词分享社区
- [ ] AI 辅助提示词生成
- [ ] 多语言支持

## 故障排除

### 常见问题

**Q: 插件无法加载？**
A: 请检查插件文件是否完整，确保在设置中启用了插件。

**Q: 提示词无法保存？**
A: 请检查是否填写了所有必填字段，确保有足够的存储空间。

**Q: 搜索功能不工作？**
A: 请尝试重新加载插件或重启 Obsidian。

### 获取帮助
- 查看 GitHub Issues
- 联系开发者
- 参与社区讨论

## 贡献指南

欢迎贡献代码、报告问题或提出建议！

### 开发环境
1. Fork 此仓库
2. 创建功能分支
3. 提交更改
4. 创建 Pull Request

### 代码规范
- 使用 TypeScript
- 遵循 ESLint 规则
- 添加适当的注释
- 编写测试用例

## 许可证

MIT License - 详见 LICENSE 文件

## 更新日志

### v1.0.0 (2024-01-01)
- 🎉 初始版本发布
- ✨ 基础提示词管理功能
- 🎨 现代化用户界面
- 🔍 搜索和分类功能
- 📱 响应式设计

---

**感谢使用 Prompt Library Plugin！** 如果您觉得这个插件有用，请给我们一个 ⭐️